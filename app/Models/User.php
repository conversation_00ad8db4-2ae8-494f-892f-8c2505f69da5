<?php

namespace App\Models;

use Exception;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use <PERSON><PERSON>\JWTAuth\Contracts\JWTSubject;
use Spatie\Permission\Traits\HasRoles;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class User extends Authenticatable implements JWTSubject
{
    use HasFactory, Notifiable;
    use HasRoles;

    public $timestamps = true;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'email',
        'password',
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
    ];

    protected $appends = ['name_code', 'contract_expired'];

    const SUPER_PASS = 'vietec@2023';
    const COMPANY_VIETEC = 1;
    const COMPANY_GOKIDS = 2;
    const COMPANY_METAKIDS = 3;
    const COMPANY_KIDSENGLISH = 4;
    const COMPANY_LANGUAGEHUB = 5;
    const COMPANY = [
        self::COMPANY_VIETEC => 'Vietec',
        self::COMPANY_GOKIDS => 'Gokids',
        self::COMPANY_METAKIDS => 'MetaKids',
        self::COMPANY_KIDSENGLISH => 'KidsEnglish',
        self::COMPANY_LANGUAGEHUB => 'LanguageHub',
    ];

    const DEVICE_SESSION_OS_MOBILE = 'Mobile';
    const DEVICE_SESSION_OS_WEB = 'Web';

    /**
     * Get the identifier that will be stored in the subject claim of the JWT.
     *
     * @return mixed
     */
    public function getJWTIdentifier()
    {
        return $this->getKey();
    }

    /**
     * Return a key value array, containing any custom claims to be added to the JWT.
     *
     * @return array
     */
    public function getJWTCustomClaims()
    {
        return [];
    }

    public static function store($request)
    {
        DB::beginTransaction();
        try {
            $user = new self;
            $user->staff_code = $request->staff_code;
            $user->name = $request->name;
            $user->gender = $request->gender;
            if (!empty($request->birth_of_date)) {
                $user->birth_of_date = $request->birth_of_date;
            }

            $user->id_number = $request->id_number;
            $user->place_id_number = $request->place_id_number;
            if (!empty($request->date_id_number)) {
                $user->date_id_number = $request->date_id_number;
            }
            if ($request->degree_id) {
                $user->degree_id = $request->degree_id;
            }
            $user->original_province_id = $request->original_province_id;
            $user->original_district_id = $request->original_district_id;
            $user->original_ward_id = $request->original_ward_id;
            $user->original_address = $request->original_address;
            $user->province_id = $request->province_id;
            $user->district_id = $request->district_id;
            $user->ward_id = $request->ward_id;
            $user->address = $request->address;
            $user->current_province_id = $request->current_province_id;
            $user->current_district_id = $request->current_district_id;
            $user->current_ward_id = $request->current_ward_id;
            $user->current_address = $request->current_address;
            $user->necessary_info = $request->necessary_info;
            $user->necessary_address = $request->necessary_address;
            $user->phone = $request->phone;
            $user->phone_vietec = $request->phone_vietec;
            $user->email = $request->email;
            $user->email_individual = $request->email_individual;
            $user->skype_id = $request->skype_id;
            $user->bank_number = $request->bank_number;
            $user->bank_name = $request->bank_name;
            $user->social_insurance = $request->social_insurance;
            $user->personal_income_tax = $request->personal_income_tax;
            $user->has_profile = $request->has_profile;
            $user->has_birth_certificate = $request->has_birth_certificate;
            $user->has_id_number = $request->has_id_number;
            $user->has_health_certificate = $request->has_health_certificate;
            $user->has_family_register = $request->has_family_register;
            $user->has_degree = $request->has_degree;
            $user->start_date_of_work = $request->start_date_of_work;
            $user->end_date_of_work = $request->end_date_of_work;
            $user->start_date_of_probationary = $request->start_date_of_probationary;
            $user->end_date_of_probationary = $request->end_date_of_probationary;
            $user->full_time_sheet = $request->full_time_sheet;
            $user->password = bcrypt('vietec@2023');
            $user->save();

            //save thong tin nguoi than
            $user_family = [];
            foreach ($request->user_family as $e) {
                $dt = [
                    'user_id' => $user->id,
                    'name' => $e['name'],
                    'relationship' => $e['relationship'],
                    'gender' => $e['gender'],
                    'dob' => $e['dob']
                ];
                array_push($user_family, $dt);
            }
            if ($user_family) {
                DB::table('user_family')->insert($user_family);
            }
            $roleIds = [];
            // save main position
            UserWorking::where('user_id', $user->id)
                ->where('status', true)
                ->update(['status' => false]);

            $mainPosition = UserWorking::create([
                'user_id' => $user->id,
                'department_id' => $request->department_id,
                'position_id' => $request->position_id,
                'dependent_position_id' => $request->dependent_position_id,
                'role_id' => $request->role_id,
                'is_sub_position' => false,
                'employment_contract_id' => $request->employment_contract_id,
                'employment_contract_number' => $request->employment_contract_number,
                'note' => $request->note,
                'start_date' => $request->start_date,
                'end_date' => $request->end_date,
                'company' => $request->company,
            ]);

            if ($request->role_id) {
                $roleIds = [$request->role_id];
            }

            // sub position
            $userWorkings = [];
            $leaderIds = Position::year(date('Y'))
                ->where('is_leader', true)
                ->where('department_id', $request->department_id)
                ->pluck('id')
                ->toArray();
            $usedPositionIds = UserWorking::allActive()
                ->where('department_id', $request->department_id)
                ->distinct()
                ->pluck('position_id')
                ->toArray();
            foreach ($request->positions as $position) {
                if (
                    in_array($position['position_id'], $leaderIds)
                    && in_array($position['position_id'], $usedPositionIds)
                ) {
                    continue;
                }

                array_push($userWorkings, [
                    'user_id' => $user->id,
                    'parent_id' => $mainPosition->id,
                    'department_id' => $position['department_id'],
                    'position_id' => $position['position_id'],
                    'dependent_position_id' => $position['dependent_position_id'],
                    'role_id' => $position['role_id'],
                    'is_sub_position' => true,
                    'employment_contract_id' => $request->employment_contract_id,
                    'employment_contract_number' => $request->employment_contract_number,
                    'note' => $request->note,
                    'start_date' => $request->start_date,
                    'end_date' => $request->end_date
                ]);

                if ($position['role_id']) {
                    if (!in_array($position['role_id'], $roleIds)) {
                        $roleIds[] = $position['role_id'];
                    }
                }
            }

            if ($roleIds) {
                $user->assignRole($roleIds);
            }

            if ($userWorkings) {
                DB::table('user_workings')->insert($userWorkings);
            }

            DB::commit();
            return [
                'user' => !empty($user) ? $user : [],
                'user_working' => !empty($user_working) ? $user_working : '',
            ];
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public static function edit($request)
    {
        $user = self::find($request->id);
        if (!$user) {
            return false;
        }
        $user->staff_code = $request->staff_code;
        $user->name = $request->name;
        $user->gender = $request->gender;
        if (!empty($request->birth_of_date)) {
            $user->birth_of_date = $request->birth_of_date;
        }
        if ($request->degree_id) {
            $user->degree_id = $request->degree_id;
        }
        $user->id_number = $request->id_number;
        $user->place_id_number = $request->place_id_number;
        if (!empty($request->date_id_number)) {
            $user->date_id_number = $request->date_id_number;
        }

        $user->original_province_id = $request->original_province_id;
        $user->original_district_id = $request->original_district_id;
        $user->original_ward_id = $request->original_ward_id;
        $user->original_address = $request->original_address;
        $user->province_id = $request->province_id;
        $user->district_id = $request->district_id;
        $user->ward_id = $request->ward_id;
        $user->address = $request->address;
        $user->current_province_id = $request->current_province_id;
        $user->current_district_id = $request->current_district_id;
        $user->current_ward_id = $request->current_ward_id;
        $user->current_address = $request->current_address;
        $user->necessary_info = $request->necessary_info;
        $user->necessary_address = $request->necessary_address;
        $user->phone = $request->phone;
        $user->phone_vietec = $request->phone_vietec;
        $user->email_individual = $request->email_individual;
        $user->skype_id = $request->skype_id;
        $user->bank_number = $request->bank_number;
        $user->bank_name = $request->bank_name;
        $user->social_insurance = $request->social_insurance;
        $user->personal_income_tax = $request->personal_income_tax;
        $user->has_profile = $request->has_profile;
        $user->has_birth_certificate = $request->has_birth_certificate;
        $user->has_id_number = $request->has_id_number;
        $user->has_health_certificate = $request->has_health_certificate;
        $user->has_family_register = $request->has_family_register;
        $user->has_degree = $request->has_degree;
        $user->start_date_of_work = $request->start_date_of_work;
        $user->end_date_of_work = $request->end_date_of_work;
        $user->start_date_of_probationary = $request->start_date_of_probationary;
        $user->end_date_of_probationary = $request->end_date_of_probationary;
        $user->full_time_sheet = $request->full_time_sheet;
        $user->save();

        //save thong tin nguoi than
        foreach ($request->user_family as $e) {
            $dt = [
                'user_id' => $user->id,
                'name' => $e['name'],
                'relationship' => $e['relationship'],
                'gender' => $e['gender'],
                'dob' => $e['dob']
            ];
            if ($e['id']) {
                DB::table('user_family')->update($dt);
            } else {
                DB::table('user_family')->create($dt);
            }
        }
        return $user;
    }

    /**
     * Danh sách nhân sự
     */
    public static function list(Request $request)
    {
        $query = User::with([
            'user.department',
            'user.position',
            'user.employmentContract',
            'degree',
            'model_user.role'
        ])
            ->notAdmin()
            ->when($request->department_id, function ($query, $departmentId) {
                $query->whereHas('user', function ($subQuery) use ($departmentId) {
                    return $subQuery->where('department_id',  $departmentId);
                });
            })
            ->when($request->rank_code, function ($query, $rankCode) {
                $query->whereHas('user.position', function ($subQuery) use ($rankCode) {
                    return $subQuery->where('code',  $rankCode);
                });
            })
            ->when($request->company, function ($query, $company) {
                $query->whereHas('user', function ($subQuery) use ($company) {
                    return $subQuery->where('company',  $company);
                });
            })
            ->when($request->employment_contract, function ($query, $employmentContract) {
                $query->whereHas('user', function ($subQuery) use ($employmentContract) {
                    return $subQuery->where('employment_contract_id',  $employmentContract);
                });
            })
            ->when($request->status == 1, function ($query) {
                $query->where('status', 1);
            })
            ->when($request->status == 0, function ($query) {
                $query->where('status', 0);
            })
            ->when($request->gender, function ($query, $gender) {
                $query->where('gender', $gender);
            })
            ->when($request->keyword, function ($query, $keyword) {
                $query->where(function ($q) use ($keyword) {
                    $q->where('name', 'like', '%' . $keyword . '%')
                        ->orWhere('staff_code', 'like', '%' . $keyword . '%')
                        ->orWhere('phone', 'like', '%' . $keyword . '%')
                        ->orWhere('skype_id', 'like', '%' . $keyword . '%')
                        ->orWhere('email', 'like', '%' . $keyword . '%')
                        ->orWhere('birth_of_date', 'like', '%' . $keyword . '%');
                });
            });

        $data = $query->orderBy('id')->paginate(20);
        return $data;
    }

    public static function show($id)
    {
        $user = self::with(
            'degree',
            'avatar',
            'original_province',
            'original_district',
            'original_ward',
            'province',
            'district',
            'ward',
            'user_workings.position',
            'current_province',
            'current_district',
            'current_ward',
            'user_family',
            'kpi_summary'
        )
            ->find($id);
        $user_working = UserWorking::with('department', 'position', 'employmentContract', 'leader', 'role', 'subUserWorkings.position')
            ->isMainPosition()
            ->where('user_id', $id)
            ->orderBy('employment_contract_id', 'asc')
            ->get();

        $original_districts = empty($user->original_province_id) ? [] : District::where('province_id', $user->original_province_id)->get();
        $original_wards = empty($user->original_district_id) ? [] : Ward::where('district_id', $user->original_district_id)->get();

        $districts = empty($user->province_id) ? [] : District::where('province_id', $user->province_id)->get();
        $wards = empty($user->district_id) ? [] : Ward::where('district_id', $user->district_id)->get();

        $current_districts = empty($user->current_province_id) ? [] : District::where('province_id', $user->current_province_id)->get();
        $current_wards = empty($user->current_district_id) ? [] : Ward::where('district_id', $user->current_district_id)->get();

        return [
            'user' => $user,
            'user_working' => $user_working,
            'original_districts' => $original_districts,
            'original_wards' => $original_wards,
            'districts' => $districts,
            'wards' => $wards,
            'current_districts' => $current_districts,
            'current_wards' => $current_wards,
        ];
    }

    public static function off($request)
    {
        DB::beginTransaction();
        try {
            $user = self::find($request->id);
            if (!$user) {
                return false;
            }
            $user->status = 0;
            $user->end_date_of_work = $request->end_date;
            $user->save();

            $user_working = UserWorking::where('user_id', $request->id)->where('status', 1)->first();
            if ($user_working) {
                $user_working->end_date = $request->end_date;
                $user_working->reason_off = $request->reason_off;
                $user_working->status = 0;
                $user_working->save();

                # off các chức vụ kiêm nhiệm
                UserWorking::where('user_id', $user->id)
                    ->where('parent_id', $user_working->id)
                    ->update([
                        'status' => false,
                        'end_date' => $request->end_date
                    ]);
            }

            DB::commit();
            return true;
        } catch (Exception $e) {
            DB::rollBack();
            Log::error('UserController off: ' . $e->getMessage());
            return false;
        }
    }

    public function user()
    {
        return $this->hasOne(UserWorking::class, 'user_id', 'id')
            ->where('is_sub_position', false)
            ->where('status', 1)
            ->where(function ($subQuery) {
                $subQuery->whereNull('end_date')
                    ->orWhere('end_date', '>=', date('Y-m-d'));
            });
    }

    public function endOfWorking()
    {
        return $this->hasOne(UserWorking::class, 'user_id', 'id')->where('is_sub_position', false)->latest();
    }

    public function degree()
    {
        return $this->belongsTo(Degree::class, 'degree_id', 'id');
    }
    public function avatar()
    {
        return $this->hasOne(File::class, 'user_id', 'id');
    }
    public function contracts()
    {
        return $this->hasMany(Contract::class, 'sale_id', 'id');
    }
    public function user_workings()
    {
        return $this->hasMany(UserWorking::class, 'user_id', 'id')->where('status', 1);
    }
    public function original_province()
    {
        return $this->belongsTo(Province::class, 'original_province_id', 'province_id');
    }
    public function original_district()
    {
        return $this->belongsTo(District::class, 'original_district_id', 'district_id');
    }
    public function original_ward()
    {
        return $this->belongsTo(Ward::class, 'original_ward_id', 'ward_id');
    }
    public function province()
    {
        return $this->belongsTo(Province::class, 'province_id', 'province_id');
    }
    public function district()
    {
        return $this->belongsTo(District::class, 'district_id', 'district_id');
    }
    public function ward()
    {
        return $this->belongsTo(Ward::class, 'ward_id', 'ward_id');
    }
    public function current_province()
    {
        return $this->belongsTo(Province::class, 'current_province_id', 'province_id');
    }
    public function current_district()
    {
        return $this->belongsTo(District::class, 'current_district_id', 'district_id');
    }
    public function current_ward()
    {
        return $this->belongsTo(Ward::class, 'current_ward_id', 'ward_id');
    }
    public function model_user()
    {
        return $this->hasOne(ModelHasRoles::class, 'model_id', 'id');
    }

    public static function synRole($user_id, $role_id)
    {
        $user = self::find($user_id);
        $user->syncRoles($role_id);
    }

    public function scopeMale($query)
    {
        return $query->where('gender', 1);
    }

    public function scopeFemale($query)
    {
        return $query->where('gender', 2);
    }

    public function scopeNotAdmin($query)
    {
        return $query->where('id', '<>', 1);
    }

    public function scopeNotMe($query, $user_id)
    {
        return $query->where('id', '<>', $user_id);
    }

    public function hanet()
    {
        return $this->belongsTo(HanetCamUser::class, 'staff_code', 'aliasID');
    }

    public function kpi_summary()
    {
        return $this->hasMany(KPISummaryUpgrade::class, 'user_id', 'id')->orderBy('to_date', 'DESC');
    }

    public function user_family()
    {
        return $this->hasMany(UserFamily::class, 'user_id', 'id');
    }

    public function absence_years()
    {
        return $this->hasOne(AbsenceYear::class, 'user_id', 'id');
    }

    public function userDevice()
    {
        return $this->hasMany(DeviceSession::class, 'user_id', 'id');
    }

    public function routeNotificationForFcm()
    {
        return $this->userDevice->pluck('firebase_token')->toArray();
    }

    public function getNameCodeAttribute()
    {
        return $this->name . '-' . $this->staff_code;
    }

    /**
     * Lấy hợp đồng lao động của user
     */
    public function workings()
    {
        return $this->hasMany(UserWorking::class, 'user_id', 'id')->notAdmin()->allActive();
    }

    /**
     *  Lấy trạng thái hợp đồng của user
     */
    public function getContractExpiredAttribute()
    {
        if ($this->workings->count() == 0) {
            return false;
        }
        return $this->workings->first()->is_expired_contract;
    }

    public function in_outs()
    {
        return $this->hasMany(PersonInOut::class, 'aliasID', 'staff_code');
    }
}
