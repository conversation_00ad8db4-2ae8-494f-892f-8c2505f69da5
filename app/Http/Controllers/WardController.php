<?php

namespace App\Http\Controllers;

use App\Models\Ward;
use Illuminate\Http\Request;
use App\Models\Response;

class WardController extends Controller
{
    public function index(Request $request)
    {
        $district_id = empty($request->district_id) ? 0 : $request->district_id;
        $data = Ward::where('district_id', $district_id)->get();
        return Response::formatResponse(config('apicode.SUCCESS'), $data);
    }
}
