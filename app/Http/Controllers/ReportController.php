<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\ProvinceBusinessMarket;
use Illuminate\Http\Request;
use App\Models\Report;
use Exception;
use Illuminate\Support\Facades\Log;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Style;

class ReportController extends Controller
{
    /**
     * Lấy Báo cáo doanh số tuần - theo tỉnh thành
     */
    public function report_sale_week(Request $request)
    {
        try {
            $p = Report::params($request);
            $data = Report::report_sale_week($p);
            return response()->json($data);
        } catch (Exception $e) {
            Log::error("ReportController report_sale_week: " . $e->getMessage());
            return response()->json([], 500);
        }
    }

    /**
     * Lấy Báo cáo doanh số theo sản phẩm
     */
    public function report_sale_product(Request $request)
    {
        try {
            $p = Report::params($request);
            $data = Report::report_sale_product($p);
            return response()->json($data);
        } catch (Exception $e) {
            Log::error("ReportController report_sale_product: " . $e->getMessage());
            return response()->json([], 500);
        }
    }

    /**
     * Báo cáo công nợ
     */
    public function report_sale_debt(Request $request)
    {
        try {
            $p = Report::params($request);
            $data = Report::report_sale_debt($p);
            return response()->json($data);
        } catch (Exception $e) {
            Log::error("ReportController report_sale_debt: " . $e->getMessage());
            return response()->json([], 500);
        }
    }

    /**
     * Danh sách các hợp đồng sắp hết hạn
     */
    public function report_sale_expire(Request $request)
    {
        try {
            $p = Report::params($request);
            $data = Report::report_sale_expire($p);
            return response()->json($data);
        } catch (Exception $e) {
            Log::error("ReportController report_sale_expire: " . $e->getMessage());
            return response()->json([], 500);
        }
    }

    /**
     * Lấy Báo cáo cuộc gọi khách hàng
     */
    public function report_call_center(Request $request)
    {
        try {
            $p = Report::params($request);
            $data = Report::report_call_center($p);
            return response()->json($data);
        } catch (Exception $e) {
            Log::error("ReportController report_call_center: " . $e->getMessage());
            return response()->json([], 500);
        }
    }
    /**
     * Lấy Báo cáodoanh thu 3 năm gần nhất
     */
    public function report_sales(Request $request)
    {
        try {
            //doanh thu 3 nam gan nhat
            $data = Report::report_three_years($request);
            return response()->json($data);
        } catch (Exception $e) {
            Log::error("ReportController report_sales: " . $e->getMessage());
            return response()->json([], 500);
        }
    }
    /**
     * Lấy Báo cáo doanh thu từng team trong năm hiện tại
     */
    public function report_team_sales(Request $request)
    {
        try {
            $data = Report::report_current_years($request);
            return response()->json($data);
        } catch (Exception $e) {
            Log::error("ReportController report_team_sales: " . $e->getMessage());
            return response()->json([], 500);
        }
    }
    /**
     * Lấy Báo cáo doanh thu giữa tình hình thực tế và mục tiêu của từng team kinh doanh trong năm hiện tại
     */
    public function report_actual_sales_and_goal_sales(Request $request)
    {
        try {
            $data = Report::report_actual_sales_and_goal_sales($request);
            return response()->json($data);
        } catch (Exception $e) {
            Log::error("ReportController report_actual_sales_and_goal_sales: " . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function reportByProduct(Request $request)
    {
        try {
            $data = Report::reportByProduct($request);

            $contract_value_total = $debt_total = $received_money_total = 0;
            foreach ($data as $item) {
                $contract_value_total += $item->contract_value_total;
                $debt_total += $item->debt_total;
                $received_money_total += $item->received_money_total;
            }
            $rowTotal = [
                'code' => 'Tổng',
                'contract_value_total' => $contract_value_total,
                'debt_total' => $debt_total,
                'received_money_total' => $received_money_total,
            ];
            return response()->json([...$data, $rowTotal]);
        } catch (Exception $e) {
            Log::error("ReportController reportByProduct: " . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function exportByProduct(Request $request)
    {
        try {
            $inputFileName = 'templates/report_by_product.xlsx';
            $fileType = IOFactory::identify($inputFileName);
            $objReader = IOFactory::createReader($fileType);
            $objPHPExcel = $objReader->load($inputFileName);

            $sheetProduct = $objPHPExcel->setActiveSheetIndex(0);
            $row = 5;

            $contract_value_total_arr = [];
            $received_money_total_arr = [];
            $debt_total_arr = [];
            $products = Report::reportByProduct($request);
            if ($request->date_range) {
                $from_date = date('d-m-Y', strtotime(explode(' ', trim($request->date_range))[0]));
                $to_date = date('d-m-Y', strtotime(explode(' ', trim($request->date_range))[2]));
                $sheetProduct->setCellValue("B3", "Từ " . $from_date . " đến " . $to_date);
            }
            foreach ($products as $e) {
                array_push($contract_value_total_arr, $e->contract_value_total);
                array_push($received_money_total_arr, $e->received_money_total);
                array_push($debt_total_arr, $e->debt_total);
                $sheetProduct->setCellValue("A" . $row, $e->code);
                $sheetProduct->setCellValue("B" . $row, $e->name);
                $sheetProduct->setCellValue("D" . $row, $e->contract_value_total)->getStyle("D" . $row)->getNumberFormat()->setFormatCode('#,###');
                $sheetProduct->setCellValue("E" . $row, $e->received_money_total)->getStyle("E" . $row)->getNumberFormat()->setFormatCode('#,###');
                $sheetProduct->setCellValue("F" . $row, $e->debt_total)->getStyle("F" . $row)->getNumberFormat()->setFormatCode('#,###');
                $row++;
            }
            $sheetProduct->setCellValue("B" . ($row), "Tổng cộng")->getStyle("B" . $row)->getFont()->setBold(true);
            $sheetProduct->getStyle("B" . ($row))->getAlignment()->setHorizontal('center');
            $sheetProduct->setCellValue("D" . ($row), array_sum($contract_value_total_arr))->getStyle("D" . $row)->getFont()->setBold(true);
            $sheetProduct->getStyle("D" . $row)->getNumberFormat()->setFormatCode('#,###');
            $sheetProduct->setCellValue("E" . ($row), array_sum($received_money_total_arr))->getStyle("E" . $row)->getFont()->setBold(true);
            $sheetProduct->getStyle("E" . $row)->getNumberFormat()->setFormatCode('#,###');
            $sheetProduct->setCellValue("F" . ($row), array_sum($debt_total_arr))->getStyle("F" . $row)->getFont()->setBold(true);
            $sheetProduct->getStyle("F" . $row)->getNumberFormat()->setFormatCode('#,###');
            $sheetProduct->getStyle('A5:F' . ($row))->getBorders()->getAllBorders()->setBorderStyle(Style\Border::BORDER_THIN);

            $objWriter = IOFactory::createWriter($objPHPExcel, $fileType);
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename="contract_report.xlsx"');
            header('Cache-Control: max-age=0');
            $objWriter->save("php://output");
        } catch (Exception $e) {
            Log::error("exportByProduct exportExcel: " . $row . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function reportByTeam(Request $request)
    {
        try {
            $data = Report::reportByTeam($request);
            $contract_value_total = $debt_total = $received_money_total = 0;
            foreach ($data as $item) {
                $contract_value_total += $item->contract_value_total;
                $debt_total += $item->debt_total;
                $received_money_total += $item->received_money_total;
            }
            $rowTotal = [
                'team' => 'Tổng',
                'contract_value_total' => $contract_value_total,
                'debt_total' => $debt_total,
                'received_money_total' => $received_money_total,
            ];
            return response()->json([...$data, $rowTotal]);
        } catch (Exception $e) {
            Log::error("ReportController reportByProduct: " . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function exportByTeam(Request $request)
    {
        try {
            $inputFileName = 'templates/report_by_team.xlsx';
            $fileType = IOFactory::identify($inputFileName);
            $objReader = IOFactory::createReader($fileType);
            $objPHPExcel = $objReader->load($inputFileName);

            $sheetTeam = $objPHPExcel->setActiveSheetIndex(0);
            $row = 5;

            $contract_value_total_arr = [];
            $received_money_total_arr = [];
            $debt_total_arr = [];
            $products = Report::reportByTeam($request);
            if ($request->date_range) {
                $from_date = date('d-m-Y', strtotime(explode(' ', trim($request->date_range))[0]));
                $to_date = date('d-m-Y', strtotime(explode(' ', trim($request->date_range))[2]));
                $sheetTeam->setCellValue("B3", "Từ " . $from_date . " đến " . $to_date);
            }
            foreach ($products as $e) {
                array_push($contract_value_total_arr, $e->contract_value_total);
                array_push($received_money_total_arr, $e->received_money_total);
                array_push($debt_total_arr, $e->debt_total);
                $sheetTeam->setCellValue("A" . $row, $e->team);
                $sheetTeam->setCellValue("B" . $row, $e->name);
                $sheetTeam->setCellValue("D" . $row, $e->contract_value_total)->getStyle("D" . $row)->getNumberFormat()->setFormatCode('#,###');
                $sheetTeam->setCellValue("E" . $row, $e->received_money_total)->getStyle("E" . $row)->getNumberFormat()->setFormatCode('#,###');
                $sheetTeam->setCellValue("F" . $row, $e->debt_total)->getStyle("F" . $row)->getNumberFormat()->setFormatCode('#,###');
                $row++;
            }
            $sheetTeam->setCellValue("B" . ($row), "Tổng cộng")->getStyle("B" . $row)->getFont()->setBold(true);
            $sheetTeam->getStyle("B" . ($row))->getAlignment()->setHorizontal('center');
            $sheetTeam->setCellValue("D" . ($row), array_sum($contract_value_total_arr))->getStyle("D" . $row)->getFont()->setBold(true);
            $sheetTeam->getStyle("D" . $row)->getNumberFormat()->setFormatCode('#,###');
            $sheetTeam->setCellValue("E" . ($row), array_sum($received_money_total_arr))->getStyle("E" . $row)->getFont()->setBold(true);
            $sheetTeam->getStyle("E" . $row)->getNumberFormat()->setFormatCode('#,###');
            $sheetTeam->setCellValue("F" . ($row), array_sum($debt_total_arr))->getStyle("F" . $row)->getFont()->setBold(true);
            $sheetTeam->getStyle("F" . $row)->getNumberFormat()->setFormatCode('#,###');
            $sheetTeam->getStyle('A5:F' . ($row))->getBorders()->getAllBorders()->setBorderStyle(Style\Border::BORDER_THIN);

            $objWriter = IOFactory::createWriter($objPHPExcel, $fileType);
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename="contract_report.xlsx"');
            header('Cache-Control: max-age=0');
            $objWriter->save("php://output");
        } catch (Exception $e) {
            Log::error("exportByTeam exportExcel: " . $row . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function reportByMarket(Request $request)
    {
        try {
            $data = Report::report_by_market($request);
            $contract_value_total = $debt_total = $received_money_total = $fb_total = 0;
            foreach ($data as $item) {
                $contract_value_total += $item->contract_value_total;
                $debt_total += $item->debt_total;
                $received_money_total += $item->received_money_total;
                $fb_total += $item->fb_total;
            }
            $rowTotal = [
                'name' => 'Tổng',
                'contract_value_total' => $contract_value_total,
                'debt_total' => $debt_total,
                'received_money_total' => $received_money_total,
                'fb_total' => $fb_total,
            ];

            return response()->json([...$data, $rowTotal]);
        } catch (Exception $e) {
            Log::error("ReportController reportByMarket: " . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function exportByMarket(Request $request)
    {
        try {
            $inputFileName = 'templates/report_by_market.xlsx';
            $fileType = IOFactory::identify($inputFileName);
            $objReader = IOFactory::createReader($fileType);
            $objPHPExcel = $objReader->load($inputFileName);
            $sheetMarket = $objPHPExcel->setActiveSheetIndex(0);

            $contract_value_total_arr = [];
            $received_money_total_arr = [];
            $debt_total_arr = [];
            $fb_total_arr = [];
            $dataReport = Report::report_by_market($request);
            if ($request->dateRange) {
                $fromDate = date('d-m-Y', strtotime(explode(' ', trim($request->dateRange))[0]));
                $toDate = date('d-m-Y', strtotime(explode(' ', trim($request->dateRange))[2]));
                $sheetMarket->setCellValue("B3", "Từ " . $fromDate . " đến " . $toDate);
            }
            
            $row = 5;
            foreach ($dataReport as $e) {
                array_push($contract_value_total_arr, $e->contract_value_total);
                array_push($received_money_total_arr, $e->received_money_total);
                array_push($debt_total_arr, $e->debt_total);
                array_push($fb_total_arr, $e->fb_total);
                $sheetMarket->setCellValue("A" . $row, $e->name);
                $sheetMarket->setCellValue("B" . $row, $e->contract_value_total)->getStyle('B' . $row)->getNumberFormat()->setFormatCode('#,###');
                $sheetMarket->setCellValue("C" . $row, $e->received_money_total)->getStyle('C' . $row)->getNumberFormat()->setFormatCode('#,###');
                $sheetMarket->setCellValue("D" . $row, $e->debt_total)->getStyle('D' . $row)->getNumberFormat()->setFormatCode('#,###');
                $sheetMarket->setCellValue("E" . $row, $e->fb_total)->getStyle('E' . $row)->getNumberFormat()->setFormatCode('#,###');
                $row++;
            }
            $sheetMarket->setCellValue("A" . ($row), "Tổng cộng")->getStyle("A" . $row)->getFont()->setBold(true);
            $sheetMarket->getStyle("A" . ($row))->getAlignment()->setHorizontal('center');
            $sheetMarket->setCellValue("B" . ($row), array_sum($contract_value_total_arr))->getStyle("B" . $row)->getFont()->setBold(true);
            $sheetMarket->getStyle("B" . $row)->getNumberFormat()->setFormatCode('#,###');
            $sheetMarket->setCellValue("C" . ($row), array_sum($received_money_total_arr))->getStyle("C" . $row)->getFont()->setBold(true);
            $sheetMarket->getStyle("C" . $row)->getNumberFormat()->setFormatCode('#,###');
            $sheetMarket->setCellValue("D" . ($row), array_sum($debt_total_arr))->getStyle("D" . $row)->getFont()->setBold(true);
            $sheetMarket->getStyle("D" . $row)->getNumberFormat()->setFormatCode('#,###');
            $sheetMarket->setCellValue("E" . ($row), array_sum($fb_total_arr))->getStyle("D" . $row)->getFont()->setBold(true);
            $sheetMarket->getStyle("E" . $row)->getNumberFormat()->setFormatCode('#,###');
            $sheetMarket->getStyle('A5:G' . ($row))->getBorders()->getAllBorders()->setBorderStyle(Style\Border::BORDER_THIN);

            $objWriter = IOFactory::createWriter($objPHPExcel, $fileType);
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename="contract_report.xlsx"');
            header('Cache-Control: max-age=0');
            $objWriter->save("php://output");
        } catch (Exception $e) {
            Log::error("exportByMarket exportExcel: " . $row . $e->getMessage());
            return response()->json([], 500);
        }
    }
}
