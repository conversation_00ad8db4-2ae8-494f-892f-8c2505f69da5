<?php

namespace App\Http\Controllers;

use App\Http\Requests\QuarterlyGoalsStoreRequest;
use App\Models\Position;
use App\Models\QuarterlyGoals;
use Illuminate\Http\Request;
use App\Models\Response;

class QuarterlyGoalsController extends Controller
{
    public function getLeaderTeamSales()
    {
        $leader_team_sales = Position::year(date('Y'))
            ->sale()
            ->where('is_leader', true)
            ->whereNotNull('representatives')
            ->get();
        return $leader_team_sales;
    }

    public function index()
    {
        $data = QuarterlyGoals::with('leader')
            ->active()
            ->paginate(20);
        return Response::formatResponse(config('apicode.SUCCESS'), $data);
    }

    public function store(QuarterlyGoalsStoreRequest $request)
    {
        try {
            $data = $request->all();
            QuarterlyGoals::create($data);
            return Response::formatResponse(config('apicode.SUCCESS'), $data);
        } catch (\Exception $e) {
            return Response::formatResponse(config('apicode.PROCESS_SERVER_ERROR'), $e->getMessage());
        }
    }

    public function show($id)
    {
        $data = QuarterlyGoals::where('id', $id)->first();
        return Response::formatResponse(config('apicode.SUCCESS'), $data);
    }

    public function update(QuarterlyGoalsStoreRequest $request, $id)
    {
        try {
            $data = $request->all();
            QuarterlyGoals::where('id', $id)->update($data);
            return Response::formatResponse(config('apicode.SUCCESS'), $data);
        } catch (\Exception $e) {
            return Response::formatResponse(config('apicode.PROCESS_SERVER_ERROR'), $e->getMessage());
        }
    }

    public function destroy($id)
    {
        $data = QuarterlyGoals::where('id', $id)->delete();
        return Response::formatResponse(config('apicode.SUCCESS'), $data);
    }
}