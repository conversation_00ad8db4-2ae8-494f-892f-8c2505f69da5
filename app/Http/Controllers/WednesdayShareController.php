<?php

namespace App\Http\Controllers;

use App\Jobs\SendWedShareEmail;
use App\Models\Response;
use App\Models\Time;
use App\Models\User;
use App\Models\UserWorking;
use App\Models\WednesdayShare;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class WednesdayShareController extends BaseController
{
    /**
     * Danh mục thời gian
     */
    public function getTime()
    {
        $data = Time::all();
        return Response::formatResponse(config('apicode.SUCCESS'), $data);
    }

    /**
     * Danh sách thư 4 sẽ chia
     */
    public function index(Request $request)
    {
        $data = WednesdayShare::with('speaker', 'hearer', 'time')->orderBy('created_at', 'DESC')->paginate(20);
        return Response::formatResponse(config('apicode.SUCCESS'), $data);
    }

    /**
     * Thêm mới
     */
    public function store(Request $request)
    {
        DB::beginTransaction();
        try {
            $datas = $request->wednesday_share;

            $date = Carbon::parse($datas['date']);
            $datas['week'] = $date->weekOfYear;
            $datas['year'] = $date->year;

            # check xem tuần này đã có yêu cầu nào đc xác nhận hay chưa?
            $checkIsset = WednesdayShare::whereIn('status', [1, 2])->where('week', $datas['week'])->where('year', $datas['year'])->count();
            if ($checkIsset < 2) {
                $rs = WednesdayShare::create($datas);

                # send mail cho người lắng nghe biết có đặt lịch
                $info = WednesdayShare::where('id', $rs->id)->with('speaker', 'hearer', 'time')->first();
                $content = (object) [];
                $content->receiver = $info->hearer->email;
                $content->receiver_name = $info->hearer->name;
                $content->subject = '[VIETEC-QLDN]- Thông báo Xác nhận chương trình thứ 4 chia sẻ!';
                $content->message = 'Anh/chị đã nhận được đề nghị tham gia thứ 4 chia sẻ từ ' . $info->speaker->name . ' vào lúc: ' . $info->time->time . ' ngày: ' . vietec_format_date($info->date) . ' tại địa điểm: ' . $info->address . ', vui lòng vào hệ thống QLDN để xem xét xác nhận hoặc từ chối lời đề nghị!';

                self::sendMail($content); // gửi cho người lắng nghe
                DB::commit();
                return Response::formatResponse(config('apicode.SUCCESS'), $rs);
            } else {
                return Response::formatResponse(config('apicode.INTERNAL_SERVER_ERROR'), [], 'Bạn không thể thêm mới lịch hẹn trong tuần này nữa!');
            }
        } catch (\Exception $e) {
            DB::rollback();
            return Response::formatResponse(config('apicode.INTERNAL_SERVER_ERROR'), $e->getMessage());
        }
    }

    /**
     * thông tin
     */
    public function show(Request $request)
    {
        try {
            $data = WednesdayShare::where("id", $request->id)->with('time', 'speaker.user.department', 'hearer.user.department')->first();
            return Response::formatResponse(config('apicode.SUCCESS'), $data);
        } catch (\Exception $e) {
            return $e;
        }
    }

    /**
     * Cập nhật/Xác nhận/Từ chối
     */
    public function update(Request $request)
    {
        DB::beginTransaction();
        try {
            $data = $request->wednesday_share;

            $date = Carbon::parse($data['date']);
            $data['week'] = $date->weekOfYear;
            $data['year'] = $date->year;
            # check xem tuần này đã có yêu cầu nào đc xác nhận hay chưa?
            $checkIsset = WednesdayShare::whereIn('status', [1, 2])->where('week', $data['week'])->where('year', $data['year'])->count();
            if ($checkIsset < 2) {
                $dt = [
                    'time_id' => $data['time_id'],
                    'speaker_id' => $data['speaker_id'],
                    'hearer_id' => $data['hearer_id'],
                    'date' => $data['date'],
                    'address' => $data['address'],
                    'note' => $data['note'],
                    'status' => $data['status'],
                    'week' => $data['week'],
                    'year' => $data['year'],
                ];
                WednesdayShare::where('id', $request->id)->update($dt);

                # nếu xác nhận hay từ chối đề send mail cho người đặt lịch biết
                if ($data['status'] == 2 || $data['status'] == 3) {
                    $info = WednesdayShare::where('id', $request->id)->with('speaker', 'hearer', 'time')->first();
                    # gửi mail cho người nói (người yêu cầu) biết trạng thái xác nhận
                    $content = (object) [];
                    $content->subject = '[VIETEC-QLDN]- Thông báo Xác nhận về thứ 4 chia sẻ!';
                    $content->receiver = $info->speaker->email;
                    $content->receiver_name = $info->speaker->name;
                    if ($data['status'] == 2) {
                        $content->message = 'Bạn đã được Anh/chị: ' . $info->hearer->name . ' đồng ý xác nhận tham gia thứ 4 chia sẻ vào lúc: ' . $info->time->time . ' ngày: ' . vietec_format_date($info->date) . ' tại địa điểm: ' . $info->address . '. Chúc bạn có 1 buổi gặp gỡ chia sẻ thành công, hiệu quả!';
                    } else {
                        $content->message = 'Bạn đã bị từ chối đề nghị tham gia thứ 4 chia sẻ từ Anh/chị: ' . $info->hearer->name . ', vui lòng vào hệ thống để xem lý do cũng như đặt lịch khác nếu có nhu cầu!';
                    }
                    self::sendMail($content); // gửi cho người nói

                    if ($data['status'] == 2) {
                        # Nếu xác nhận thì Báo lại cho chính mình luôn (người nghe)
                        $content->receiver = $info->hearer->email;
                        $content->receiver_name = $info->hearer->name;
                        $content->message = 'Bạn đã xác nhận tham gia thứ 4 chia sẻ cùng Anh/chị: ' . $info->speaker->name . ' vào lúc: ' . $info->time->time . ' ngày: ' . vietec_format_date($info->date) . ' tại địa điểm: ' . $info->address . '. Chúc bạn có 1 buổi gặp gỡ chia sẻ thành công, hiệu quả!';
                        self::sendMail($content); // gửi cho người nghe
                    }
                }

                DB::commit();
                return Response::formatResponse(config('apicode.SUCCESS'), $data);
            } else {
                return Response::formatResponse(config('apicode.INTERNAL_SERVER_ERROR'), [], 'Bạn không thể thêm mới lịch hẹn trong tuần này nữa!');
            }
        } catch (\Exception $e) {
            DB::rollback();
            return Response::formatResponse(config('apicode.INTERNAL_SERVER_ERROR'), $e->getMessage());
        }
    }

    /**
     * Xoá
     */
    public function destroy(Request $request)
    {
        try {
            WednesdayShare::where('id', $request->id)->delete();
            return response()->json(['message' => 'Success'], 200);
        } catch (\Exception $e) {
            return $e;
        }
    }

    /**
     * Lấy danh sách nhân sự theo phòng ban lắng nghe
     */
    public function getHearer(Request $request)
    {
        $user_id = auth()->user()->id;
        $date = Carbon::parse($request->date);
        $week = $date->weekOfYear;
        $year = $date->year;
        $person_books = WednesdayShare::where('week', $week)->where('year', $year)->whereIn('status', [1, 2])->get();
        $person_book_ids = [];
        $person_book_ids[] = $user_id; // not in chính bản thân mình trước
        foreach ($person_books as $person_book) {
            array_push($person_book_ids, $person_book->speaker_id, $person_book->hearer_id);
        }
        $data = UserWorking::where('department_id', $request->department_id)->where('status', 1)->whereNotIn('user_id', $person_book_ids)->with('user')->get();

        # chỗ này là lấy thêm người lắng nghe để đẩy ra form edit
        if (!empty($request->hearer_id)) {
            $header = UserWorking::where('department_id', $request->department_id)->where('user_id', $request->hearer_id)->with('user')->get();
        } else {
            $header = [];
        }

        $rs = [
            'lists' => $data,
            'header' => $header,
        ];

        return Response::formatResponse(config('apicode.SUCCESS'), $rs);
    }

    /**
     * send mail thông báo xác nhận, từ chối lắng nghe
     */
    private static function sendMail($content)
    {
        # khác môi trường PRO thì gửi hết về annp để test, cc thêm tuanna và huongtt
        $content->receiver = env('APP_ENV') == 'production' ? $content->receiver : '<EMAIL>';
        $content->cc = [];
        $emailJob = (new SendWedShareEmail($content))->onQueue('email');
        dispatch($emailJob);
    }

    public function review(Request $request)
    {
        try {
            $id = $request->id;
            $grade = $request->grade;
            $rate = $request->rate;
            $option = $request->option;

            $data = [];
            if ($option == 'speaker') {
                $data['speaker_grade'] = $grade;
                $data['speaker_rate'] = $rate;
            } else if ($option == 'hearer') {
                $data['hearer_grade'] = $grade;
                $data['hearer_rate'] = $rate;
            }
            WednesdayShare::where('id', $id)->update($data);
            return Response::formatResponse(config('apicode.SUCCESS'), $data);
        } catch (\Exception $e) {
            return Response::formatResponse(config('apicode.INTERNAL_SERVER_ERROR'), $e->getMessage());
        }
    }
}
