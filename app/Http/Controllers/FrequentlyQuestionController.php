<?php

namespace App\Http\Controllers;

use App\Models\Question;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use App\Models\Response;
use PhpParser\Node\Stmt\Return_;

class FrequentlyQuestionController extends Controller
{
    public function list(Request $request)
    {
        try {
            $data = Question::whereMonth('created_at', $request->month)->when($request->product_category_id, function ($q, $product_category_id) {
                $q->where('product_category_id', $product_category_id);
            })->when($request->status, function ($q, $status) {
                $q->where('status', $status);
            })->with('created_by', 'product_category')->orderBy('created_at', 'DESC')->paginate(20);
            return Response::formatResponse(config('apicode.SUCCESS'), $data);
        } catch (\Exception $e) {
            Log::error('FrequentlyQuestionController list: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function create(Request $request)
    {
        $request->validate([
            'product_category_id' => 'required',
            'status' => 'required',
            'title' => 'required',
            'content' => 'required',
        ], [], [
            'product_category_id' => 'Tên hệ thống',
            'status' => 'Trạng thái hiển thị',
            'title' => 'Tiêu đề',
            'content' => 'Nội dung',
        ]);
        try {
            DB::beginTransaction();
            $file = $request->file('file');
            if ($request->hasFile('file')) {
                $file_name = $file->getClientOriginalName();
                $file->storeAs('public/attachments', $file_name);
                $file_path = 'attachments/' . $file_name;
            }
            $model = new Question();
            $model->product_category_id = $request->product_category_id;
            $model->status = $request->status;
            $model->title = $request->title;
            $model->content = $request->content;
            $model->file_name = isset($file_name) ? $file_name : '';
            $model->file_path = isset($file_path) ? $file_path : '';
            $model->created_by = auth()->user()->id;
            $model->save();
            DB::commit();
            return response()->json(['message' => 'Xử lý thành công!']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('FrequentlyQuestionController store: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function show($id)
    {
        try {
            $data = Question::find($id);
            return Response::formatResponse(config('apicode.SUCCESS'), $data);
        } catch (\Exception $e) {
            Log::error('FrequentlyQuestionController list: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function update(Request $request, $id)
    {
        $request->validate([
            'product_category_id' => 'required',
            'status' => 'required',
            'title' => 'required',
            'content' => 'required',
        ], [], [
            'product_category_id' => 'Tên hệ thống',
            'status' => 'Trạng thái hiển thị',
            'title' => 'Tiêu đề',
            'content' => 'Nội dung',
        ]);
        try {
            DB::beginTransaction();
            $file = $request->file('file');
            if ($request->hasFile('file')) {
                $file_name = $file->getClientOriginalName();
                $file->storeAs('public/attachments', $file_name);
                $file_path = 'attachments/' . $file_name;
            }

            $model = Question::find($id);
            $model->product_category_id = $request->product_category_id;
            $model->status = $request->status;
            $model->title = $request->title;
            $model->content = $request->content;
            $model->file_name = isset($file_name) ? $file_name : '';
            $model->file_path = isset($file_path) ? $file_path : '';
            $model->created_by = auth()->user()->id;
            $model->save();
            DB::commit();
            return response()->json(['message' => 'Xử lý thành công!']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('FrequentlyQuestionController store: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function delete($id)
    {
        try {
            DB::beginTransaction();
            Question::where('id', $id)->delete();
            DB::commit();
            return response()->json(['message' => 'Xử lý thành công']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('FrequentlyQuestionController delete: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function downloadFile($id)
    {
        $data = Question::where('id', $id)->first();
        $file_path = $data->file_path;
        $file =  public_path('/storage/' . $file_path);
        $headers = array('Content-Type: application/pdf',);
        return response()->download($file, 'info.pdf', $headers);
    }

    public function frequentlyQuestionByProject(Request $request)
    {
        try {
            $req = $request->all();
            $paginate = $req['paginate'];
            $param = [
                'token' =>  $req['token'],
                'code' => $req['code']
            ];
            if (in_array($param, config('token'))) {
                $code =  $param['code'];
            }
            $data = Question::select('title', 'content', 'status', 'file_name', 'file_path', 'product_category_id')->where('status', true)->whereHas('product_category', function ($query) use ($code) {
                $query->where('code', $code);
            })->with('product_category')->paginate($paginate);
            return Response::formatResponse(config('apicode.SUCCESS'), $data);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('FrequentlyQuestionController frequentlyQuestionByProject: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }
}
