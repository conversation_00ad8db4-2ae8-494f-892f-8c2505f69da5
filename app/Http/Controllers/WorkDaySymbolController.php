<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\WorkDaySymbol;
use App\Models\Response;


class WorkDaySymbolController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $data = WorkDaySymbol::paginate(20);
        return Response::formatResponse(config('apicode.SUCCESS'), $data);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required',
            'symbol' => 'required|unique:m_work_day_symbols,symbol',
        ]);
        try {
            $data = $request->all();
            WorkDaySymbol::create($data);
            return Response::formatResponse(config('apicode.SUCCESS'), $data);
        } catch (\Exception $e) {
            return Response::formatResponse(config('apicode.INTERNAL_SERVER_ERROR'), $e->getMessage());
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\WorkDaySymbol $workdaysymbol
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request)
    {
        $request->validate([
            'name' => 'required',
            'symbol' => 'required|unique:m_work_day_symbols,symbol,' . $request->id,
        ]);
        try {
            $data = $request->all();
            WorkDaySymbol::where('id', $request->id)->update($data);
            return Response::formatResponse(config('apicode.SUCCESS'), $data);
        } catch (\Exception $e) {
            return Response::formatResponse(config('apicode.INTERNAL_SERVER_ERROR'), $e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\WorkDaySymbol $workdaysymbol
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, $id)
    {
        $data = WorkDaySymbol::where('id', $id)->delete();
        return Response::formatResponse(config('apicode.SUCCESS'), $data);
    }
}
