<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\MasterKPI;
use App\Models\Response;
use Illuminate\Support\Facades\Log;

class MasterKPIController extends Controller
{

    public function index(Request $request)
    {
        if ($request->list) {
            $data = MasterKPI::paginate(20);
        } else {
            $level = auth()->user()->user->position->is_leader ? MasterKPI::LEADER : MasterKPI::STAFF;
            $data = MasterKPI::where('level', $level)->paginate(20);
        }
        return Response::formatResponse(config('apicode.SUCCESS'), $data);
    }

    public function store(Request $request)
    {
        try {
            $data = $request->all();
            MasterKPI::create($data);
            return Response::formatResponse(config('apicode.SUCCESS'), $data);
        } catch (\Exception $e) {
            return Response::formatResponse(config('apicode.INTERNAL_SERVER_ERROR'), $e->getMessage());
        }
    }

    public function update(Request $request)
    {
        try {
            $data = $request->all();
            MasterKPI::where('id', $request->id)->update($data);
            return Response::formatResponse(config('apicode.SUCCESS'), $data);
        } catch (\Exception $e) {
            return Response::formatResponse(config('apicode.INTERNAL_SERVER_ERROR'), $e->getMessage());
        }
    }

    public function destroy($id)
    {
        $data = MasterKPI::where('id', $id)->delete();
        return Response::formatResponse(config('apicode.SUCCESS'), $data);
    }
}
