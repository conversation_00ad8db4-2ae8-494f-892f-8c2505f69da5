<?php

namespace App\Http\Controllers;

use App\Models\Department;
use App\Models\Position;
use Illuminate\Http\Request;
use Exception;
use App\Models\User;
use App\Models\UserWorking;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use App\Models\Response;

class PositionController extends Controller
{
    public function index()
    {
        try {
            $data = Position::year(date('Y'))
                ->with('department')
                ->orderBy('department_id', 'ASC')
                ->orderBy('sort', 'ASC')
                ->paginate(20);
            return Response::formatResponse(config('apicode.SUCCESS'), $data);
        } catch (Exception $e) {
            Log::error("PositionController index: " . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function getByDepartmentId(Request $request)
    {
        $data = Position::year(date('Y'))
            ->where('department_id', $request->department_id)
            ->orderBy('sort', 'ASC')
            ->get();

        return response()->json($data);
    }

    public function getSubPositionByDepartmentId(Request $request)
    {
        $query = UserWorking::allActive()
            ->where('department_id', $request->department_id);
        if ($request->has('user_id') && $request->user_id) {
            $query->where('user_id', '<>', $request->user_id);
        }

        $usedPositionIds = $query->distinct()
            ->pluck('position_id')
            ->toArray();

        $data = Position::year(date('Y'))
            ->where('department_id', $request->department_id)
            // ->where(function ($query) use ($usedPositionIds) {
            //     $query->whereNotIn('id', $usedPositionIds)
            //         ->orWhere(function ($subQuery) use ($usedPositionIds) {
            //             $subQuery->whereIn('id', $usedPositionIds)
            //                 ->where('is_leader', false);
            //         });
            // })
            ->orderBy('sort', 'ASC')
            ->get();

        return response()->json($data);
    }

    public function getLeaderPosition(Request $request)
    {
        $query = Position::year(date('Y'))
            ->where('department_id', $request->department_id)
            ->where('is_leader', 1)
            ->when($request->position_id, function ($q, $positionId) {
                $q->where('id', '<>', $positionId);
            });

        $data = $query
            ->orderBy('sort', 'ASC')->get();

        $directors = collect([]);
        if ($request->position_id && Position::year(date('Y'))->where('id', $request->position_id)->where('is_leader', 1)->first()) {
            $directors = Position::year(date('Y'))
                ->where('department_id', Department::DIRECTOR_DEPARTMENT)
                ->get();
        }

        $mergeData = $data->concat($directors)->sortBy('sort')->values()->all();

        return response()->json($mergeData);
    }

    public function store(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required',
                'id' => 'integer'
            ]);

            if ($validator->fails()) {
                return response()->json(['message' => $validator->errors()->first()], 400);
            }

            $model = new Position();
            $model->setYear(date('Y'));
            $msg = 'Thêm chức vụ thành công!';
            if ($request->has('id')) {
                $model = Position::year(date('Y'))->find($request->id);
                $msg = 'Cập nhật chức vụ thành công!';
            }

            $model->department_id = $request->department_id;
            $model->is_leader = isset($request->is_leader);
            $model->name = trim($request->name);
            $model->note = trim($request->note);
            $model->sort = $request->sort;
            $model->save();
            return response()->json(['message' => $msg]);
        } catch (\Exception $e) {
            Log::error("PositionController store: " . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function getPositions()
    {
        try {
            $rs = Position::year(date('Y'))->select('*')->get();

            return response()->json($rs);
        } catch (\Exception $e) {
            Log::error('PositionController getPositions: ' . $e->getMessage());

            return response()->json([], 500);
        }
    }

    public function getLeader()
    {
        $data = User::select('users.id', 'users.name', 'user_workings.user_id', 'user_workings.department_id')
            ->join('user_workings', 'user_workings.user_id', 'users.id')
            ->whereIn('department_id', [6, 8])
            ->get();
        return $data;
    }

    public function destroy(Request $request)
    {
        try {
            Position::year(date('Y'))->where('id', $request->id)->delete();
            return response()->json(['message' => 'Success'], 200);
        } catch (\Exception $e) {
            Log::error('ClientController destroy: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function edit(Request $request)
    {
        try {
            $data = [
                'department_id' => $request->department_id,
                'name' => $request->name,
                'is_leader' => $request->is_leader,
                'note' => $request->note,
                'sort' => $request->sort
            ];
            Position::year(date('Y'))->where('id', $request->id)->update($data);
            return response()->json(['message' => 'Success'], 200);
        } catch (\Exception $e) {
            Log::error('ClientController destroy: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function getTeamSale()
    {
        $data = Position::year(date('Y'))->select('representatives', 'representatives as name', 'sort', 'id')->where('representatives', 'like', '%TEAM%')->orderBy('sort', 'ASC')->get();
        return $data;
    }
}
