<?php

namespace App\Http\Controllers;

use App\Http\Requests\EmploymentContractRequest;
use App\Http\Requests\UserStoreRequest;
use App\Http\Requests\UserUpdateRequest;
use App\Models\File as ModelsFile;
use App\Models\Response;
use App\Models\User;
use App\Models\UserWorking;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use App\Http\Requests\ChangePasswordRequest;
use App\Models\Department;
use App\Models\Position;
use App\Models\HanetCamUser;
use App\Models\UserFamily;
use App\Services\HanetApiService;
use PhpOffice\PhpSpreadsheet\IOFactory;
use Illuminate\Support\Facades\DB;
use App\Transformer\APIJsonResponse;
use Illuminate\Support\Facades\Validator;

class UserController extends Controller
{
    public function index(Request $request)
    {
        $data = User::list($request);
        $data = empty($data) ? null : $data;
        return Response::formatResponse(config('apicode.SUCCESS'), $data);
    }

    public function show(Request $request, $id)
    {
        try {
            if ($request->user()->cannot('viewUserDetail', User::find($id))) {
                return Response::formatResponse(config('apicode.PERMISSION_DENIED'), []);
            }

            $info = User::show($id);
            return Response::formatResponse(config('apicode.SUCCESS'), $info);
        } catch (\Exception $e) {
            Log::error('UserController show: ' . $e->getMessage());
            return Response::formatResponse(config('apicode.INTERNAL_SERVER_ERROR'), []);
        }
    }

    public function store(UserStoreRequest $request)
    {
        try {
            $user = User::where('staff_code',$request->staff_code)->first();
            if ($user) {
                return response()->json(['errors' =>['staff_code' => 'Mã nhân viên đã tồn tại'],'result' => false], 400);
            }
            $data = User::store($request);
            $data = empty($data) ? null : $data;
            HrInfoController::infoNewStaff();
            return response()->json($data);
        } catch (\Exception $e) {
            Log::error("UserController store: " . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function update(UserUpdateRequest $request)
    {
        $data = User::edit($request);
        $data = empty($data) ? null : $data;
        return Response::formatResponse(config('apicode.SUCCESS'), $data);
    }

    public function offuser(Request $request)
    {
        $data = User::off($request);
        $data = empty($data) ? null : $data;
        return Response::formatResponse(config('apicode.SUCCESS'), $data);
    }

    public function createEmploymentContract(EmploymentContractRequest $request)
    {
        try {
            DB::beginTransaction();
            if ($request->id) {
                if (!$request->status) {
                    UserWorking::where('status', 1)->where('user_id', $request->user_id)
                        ->update([
                            'status' => false,
                            'employment_contract_number' => $request->employment_contract_number,
                            'employment_contract_id' => $request->employment_contract_id,
                            'end_date' => $request->end_date,
                            'start_date' => $request->start_date
                        ]);
                    DB::commit();
                    return response()->json(['message' => 'Cập nhật thành công!']);
                }

                $roleIds = $request->role_id ? [$request->role_id] : [];
                UserWorking::where('user_id', $request->user_id)
                    ->where('id', $request->id)
                    ->update([
                        'status' => true,
                        'department_id' => $request->department_id,
                        'position_id' => $request->position_id,
                        'dependent_position_id' => $request->dependent_position_id,
                        'role_id' => $request->role_id,
                        'employment_contract_id' => $request->employment_contract_id,
                        'employment_contract_number' => $request->employment_contract_number,
                        'start_date' => $request->start_date,
                        'end_date' => $request->end_date,
                        'company' => $request->company,
                    ]);

                if ($request->positions) {
                    // update user working exists
                    $updateUserWorkingIds = [];
                    $newSubUserWorking = [];
                    foreach ($request->positions as $position) {
                        if ($position['id']) {
                            UserWorking::where('user_id', $request->user_id)
                                ->where('parent_id', $request->id)
                                ->where('id', $position['id'])
                                ->update([
                                    'status' => true,
                                    'department_id' => $position['department_id'],
                                    'position_id' => $position['position_id'],
                                    'dependent_position_id' => $position['dependent_position_id'],
                                    'role_id' => $position['role_id'],
                                    'employment_contract_id' => $request->employment_contract_id,
                                    'employment_contract_number' => $request->employment_contract_number,
                                    'start_date' => $request->start_date,
                                    'end_date' => $request->end_date
                                ]);
                            $updateUserWorkingIds[] = $position['id'];
                        } else {
                            $newSubUserWorking[] = $position;
                        }
                    }

                    if ($updateUserWorkingIds) {
                        UserWorking::where('user_id', $request->user_id)
                            ->where('parent_id', $request->id)
                            ->whereNotIn('id', $updateUserWorkingIds)
                            ->delete();
                    }

                    if ($newSubUserWorking) {
                        $roleIds = $this->storeUserWorking(
                            $request->user_id,
                            $newSubUserWorking,
                            $request->id,
                            $request->employment_contract_id,
                            $request->employment_contract_number,
                            $request->note,
                            $request->start_date,
                            $request->end_date,
                            $roleIds,
                            $request->company
                        );
                    }

                    if ($roleIds) {
                        User::synRole($request->user_id, $roleIds);
                    }
                } else {
                    UserWorking::where('user_id', $request->user_id)->where('is_sub_position', 1)->delete();
                    $uw = UserWorking::where('user_id', $request->user_id)
                        ->where('parent_id', $request->id)
                        ->distinct()
                        ->pluck('role_id')
                        ->toArray();
                    if ($uw) {
                        $roleIds = array_merge($roleIds, $uw);
                    }

                    UserWorking::where('user_id', $request->user_id)
                        ->where('parent_id', $request->id)
                        ->update(['status' => true]);

                    if ($roleIds) {
                        User::synRole($request->user_id, $roleIds);
                    }
                }
            } else {
                UserWorking::where('user_id', $request->user_id)->where('status', true)->update(['status' => false]);
                $roleIds = [];
                $mainPosition = UserWorking::create([
                    'user_id' => $request->user_id,
                    'department_id' => $request->department_id,
                    'position_id' => $request->position_id,
                    'dependent_position_id' => $request->dependent_position_id,
                    'role_id' => $request->role_id,
                    'is_sub_position' => false,
                    'employment_contract_id' => $request->employment_contract_id,
                    'employment_contract_number' => $request->employment_contract_number,
                    'note' => $request->note,
                    'start_date' => $request->start_date,
                    'end_date' => $request->end_date,
                    'company' => $request->company,
                ]);

                if ($request->role_id) {
                    $roleIds = [$request->role_id];
                }

                $roleIds = $this->storeUserWorking(
                    $request->user_id,
                    $request->positions,
                    $mainPosition->id,
                    $request->employment_contract_id,
                    $request->employment_contract_number,
                    $request->note,
                    $request->start_date,
                    $request->end_date,
                    $roleIds,
                    $request->company
                );

                if ($roleIds) {
                    User::synRole($request->user_id, $roleIds);
                }
            }
            // return 1;
            DB::commit();
            return response()->json(['message' => 'Cập nhật thành công!'], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('UserController createEmploymentContract: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    private function storeUserWorking(
        $userId,
        $data,
        $mainPositionId,
        $employmentContractId,
        $employmentContractNumber,
        $note,
        $startDate,
        $endDate,
        $roleIds = [],
        $company = ''
    ) {
        // sub position
        $userWorkings = [];
        foreach ($data as $position) {
            $leaderIds = Position::year(date('Y'))
                ->where('is_leader', true)
                ->where('department_id', $position['department_id'])
                ->pluck('id')
                ->toArray();
            $usedPositionIds = UserWorking::allActive()
                ->where('department_id', $position['department_id'])
                ->where('user_id', '<>', $userId)
                ->distinct()
                ->pluck('position_id')
                ->toArray();
            if (
                in_array($position['position_id'], $leaderIds)
                && in_array($position['position_id'], $usedPositionIds)
            ) {
                continue;
            }

            array_push($userWorkings, [
                'user_id' => $userId,
                'parent_id' => $mainPositionId,
                'department_id' => $position['department_id'],
                'position_id' => $position['position_id'],
                'dependent_position_id' => $position['dependent_position_id'],
                'role_id' => $position['role_id'],
                'is_sub_position' => true,
                'employment_contract_id' => $employmentContractId,
                'employment_contract_number' => $employmentContractNumber,
                'note' => $note,
                'start_date' => $startDate,
                'end_date' => $endDate,
                'company' => $company,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ]);

            if ($position['role_id']) {
                if (!in_array($position['role_id'], $roleIds)) {
                    $roleIds[] = $position['role_id'];
                }
            }
        }

        if ($userWorkings) {
            DB::table('user_workings')->insert($userWorkings);
        }

        return $roleIds;
    }

    public function uploadImage(Request $request)
    {
        $user = User::where('id', $request->user_id)->with('user.position')->first();
        $image_64 = $request['file']['dataUrl'];
        $extension = explode('/', explode(':', substr($image_64, 0, strpos($image_64, ';')))[1])[1];
        $replace = substr($image_64, 0, strpos($image_64, ',') + 1);
        $image = str_replace($replace, '', $image_64);
        $image = str_replace(' ', '+', $image);
        $imageName = Str::random(10) . '.' . $extension;
        if (!Storage::exists("app/public/avatar")) {
            Storage::makeDirectory("app/public/avatar", 777, true);
        }
        Storage::disk('avatar')->put($imageName, base64_decode($image));
        $path = 'storage/avatar/' . $imageName;
        ModelsFile::updateOrCreate(
            [
                'user_id' => $request->user_id
            ],
            [
                'name' => $request['file']['info']['name'],
                'path' => $path
            ]
        );

        # kiểm tra lại xem trên camAI đã đăng ký người dùng chưa để đăng ký lên cam chấm công
        $modelCamHanet = HanetCamUser::where('aliasID', $user->staff_code)->first();
        if (!$modelCamHanet && $user->user && $user->user->position) {
            # nếu chưa đăng ký cam thì mới call API sang hanet
            $hanetApiService = new HanetApiService();
            $params = [
                'name' => $user->name,
                'aliasID' => $user->staff_code,
                'title' => $user->user->position->name,
                'url' => url($path),
            ];
            $rs = $hanetApiService->registerByUrl($params, $user->id);
            if (!empty($rs) && isset($rs->returnCode) && $rs->returnCode == 1) {
                $dataUser = $rs->data;
                HanetCamUser::create([
                    'personID' => $dataUser->personID,
                    'aliasID' => $dataUser->aliasID,
                    'name' => $dataUser->name,
                    'avatar' => $dataUser->file,
                    'title' => $dataUser->title,
                    'type' => 0,
                ]);
            }
        }
        return 1;
    }

    public function mobileUploadImage(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required',
            'dataUrl' => 'required',
            'filename' => 'required',
            'extension' => 'required',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        $userId = $request->user_id;
        $image64 = $request->dataUrl;
        $filename = $request->filename;
        $extension = $request->extension;
        $imageName = Str::random(10) . '.' . $extension;

        $user = User::where('id', $userId)->with('user.position')->first();
        if (!$user) {
            return (new APIJsonResponse)->responseError();
        }

        if (!Storage::exists("app/public/avatar")) {
            Storage::makeDirectory("app/public/avatar", 777, true);
        }
        Storage::disk('avatar')->put($imageName, base64_decode($image64));
        $path = 'storage/avatar/' . $imageName;
        ModelsFile::updateOrCreate(
            [
                'user_id' => $userId
            ],
            [
                'name' => $filename,
                'path' => $path
            ]
        );

        return (new APIJsonResponse)->responseSuccess();
    }

    public function changePassword(ChangePasswordRequest $request)
    {
        $user = Auth::user();
        try {
            DB::beginTransaction();
            $user = User::query()->where('id', $user->id)->first();
            if (Hash::check($request->old_password, $user->password)) {
                $update = User::query()->where('id', $user->id)->update(['password' => Hash::make($request->new_password)]);
            } else {
                return response()->json(['errors' => ['old_password' => 'Mật khẩu cũ không chính xác']], 422);
            }
            DB::commit();
            return response()->json(['message' => 'Cập nhật thành công']);
        } catch (\Exception $e) {
            return $e;
        }
    }

    public function getUserWorkings()
    {
        try {
            $rs = UserWorking::select('*')
                ->with(['user', 'user.model_user', 'user.avatar'])
                ->allActive()
                ->get();

            return response()->json($rs);
        } catch (\Exception $e) {
            Log::error("UserController getUserWorkings: " . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function getUserStatus(Request $request)
    {
        try {
            //last month
            $date = explode('-', $request->month);
            $month = $date[0];
            $year =  $date[1];
            $departments = [];
            $lay_offs = [];
            $on_jobs = [];
            $data_change = Department::year(date('Y'))->select('id', 'name', 'code')->get();

            $new_staffs = $this->getNewStaffs($month, $year);
            $lay_off_staffs = $this->getLayOffStaffs($month, $year);

            foreach ($data_change as $d) {
                array_push($departments, $d->code);
                $array_new = [];
                $array_lay_off = [];
                foreach ($new_staffs as $new_staff) {
                    if ($new_staff->endOfWorking->department_id == $d->id) {
                        array_push($array_new, $new_staff);
                    }
                }
                foreach ($lay_off_staffs as $user) {
                    if ($user->endOfWorking->department_id == $d->id) {
                        array_push($array_lay_off, $new_staff);
                    }
                }
                $d->new_staff = count($array_new);
                $d->lay_off = count($array_lay_off);
                array_push($on_jobs, count($array_new));
                array_push($lay_offs, count($array_lay_off));
            }
            $data[] = [
                'name' => 'Số NV mới',
                'data' => $on_jobs
            ];
            $data[] = [
                'name' => 'Số NV nghỉ',
                'data' => $lay_offs
            ];
            return response()->json(['user_status' => $data, 'departments' => $departments, 'data_change' => $data_change]);
        } catch (\Exception $e) {
            Log::error("UserController getUserStatus: " . $e->getMessage());
            return response()->json([], 500);
        }
    }

    private function getNewStaffs($month, $year)
    {
        $new_staffs = User::whereYear(
            'created_at',
            $year
        )->whereMonth('created_at', $month)->with('endOfWorking')->get();
        return $new_staffs;
    }

    private function getLayOffStaffs($month, $year)
    {
        $lay_off_staffs = User::whereYear(
            'end_date_of_work',
            $year
        )->whereMonth('end_date_of_work', $month)->with('endOfWorking')->where('status', 0)->get();
        return $lay_off_staffs;
    }

    public function downloadFileSample()
    {
        $file = public_path() . '/templates/user_family.xlsx';
        return response()->download($file);
    }

    public function importExcel(Request $request)
    {
        $this->validate($request, [
            'file' => 'required|file|mimes:xls,xlsx'
        ]);
        $file = $request->file('file');
        try {

            $spreadsheet = IOFactory::load($file->getRealPath());
            $sheet        = $spreadsheet->getActiveSheet(0);
            $row_limit    = $sheet->getHighestDataRow();
            $row_range    = range(3, $row_limit);
            DB::beginTransaction();
            $errors = [];
            $user_family = [];
            foreach ($row_range as $row) {
                //validate
                if (!$sheet->getCell('A' . $row)->getValue()) {
                    $errors[] = "Mã nhân sự dòng " . $row . " không được bỏ trống";
                }
                $user = User::where('staff_code', $sheet->getCell('A' . $row)->getValue())->first();
                if (!$user) {
                    $errors[] = "Mã nhân sự dòng " . $row . " không tồn tại";
                }
                if (!$sheet->getCell('B' . $row)->getValue()) {
                    $errors[] = "Tên thân nhân dòng " . $row . " không được bỏ trống";
                }
                if (!$sheet->getCell('C' . $row)->getValue()) {
                    $errors[] = "Mối quan hệ dòng " . $row . " không được bỏ trống";
                }
                if (!$sheet->getCell('D' . $row)->getValue()) {
                    $errors[] = "Ngày sinh dòng " . $row . " không được bỏ trống";
                }
                if (!$sheet->getCell('E' . $row)->getValue()) {
                    $errors[] = "Giới tính dòng " . $row . " không được bỏ trống";
                }
                if ($sheet->getCell('E' . $row)->getValue() == 'Nam') {
                    $gender = false;
                }
                if ($sheet->getCell('E' . $row)->getValue() == 'Nữ') {
                    $gender = true;
                }

                if (count($errors) > 0) {
                    return response()->json($errors, 422);
                }

                $data = [
                    'user_id' => $user->id,
                    'name' => $sheet->getCell('B' . $row)->getValue(),
                    'relationship' => $sheet->getCell('C' . $row)->getValue(),
                    'dob' => $sheet->getCell('D' . $row),
                    'gender' => $gender
                ];
                array_push($user_family, $data);
            }

            UserFamily::insert($user_family);
            DB::commit();
            return Response::formatResponse(config('apicode.SUCCESS'), 1);
        } catch (\Exception $e) {
            Log::error('UserController update: ' . $e->getMessage());
            DB::rollBack();
            return Response::formatResponse(config('apicode.INTERNAL_SERVER_ERROR'), $e->getMessage());
        }
    }
}
