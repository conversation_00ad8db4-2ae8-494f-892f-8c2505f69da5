<?php
namespace App\Http\Controllers;

use App\Models\Property;
use App\Models\User;
use Illuminate\Http\Request;
use App\Models\SubProperty;
use App\Models\Response;
use App\Models\SubPropertyHistory;
use App\Http\Requests\SubPropertyStoreRequest;
use App\Models\SubPropertyDescribe;
use App\Models\SubPropertyDetail;
use App\Models\SubPropertyWarrantyHistory;
use Illuminate\Support\Facades\DB;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Reader\Exception;

class SubPropertyDetailController extends Controller
{
    public function list_asset(Request $request)
    {
        $query = SubPropertyDetail::with([
            'using',
            'using.user_use:id,name,staff_code',
            'property:id,name',
            'subPropertyDescribe.subProperty:id,name_equipment',
        ]);
        $data = $query->orderby('id', 'DESC')->paginate(20);
        return Response::formatResponse(config('apicode.SUCCESS'), $data);
    }

    public function store(Request $request)
    {
        DB::beginTransaction();

        try {
            $sub_property = $request->sub_property;
            $sub_property_describe = $request->sub_property_describe;

            $arr_property_describe = [];
            foreach($sub_property_describe as $key => $valu){
                if($valu['property_id'] == $sub_property['property']['id']){
                    $arr_property_describe[$key] = $sub_property_describe[$key];
                }
            }

            $sub_property_obj = [
                'property_id'   => $sub_property['property']['id'],
                'code'          => $sub_property['code'],
                'code_property' => isset($sub_property['code_property']) ? $sub_property['code_property'] : null,
                'price'         => isset($sub_property['price']) ? $sub_property['price'] : null,
                'purchase_date' => isset($sub_property['purchase_date']) ? $sub_property['purchase_date'] : null,
                'supplier'      => isset($sub_property['supplier']) ? $sub_property['supplier'] : null,
                'phone_number'  => isset($sub_property['phone_number']) ? $sub_property['phone_number'] : null,
                'description'   => isset($sub_property['description']) ? $sub_property['description'] : null,
                'created_by'    => auth()->user()->id,
            ];
            $sub_pro = SubPropertyDetail::create($sub_property_obj);

            foreach($arr_property_describe as $k_insert => $va_insert){
                $sub_property_describe_obj = [
                    'property_id'           => $sub_property['property']['id'],
                    'sub_property_id'       => $va_insert['id'],
                    'sub_property_detail'   => $sub_pro->id,
                    'specifications'        => $va_insert['specifications'],
                    'created_by'            => auth()->user()->id,
                ];
    
                $sub_pro_describe = SubPropertyDescribe::create($sub_property_describe_obj);
            }

            $sub_property_history = $request->sub_property_histories;
            $sub_property_history_obj = [
                'sub_property_detail_id' => $sub_pro->id,
                'trade_date'             => $sub_property_history[0]['trade_date'],
                'recover_date'           => $sub_property_history[0]['recover_date'],
                'user_use_id'            => $sub_property_history[0]['user_use_id'],
                'status'                 => $sub_property_history[0]['status'],
                'created_by'             => auth()->user()->id,
            ];
            
            $sub_history = SubPropertyHistory::create($sub_property_history_obj);

            $property_warranty_history_obj = [
                'sub_property_detail_id'    => $sub_pro->id,
                'sub_property_history_id'   => $sub_history->id,
                'created_by'                => auth()->user()->id,
                'warranty_to'               => isset($sub_property['warranty_to']) ? $sub_property['warranty_to'] : null,
                'warranty_from'             => isset($sub_property['warranty_from']) ? $sub_property['warranty_from'] : null,
                'warranty_number'           => isset($sub_property['warranty_number']) ? $sub_property['warranty_number'] : 0,
                'warranty_status'           => isset($sub_property['warranty_status']['id']) ? $sub_property['warranty_status']['id'] : 0,
            ];

            $pro_warranty_history = SubPropertyWarrantyHistory::create($property_warranty_history_obj);
            DB::commit();
            return Response::formatResponse(config('apicode.SUCCESS'), $pro_warranty_history);
        } catch (\Exception $e) {
            DB::rollBack();
            dd($e);
            return Response::formatResponse(config('apicode.INTERNAL_SERVER_ERROR'), $e->getMessage());
        }
    }

    public function update(Request $request)
    {   
        DB::beginTransaction();

        try {
            $sub_property           = $request->sub_property;
            //$arr_warranty_history   = $sub_property['sub_warranty_history'];
            $sub_warranty_history   = $request->sub_warranty_history;
            $sub_property_describe  = $request->sub_property_describe;
            $sub_property_history   = $request->sub_property_histories;

            
            $sub_property_obj = [
                'code'          => $sub_property['code'],
                'code_property' => isset($sub_property['code_property']) ? $sub_property['code_property'] : null,
                'price'         => isset($sub_property['price']) ? $sub_property['price'] : null,
                'purchase_date' => isset($sub_property['purchase_date']) ? $sub_property['purchase_date'] : null,
                'supplier'      => isset($sub_property['supplier']) ? $sub_property['supplier'] : null,
                'phone_number'  => isset($sub_property['phone_number']) ? $sub_property['phone_number'] : null,
                'description'   => isset($sub_property['description']) ? $sub_property['description'] : null,
                'created_by'    => auth()->user()->id,
            ];

            $sub_pro = SubPropertyDetail::where('id', $sub_property['id'])->update($sub_property_obj);

            $arr_property_describe = [];
            foreach($sub_property_describe as $key => $valu){
                if($valu['property_id'] == $sub_property['property']['id']){
                    $arr_property_describe[$key] = $sub_property_describe[$key];
                }
            }

            foreach($arr_property_describe as $k_insert => $va_insert){
                $sub_property_describe_obj = [
                    'specifications'        => $va_insert['specifications'],
                    'created_by'            => auth()->user()->id,
                ];
    
                $sub_pro_describe = SubPropertyDescribe::where('id', $va_insert['id'])->update($sub_property_describe_obj);
            }
            // dd($sub_property_history);
            $sub_property_history_obj = [
                'trade_date'             => $sub_property_history[0]['trade_date'],
                'recover_date'           => $sub_property_history[0]['recover_date'],
                'user_use_id'            => $sub_property_history[0]['user_use']['id'],
                'status'                 => $sub_property_history[0]['status'],
                'created_by'             => auth()->user()->id,
            ];

            $sub_history = SubPropertyHistory::where('id', $sub_property_history[0]['id'])->update($sub_property_history_obj);

            //return $request;
            //var_dump($sub_warranty_history); dd("uukkkuu");
            foreach($sub_warranty_history as $warranty_history){
                $property_warranty_history_obj_update = [
                    'warranty_to'     => isset($warranty_history['warranty_to']) ? $warranty_history['warranty_to'] : null,
                    'warranty_from'   => isset($warranty_history['warranty_from']) ? $warranty_history['warranty_from'] : null,
                    'warranty_number' => isset($warranty_history['warranty_number']) ? $warranty_history['warranty_number'] : 0,
                    'warranty_status' => isset($warranty_history['warranty_status']) ? $warranty_history['warranty_status'] : 0,
                ];
    
                $property_warranty_history_obj_create = [
                    'sub_property_detail_id'    => $sub_property['id'],
                    'sub_property_history_id'   => $sub_property['histories'][0]['id'],
                    'created_by'                => auth()->user()->id,
                    'warranty_to'               => isset($warranty_history['warranty_to']) ? $warranty_history['warranty_to'] : null,
                    'warranty_from'             => isset($warranty_history['warranty_from']) ? $warranty_history['warranty_from'] : null,
                    'warranty_number'           => isset($warranty_history['warranty_number']) ? $warranty_history['warranty_number'] : 0,
                    'warranty_status'           => isset($warranty_history['warranty_status']) ? $warranty_history['warranty_status'] : 0,
                ];

                $pro_warranty_history = SubPropertyWarrantyHistory::where('id', $warranty_history['id'])->get()->toArray();
                foreach($pro_warranty_history as $db_warranty_history){
                    if(in_array($warranty_history['id'], $db_warranty_history) == true){
                        $warranty_history_lable = SubPropertyWarrantyHistory::where('id', $warranty_history['id'])->update($property_warranty_history_obj_update);
                    } else {
                        $warranty_history_lable = SubPropertyWarrantyHistory::create($property_warranty_history_obj_create);
                    }
                }
            }
            DB::commit();
            return Response::formatResponse(config('apicode.SUCCESS'), $warranty_history_lable);
        } catch (\Exception $e) {
            DB::rollBack();
            dd($e);
            return Response::formatResponse(config('apicode.INTERNAL_SERVER_ERROR'), $e->getMessage());
        }
    }

    public function destroy($id)
    {
        $data = SubProperty::where('id', $id)->delete();
        return Response::formatResponse(config('apicode.SUCCESS'), $data);
    }

    public function detail($id)
    {
        $data = SubPropertyDetail::with('property','histories','subPropertyDescribe','subPropertyDescribe.subProperty:id,name_equipment','histories.user_use:id,name','SubPropertyWarrantyHistory')->find($id);
        return Response::formatResponse(config('apicode.SUCCESS'), $data);
    }

    public function getDataWarrantyHistoty($id)
    {
        $data = SubPropertyWarrantyHistory::where('sub_property_detail_id', $id)->get();
        return Response::formatResponse(config('apicode.SUCCESS'), $data);
    }
}
