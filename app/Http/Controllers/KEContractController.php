<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Exception;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use App\Models\SalesPackages;
use App\Models\KEContract;
use App\Models\KEContractAttachedAppendix;
use App\Models\KEContractAttachedAppendixDetails;
use App\Models\ContractPaymentHistory;
use App\Models\File as ModelsFile;
use App\Models\EquipmentType;
use App\Http\Requests\EditKEContractRequest;
use App\Http\Requests\AddKEContractRequest;
use App\Models\KEContractSalePackageAttachment;
use App\Models\Product;
use App\Models\Client;
use App\Models\ClientContact;
use App\Models\ContractFeedbackHistory;
use App\Models\Position;

class KEContractController extends Controller
{
    public function index(Request $request)
    {
        try {
            $pageSize = $request->pageSize ?: 20;
            $query = KEContract::with(['clients', 'creator']);
            // if ($request->user()->user && $request->user()->user->position) {
            //     $leaderId = UserWorking::getAllDependentPositionIdByPosition($request->user()->user->position_id, []);
            //     $query->whereIn('leader_id', $leaderId);
            // }
            $rs = $query->when($request->contract_code, function ($subQuery, $contractCode) {
                $subQuery->where('contract_code', 'LIKE', '%' . $contractCode . '%');
            })->when($request->school_name, function ($subQuery, $school_name) {
                $subQuery->whereHas('clients', function ($query) use ($school_name) {
                    return $query->where('name', 'LIKE', '%' . $school_name . '%');
                });
            })->when($request->origin_ke_sales_packages_id, function ($subQuery, $origin_ke_sales_packages_id) {
                $subQuery->where('origin_ke_sales_packages_id', $origin_ke_sales_packages_id);
            })->when($request->province_id, function ($subQuery, $province_id) {
                $subQuery->whereHas('clients', function ($query) use ($province_id) {
                    return $query->where('province_id', $province_id);
                });
            })->when($request->district_id, function ($subQuery, $district_id) {
                $subQuery->whereHas('clients', function ($query) use ($district_id) {
                    return $query->where('district_id', $district_id);
                });
            })
                ->orderBy('updated_at', 'DESC')
                ->orderBy('end_date', 'DESC')
                ->paginate($pageSize);

            $sum_received_money = $rs->sum('received_money');
            $sum_payment_amount = $rs->sum('payment_amount');
            $sum_debt = $rs->sum('debt');
            $data = [
                'data' => $rs,
                'sum_received_money' => $sum_received_money,
                'sum_payment_amount' => $sum_payment_amount,
                'sum_debt' => $sum_debt,
            ];
            return response()->json(['data' => $data]);
        } catch (Exception $e) {
            Log::error('KEContractController index: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function getEquipmentTypes()
    {
        try {
            $rs = EquipmentType::orderBy('name')
                ->get();
            return response()->json(['data' => $rs]);
        } catch (Exception $e) {
            Log::error('KEContractController getEquipmentTypes: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function store(AddKEContractRequest $request)
    {
        DB::beginTransaction();
        try {
            $client = Client::find($request->client_id);
            if (!$client) {
                return response()->json(['message' => 'Không tìm thấy thông tin khách hàng'], 404);
            }

            $product = Product::active()->kidsEnglish()->first();
            if (!$product) {
                return response()->json(['message' => 'Sản phẩm KidsEnglish chưa được cấu hình!'], 400);
            }

            if (auth()->user()->hasAnyPermission(['contract_help_upload'])) {
                $creator_id = auth()->user()->id;
                $team = $request->team;
                $leader = Position::year(date('Y'))->where('representatives', $team)->first();
                $leader_id = $leader->id;
                $sale_id = null;
            } else {
                $sale_id = auth()->user()->id;
                $leader_id = auth()->user()->user->position->is_leader ? auth()->user()->user->position_id : auth()->user()->user->dependent_position_id;
            }

            $model = new KEContract();
            $model->client_id = $request->client_id;
            $model->origin_ke_sales_packages_id = $request->origin_ke_sales_packages_id;
            $model->contract_code = $request->contract_code;
            $model->sale_id = $sale_id;
            $model->creator_id = $creator_id;
            $model->leader_id = $leader_id;
            $model->contract_value = $request->contract_value;
            $model->payment_date = $request->payment_date;
            $model->payment_amount = $request->payment_amount;
            $model->unequal = $request->unequal;
            $model->received_money = $request->received_money;
            $model->debt = $request->payment_amount - $request->received_money;
            $model->start_date = $request->start_date;
            $model->end_date = $request->end_date;
            $model->province_id = $client->province_id;
            $model->district_id = $client->district_id;
            $model->product_id = $product->id;
            $model->product_category_id = $product->product_category_id;
            $model->team = $team;

            // if ($request->user()->cannot('addKEContract', $model)) {
            //     return response()->json([], 403);
            // }

            $model->save();

            # save the option sale package
            $salePackageAttachment = [];
            foreach ($request->sale_package_attachment as $item) {
                $salePackageAttachment[] = [
                    'id' => Str::uuid()->toString(),
                    'ke_contract_id' => $model->id,
                    'ke_sales_packages_id' => $model->origin_ke_sales_packages_id,
                    'ke_sales_packages_attached_appendix_id' => $item['id'],
                    'ke_sales_packages_attached_appendix_code' => $item['code'],
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now(),
                ];
            }

            if ($salePackageAttachment) {
                DB::table('ke_contract_sale_package_attachment')->insert($salePackageAttachment);
            }

            # kiểm tra đẩy file đính kèm nếu có
            if ($request->hasFile('attachFiles')) {
                KEContract::storeAttachmentFiles($request->file('attachFiles'), $model->id);
            }

            // phụ lục đính kèm
            $attachedAppendixData = [];
            $attachedAppendixDetails = [];
            foreach ($request->attachedAppendix as $attachedAppendix) {
                $attachedAppendixItem = [
                    'id' => Str::uuid()->toString(),
                    'ke_contract_id' => $model->id,
                    'attached_appendix_name' => $attachedAppendix['attached_appendix_name'],
                    'isExWareHouse' => false,
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now(),
                ];
                $attachedAppendixData[] = $attachedAppendixItem;

                foreach ($attachedAppendix['attached_appendix_items'] as $item) {
                    $attachedAppendixDetails[] = [
                        'id' => Str::uuid()->toString(),
                        'ke_contract_attached_appendix_id' => $attachedAppendixItem['id'],
                        'equipment_type_id' => $item['equipment_type_id'],
                        'amount' => $item['amount'],
                        'quantity_shipped' => 0,
                        'price' => $item['price'],
                        'discount' => $item['discount'],
                        'discount_type' => $item['discount_type'],
                        'created_at' => Carbon::now(),
                        'updated_at' => Carbon::now(),
                    ];
                }
            }

            if ($attachedAppendixData) {
                DB::table('ke_contract_attached_appendix')->insert($attachedAppendixData);
            }

            if ($attachedAppendixDetails) {
                DB::table('ke_contract_attached_appendix_details')->insert($attachedAppendixDetails);
            }

            if ($request->current_payment_amount && (int)$request->current_payment_amount > 0) {
                ContractPaymentHistory::store(
                    $model->id,
                    $model->contract_code,
                    $model->payment_amount,
                    0,
                    $model->debt,
                    $request->current_payment_amount,
                    $request->payment_note,
                    ContractPaymentHistory::KE_PRODUCT,
                    $request->hasFile('payment_file') ? $request->payment_file : null,
                    KEContract::KE_CONTRACT,
                );
            }

            // lưu lịch sử feedback
            if ($request->feedbackObject) {
                ContractFeedbackHistory::store($model, ContractPaymentHistory::KE_PRODUCT, $request->feedbackObject);
            }

            ClientContact::store($request->client_id, $product->id, $client);

            DB::commit();

            KEContract::sendRequestExWarehouseEmail($model->contract_code);

            return response()->json(['message' => 'Thêm hợp đồng thành công!']);
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
            Log::error('KEContractController store: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function edit(EditKEContractRequest $request)
    {
        DB::beginTransaction();
        try {
            $model = KEContract::where('id', $request->id)
                ->whereDate('end_date', '>=', Carbon::now())
                ->first();
            if (!$model) {
                return response()->json(['message' => 'Dữ liệu không tồn tại'], 400);
            }

            // if ($request->user()->cannot('editKEContract', $model)) {
            //     return response()->json([], 403);
            // }

            $origin_contract_value = $model->payment_amount;
            $origin_received_money = $model->received_money;
            $origin_debt = $model->debt;

            $model->client_id = $request->client_id;
            $model->origin_ke_sales_packages_id = $request->origin_ke_sales_packages_id;
            $model->contract_code = $request->contract_code;
            $model->sale_id = $request->user()->id;
            $user = $request->user()->user;
            if ($user) {
                $model->leader_id = $user->position->is_leader ? $user->position_id : $user->dependent_position_id;
            } else {
                $model->leader_id = $user->position_id;
            }
            $model->contract_value = $request->contract_value;
            $model->payment_date = $request->payment_date;
            $model->payment_amount = $request->payment_amount;
            $model->unequal = $request->unequal;
            $model->received_money = $request->received_money;
            $model->debt = $request->payment_amount - $request->received_money;
            $model->start_date = $request->start_date;
            $model->end_date = $request->end_date;
            $model->province_id = $request->province_id;
            $model->district_id = $request->district_id;
            $model->save();

            # kiểm tra đẩy file đính kèm nếu có
            if ($request->hasFile('attachFiles')) {
                KEContract::storeAttachmentFiles($request->file('attachFiles'), $model->id);
            }

            if ($request->deletedAttachedAppendix) {
                KEContractAttachedAppendix::whereIn('id', $request->deletedAttachedAppendix)
                    ->delete();
            }

            if ($request->deletedAttachedAppendixItems) {
                KEContractAttachedAppendixDetails::whereIn('id', $request->deletedAttachedAppendixItems)
                    ->whereHas('parent', function ($subQuery) {
                        $subQuery;
                    })
                    ->delete();
            }

            if ($request->sale_package_attachment) {
                # save the option sale package
                $salePackageAttachmentIds = [];
                $salePackageAttachment = [];
                foreach ($request->sale_package_attachment as $item) {
                    $salePackageAttachment[] = [
                        'id' => Str::uuid()->toString(),
                        'ke_contract_id' => $model->id,
                        'ke_sales_packages_id' => $model->origin_ke_sales_packages_id,
                        'ke_sales_packages_attached_appendix_id' => $item['id'],
                        'ke_sales_packages_attached_appendix_code' => $item['code'],
                        'created_at' => Carbon::now(),
                        'updated_at' => Carbon::now(),
                    ];
                    $salePackageAttachmentIds[] = $item['id'];
                }

                if (array_diff(
                    $salePackageAttachmentIds,
                    $model->salesPackagesAttachment()
                        ->pluck('ke_sales_packages_attached_appendix_id')
                        ->toArray()
                )) {
                    KEContractSalePackageAttachment::where('ke_contract_id', $model->id)
                        ->delete();

                    if ($salePackageAttachment) {
                        DB::table('ke_contract_sale_package_attachment')->insert($salePackageAttachment);
                    }
                }
            }

            // phụ lục đính kèm
            $hasNewAttach = false;
            $attachedAppendixData = [];
            $attachedAppendixDetails = [];
            foreach ($request->attachedAppendix as $attachedAppendix) {
                $attachedAppendixId = "";
                if ($attachedAppendix['id']) {
                    $keContractAttachedAppendix = KEContractAttachedAppendix::where('id', $attachedAppendix['id'])
                        ->where('ke_contract_id', $model->id)
                        ->where('isExWareHouse', false)
                        ->whereDoesntHave('attachedDetails', function ($q) {
                            $q->where('quantity_shipped', '>', 0);
                        })
                        ->first();
                    if (!$keContractAttachedAppendix) continue;

                    $keContractAttachedAppendix->attached_appendix_name = $attachedAppendix['attached_appendix_name'];
                    $keContractAttachedAppendix->save();
                    $attachedAppendixId = $attachedAppendix['id'];
                } else {
                    $attachedAppendixItem = [
                        'id' => Str::uuid()->toString(),
                        'ke_contract_id' => $model->id,
                        'attached_appendix_name' => $attachedAppendix['attached_appendix_name'],
                        'isExWareHouse' => false,
                        'created_at' => Carbon::now(),
                        'updated_at' => Carbon::now(),
                    ];
                    $attachedAppendixData[] = $attachedAppendixItem;
                    $attachedAppendixId = $attachedAppendixItem['id'];
                    $hasNewAttach = true;
                }

                if ($attachedAppendixId) {
                    foreach ($attachedAppendix['attached_appendix_items'] as $item) {
                        if ($item['id']) {
                            KEContractAttachedAppendixDetails::where('id', $item['id'])
                                ->where('ke_contract_attached_appendix_id', $attachedAppendixId)
                                ->where('equipment_type_id', $item['equipment_type_id'])
                                ->where('quantity_shipped', 0)
                                ->update([
                                    'amount' => $item['amount'],
                                    'price' => $item['price'],
                                    'discount' => $item['discount'],
                                    'discount_type' => $item['discount_type']
                                ]);
                        } else {
                            $attachedAppendixDetails[] = [
                                'id' => Str::uuid()->toString(),
                                'ke_contract_attached_appendix_id' => $attachedAppendixId,
                                'equipment_type_id' => $item['equipment_type_id'],
                                'amount' => $item['amount'],
                                'quantity_shipped' => 0,
                                'price' => $item['price'],
                                'discount' => $item['discount'],
                                'discount_type' => $item['discount_type'],
                                'created_at' => Carbon::now(),
                                'updated_at' => Carbon::now(),
                            ];
                        }
                    }
                }
            }

            if ($attachedAppendixData) {
                DB::table('ke_contract_attached_appendix')->insert($attachedAppendixData);
            }

            if ($attachedAppendixDetails) {
                DB::table('ke_contract_attached_appendix_details')->insert($attachedAppendixDetails);
            }

            if ($request->current_payment_amount && (int)$request->current_payment_amount > 0) {
                ContractPaymentHistory::store(
                    $model->id,
                    $model->contract_code,
                    $origin_contract_value,
                    $origin_received_money,
                    $origin_debt,
                    $request->current_payment_amount,
                    $request->payment_note,
                    ContractPaymentHistory::KE_PRODUCT,
                    $request->hasFile('payment_file') ? $request->payment_file : null,
                    KEContract::KE_CONTRACT
                );
            }

            // lưu lịch sử feedback
            if ($request->feedbackObject) {
                ContractFeedbackHistory::store($model, ContractPaymentHistory::KE_PRODUCT, $request->feedbackObject);
            }

            ClientContact::store($request->client_id, $model->product_id);

            DB::commit();

            if ($hasNewAttach) {
                KEContract::sendRequestExWarehouseEmail($model->contract_code);
            }

            return response()->json(['message' => 'Cập nhật hợp đồng thành công!']);
        } catch (Exception $e) {
            DB::rollBack();
            Log::error('KEContractController edit: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function getSalesPackages()
    {
        $rs = SalesPackages::with([
            'attachedAppendix' => function ($q) {
                $q->orderBy('sort', 'asc');
            }
        ])
            ->orderBy('sort', 'asc')
            ->get();

        return response()->json($rs);
    }

    public function details(Request $request)
    {
        try {
            $model = KEContract::with([
                'clients',
                'salesPackages',
                'salesPackagesAttachment',
                'filesAttachment',
                'attachedAppendixs'  => function ($subQuery) {
                    $subQuery->orderBy('created_at', 'ASC');
                },
                'attachedAppendixs.attachedDetails'  => function ($subQuery) {
                    $subQuery->orderBy('created_at', 'ASC');
                },
                'attachedAppendixs.attachedDetails.equipmentType',
                'paymentHistory' => function ($subQuery) {
                    $subQuery->orderBy('created_at', 'DESC');
                },
                'paymentHistory.createdBy',
                'feedbackHistory'
            ])
                ->where('id', $request->id)
                ->first();
            if (!$model) {
                return response()->json(['message' => 'Dữ liệu không tồn tại'], 404);
            }

            // if ($request->user()->cannot('viewKEContractDetail', $model)) {
            //     return response()->json([], 403);
            // }

            return response()->json($model);
        } catch (Exception $e) {
            Log::error('KEContractController details: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function delete(Request $request)
    {
        DB::beginTransaction();
        try {
            $model = KEContract::find($request->id);
            if (!$model) {
                return response()->json(['message' => 'Dữ liệu không tồn tại'], 404);
            }

            if (auth()->user()->hasAnyPermission(['contract_delete'])) {
                return response()->json([], 403);
            }

            $model->filesAttachment()->delete();
            $model->attachedAppendixs()->delete();
            $model->feedbackHistory()->delete();
            $model->paymentHistory()->keProduct()->delete();
            $model->delete();
            DB::commit();
            return response()->json(['message' => 'Xóa hợp đồng thành công!']);
        } catch (Exception $e) {
            DB::rollBack();
            Log::error('KEContractController delete: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    /**
     * Xoá file đính kèm
     */
    public function destroyFile(Request $request)
    {
        try {
            $model = ModelsFile::find($request->id);
            unlink($model->path);
            $model->delete();

            return response()->json(['message' => 'Success'], 200);
        } catch (\Exception $e) {
            return $e;
        }
    }
}
