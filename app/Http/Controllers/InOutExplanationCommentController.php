<?php

namespace App\Http\Controllers;

use App\Models\Position;
use Illuminate\Http\Request;
use App\Models\Response;
use App\Models\InOutExplanationComment;
use Illuminate\Support\Facades\DB;

class InOutExplanationCommentController extends Controller
{
    public function store(Request $request)
    {
        $request->validate([
            'in_out_explanations_id' => 'required',
            'content' => 'required',
            'created_by' => 'required',
            'created_by_title' => 'required'
        ]);

        try {
            $params = $request->all();
            InOutExplanationComment::create($params);
            return Response::formatResponse(config('apicode.SUCCESS'), $params);
        } catch (\Exception $e) {
            return Response::formatResponse(config('apicode.INTERNAL_SERVER_ERROR'), $e->getMessage());
        }
    }

    public function addCommentMobile(Request $request)
    {
        try {
            $request->validate([
                'in_out_explanations_id' => 'required',
                'content' => 'required',
            ]);
            $userName = auth()->user()->name;
            $position = Position::year(date('Y'))->select('id', 'name')->where('id', auth()->user()->user->position_id)->first();
            $createdByTitle = $userName . ' - ' . $position->name;
            $params = [
                'in_out_explanations_id' => $request->in_out_explanations_id,
                'content' => $request->content,
                'created_by_title' => $createdByTitle,
            ];
            InOutExplanationComment::create($params);
            return Response::formatResponse(config('apicode.SUCCESS'), $params);
        } catch (\Exception $e) {
            return Response::formatResponse(config('apicode.INTERNAL_SERVER_ERROR'), $e->getMessage());
        }
    }
}
