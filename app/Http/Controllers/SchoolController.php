<?php

namespace App\Http\Controllers;

use App\Models\Response;
use App\Models\School;
use App\Models\TeamSaleArea;
use App\Models\UserWorking;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use PhpOffice\PhpSpreadsheet\Style;

class SchoolController extends Controller
{
    public function index(Request $request)
    {
        $data = School::with('province', 'district', 'school_level')
            ->when($request->province_id, function ($subQuery, $provinceId) {
                return $subQuery->where('province_id', $provinceId);
            })
            ->when($request->district_id, function ($subQuery, $districtId) {
                return $subQuery->where('district_id', $districtId);
            })
            ->when($request->school_name, function ($subQuery, $schoolName) {
                return $subQuery->where('name', 'like', '%' . $schoolName . '%')->orWhere('school_id', 'like', '%' . $schoolName . '%');
            })
            ->when($request->school_level_id, function ($subQuery, $schoolLevel) {
                return $subQuery->where('school_level_id', $schoolLevel);
            })
            ->orderBy('province_id', 'ASC')
            ->orderBy('district_id', 'ASC')
            ->orderBy('school_id', 'ASC')
            ->paginate(20);

        return Response::formatResponse(config('apicode.SUCCESS'), $data);
    }

    /**
     * Xuất excel báo cáo cuộc gọi khách hàng
     */
    public function exportExcel(Request $request)
    {
        # phần này là đọc từ 1 file template đã định dạng đẹp từ trước
        $inputFileName = 'templates/list_schools.xls';
        $fileType = \PhpOffice\PhpSpreadsheet\IOFactory::identify($inputFileName);
        $objReader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader($fileType);
        $objPHPExcel = $objReader->load($inputFileName);
        $sheet = $objPHPExcel->setActiveSheetIndex(0);

        $query = School::with('province', 'district', 'school_level')->orderBy('province_id', 'ASC')->orderBy('district_id', 'ASC')->orderBy('school_id', 'ASC');
        if (!empty($request->province_id)) {
            $query->province($request->province_id);
        }
        if (!empty($request->district_id)) {
            $query->district($request->district_id);
        }
        if (!empty($request->school_name)) {
            $query->schoolNameLike($request->school_name);
        }
        if (!empty($request->school_group_level_id)) {
            $query->schoolLevel($request->school_group_level_id);
        }
        $listSchools = $query->get();
        $row = 6;
        foreach ($listSchools as $k => $school) {
            $sheet->setCellValue('A' . $row, $k + 1);
            $sheet->setCellValue('B' . $row, $school->school_id);
            $sheet->setCellValue('C' . $row, $school->name);
            $sheet->setCellValue('D' . $row, $school->school_level->name ? $school->school_level->name : '');
            $sheet->setCellValue('E' . $row, $school->province->name ? $school->province->name : '');
            $sheet->setCellValue('F' . $row, $school->district->name ? $school->district->name : '');
            $sheet->setCellValue('G' . $row, $school->address);
            $row++;
        }

        $sheet->getStyle('A5:G' . ($row - 1))->getBorders()->getAllBorders()->setBorderStyle(Style\Border::BORDER_THIN);
        $objWriter = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($objPHPExcel, $fileType);
        try {
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename="Danh sach truong.xls"');
            header('Cache-Control: max-age=0');
            $objWriter->save("php://output");
        } catch (\Exception $exception) {
            throw $exception;
        }
    }

    public function details(Request $request)
    {
        try {
            $school = School::find($request->id);
            if (!$school) {
                return response()->json([], 404);
            }
            return response()->json($school);
        } catch (Exception $e) {
            Log::error('SchoolController details: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function store(Request $request)
    {
        $request->validate([
            'province_id' => 'required',
            'school_id' => $request->id ? 'required|max:255' : 'required|max:255|unique:m_schools,school_id',
            'name' => 'required|max:255',
            'school_form_type_id' => 'required|max:255',
            'school_type_id' => 'required|max:255',
            'school_group_level_id' => 'required|max:255',
            'school_level_id' => 'required',
            'note' => 'max:500'
        ], [], [
            'province_id' => 'Tỉnh/Thành phố',
            'school_id' => 'Mã trường',
            'name' => 'Tên trường',
            'school_form_type_id' => 'Loại hình trường',
            'school_type_id' => 'Loại trường',
            'school_group_level_id' => 'Nhóm cấp học',
            'school_level_id' => 'Cấp học',
            'note' => 'Ghi chú'
        ]);

        try {
            $model = new School();
            if ($request->id) {
                $model = School::find($request->id);
                if (!$model) {
                    return response()->json(['message' => 'Dữ liệu không tồn tại!'], 400);
                }
            }
            $model->province_id = $request->province_id;
            $model->district_id = $request->district_id;

            if (!$request->id) {
                $model->school_id = $request->school_id;
            }
            $model->name = trim($request->name);
            $model->name_upper = mb_strtoupper(trim($request->name));
            $model->address = $request->address;
            $model->school_form_type_id = $request->school_form_type_id;
            $model->school_type_id = $request->school_type_id;
            $model->school_group_level_id = $request->school_group_level_id;
            $model->school_level_id = $request->school_level_id;
            $model->note = $request->note;
            $model->save();
            return response()->json($model);
        } catch (Exception $e) {
            Log::error('SchoolController store: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function getSchoolFormType()
    {
        $rs = DB::table('m_school_form_type')
            ->select('school_form_type_id as id', 'name')
            ->orderBy('sort')
            ->get();
        return response()->json($rs);
    }

    public function getSchoolType()
    {
        $rs = DB::table('m_school_type')
            ->select('school_type_id as id', 'name')
            ->orderBy('sort')
            ->get();
        return response()->json($rs);
    }

    public function getSchoolLevel()
    {
        $rs = DB::table('m_school_level')
            ->select('level as id', 'name')
            ->orderBy('sort')
            ->get();
        return response()->json($rs);
    }

    public function getSchoolGroupLevel(Request $request)
    {
        $rs = DB::table('m_school_group_level')
            ->select('school_group_level_id as id', 'name')
            ->when($request->school_level, function ($query, $schoolLevel) {
                $query->where('school_level', $schoolLevel);
            })
            ->orderBy('sort')
            ->get();
        return response()->json($rs);
    }
}
