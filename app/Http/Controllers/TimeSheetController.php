<?php

namespace App\Http\Controllers;

use App\Models\User;
use Carbon\Carbon;
use Clockwork\Request\Log;
use Illuminate\Http\Request;
use App\Models\Response;
use App\Models\TimeSheet;
use App\Models\InOutExplanation;
use App\Models\WorkDaySymbol;
use App\Models\UserWorking;
use App\Models\InOutGeneral;
use Illuminate\Support\Facades\DB;
use PhpOffice\PhpSpreadsheet\Style;
use Exception;

use App\Transformer\APIJsonResponse;
use App\Transformer\TimeSheetTransformer;
use App\Services\FirebaseNotification;
use App\Jobs\SendNotifyToAppJob;

class TimeSheetController extends Controller
{
    public function index(Request $request)
    {
        $rs = (object)[];
        # danh mục công
        $workDaySymbol = WorkDaySymbol::getAllWorkDaySymbol();

        # Phần head table
        $month = !empty($request->month) ? date_format(date_create('01-' . $request->month), 'Y-m') : date('Y-m');
        $dateInMonth = [];
        for ($i = 1; $i <= 31; $i++) {
            $tmpDate = $i < 10 ? $month . '-0' . $i : $month . '-' . $i;
            if (checkdate((date_format(date_create($month . '-01'), 'm') + 0), $i, date_format(date_create($month . '-01'), 'Y'))) {
                $dateInMonth[] = [
                    'date' => date_format(date_create($tmpDate), 'd'),
                    'name' => date_format(date_create($tmpDate), 'N'),
                ];
            }
        }
        # danh sách nhân sự
        $p_month = date_format(date_create($month . '-01'), 'm');
        $p_year = date_format(date_create($month . '-01'), 'Y');
        $department_id = $request->department_id ?? null;
        $rank_code = $request->rank_code ?? null;
        $userLists = TimeSheet::year($p_year)
            ->month($p_month)
            ->department($department_id)
            ->when($rank_code, function ($q, $rankCode) {
                $q->whereHas('position', function ($qr) use ($rankCode) {
                    $qr->where('code', $rankCode);
                });
            })
            ->with(
                'user:id,name,staff_code,start_date_of_work,end_date_of_probationary,end_date_of_work',
                'department:id,code',
                'position:id,code',
                'user.user.employmentContract:id,name',
                'user.absence_years'
            )
            ->orderBy('department_id')
            ->orderBy('position_id')
            ->orderBy('user_id')
            ->groupBy('user_id')
            ->paginate(20);

        $rs->dateInMonth = $dateInMonth;
        $rs->userLists = $userLists;
        $rs->workDaySymbol = $workDaySymbol;
        return Response::formatResponse(config('apicode.SUCCESS'), $rs);
    }

    public function show(Request $request, $id)
    {
        $date = explode('/', $request->month);
        $month = $date[0];
        $year = $date[1];
        $rs = TimeSheet::year($year)
            ->month($month)
            ->where('user_id', $id)
            ->with(
                'user:id,name,staff_code,start_date_of_work,end_date_of_probationary,end_date_of_work',
                'department:id,code',
                'position:id,code',
                'user.user.employmentContract:id,name',
                'user.absence_years'
            )->first();
        return $rs;
    }

    public function listCheckInCheckOut(Request $request)
    {
        $date = !empty($request->date) ? $request->date : date('Y-m-d');
        $department_id = $request->department_id ?? null;
        $rank_code = $request->rank_code ?? null;
        $user_id = null;
        if (auth()->user()->user) {
            $user_id = auth()->user()->user->position->is_leader == null ? auth()->user()->id : null;
        }

        $roles = auth()->user()->getRoleNames();
        $roleName = $roles[0] ?: null;
        if ($roleName == 'rd_leader' || $roleName == 'hr') {
            $user_id = null;
        }

        # list danh sách nhân sự đang active hiện tại
        $userLists = UserWorking::select(
            'user_workings.*',
            DB::raw("(SELECT MIN(`date`) FROM in_outs WHERE `date` like '$date%' AND hash != 'hash' AND create_by = 1 AND aliasID = (SELECT staff_code FROM users WHERE id = user_workings.user_id)) checkin"),
            DB::raw("(SELECT MAX(`date`) FROM in_outs WHERE `date` like '$date%' AND hash != 'hash' AND create_by = 1 AND aliasID = (SELECT staff_code FROM users WHERE id = user_workings.user_id)) checkout")
        )
            ->notAdmin()
            ->department($department_id)
            ->when($rank_code, function ($q, $rankCode) {
                $q->whereHas('position', function ($qr) use ($rankCode) {
                    $qr->where('code', $rankCode);
                });
            })->when($user_id, function ($q, $userId) {
                $q->onlyMe($userId);
            })
            ->allActive()
            ->with([
                'user',
                'department',
                'position',
                'explanation' => function ($query) use ($date) {
                    $query->where('date', $date);
                },
                'explanation.explanationComment'
            ])
            ->orderBy('department_id')
            ->orderBy('position_id')
            ->orderBy('user_id')
            ->groupBy('user_id')
            ->paginate(20);

        return Response::formatResponse(config('apicode.SUCCESS'), $userLists);
    }

    public function listCheckInCheckOutReject(Request $request)
    {
        $date = !empty($request->date) ? $request->date : date('Y-m-d');
        $department_id = $request->department_id ?? null;
        $rank_code = $request->rank_code ?? null;
        $user_id = null;
        if (auth()->user()->user) {
            $user_id = auth()->user()->user->position->is_leader == null ? auth()->user()->id : null;
        }

        $roles = auth()->user()->getRoleNames();
        $roleName = $roles[0] ?: null;
        if ($roleName == 'rd_leader' || $roleName == 'hr') {
            $user_id = null;
        }

        # list danh sách nhân sự đang active hiện tại
        $userLists = UserWorking::select(
            'user_workings.*',
            DB::raw("(SELECT MIN(`date`) FROM in_outs WHERE `date` like '$date%' AND hash != 'hash' AND create_by = 1 AND aliasID = (SELECT staff_code FROM users WHERE id = user_workings.user_id)) checkin"),
            DB::raw("(SELECT MAX(`date`) FROM in_outs WHERE `date` like '$date%' AND hash != 'hash' AND create_by = 1 AND aliasID = (SELECT staff_code FROM users WHERE id = user_workings.user_id)) checkout"),
            DB::raw("(SELECT MIN(`checkin`) FROM in_outs_online WHERE `checkin` like '$date%' AND user_id = user_workings.user_id) checkin_online"),
            DB::raw("(SELECT MAX(`checkout`) FROM in_outs_online WHERE `checkout` like '$date%' AND user_id = user_workings.user_id) checkout_online")
        )
            ->notAdmin()
            ->department($department_id)
            ->when($rank_code, function ($q, $rankCode) {
                $q->whereHas('position', function ($qr) use ($rankCode) {
                    $qr->where('code', $rankCode);
                });
            })->when($user_id, function ($q, $userId) {
                $q->onlyMe($userId);
            })
            ->allActive()
            ->with([
                'user',
                'department',
                'position',
                'explanation' => function ($query) use ($date) {
                    $query->where('date', $date);
                },
                'explanation.explanationComment'
            ])
            ->orderBy('department_id')
            ->orderBy('position_id')
            ->orderBy('user_id')
            ->groupBy('user_id')
            ->paginate(20);

        return Response::formatResponse(config('apicode.SUCCESS'), $userLists);
    }

    public function exportCheckInCheckOut(Request $request)
    {
        $date = !empty($request->date) ? $request->date : date('Y-m-d');
        $month = date_format(date_create($date), 'Y-m');
        $departmentId = $request->department_id ?? null;
        $rankCode = $request->rank_code ?? null;

        $userLists = UserWorking::notAdmin()
            ->department($departmentId)
            ->when($rankCode, function ($q, $rankCode) {
                $q->whereHas('position', function ($qr) use ($rankCode) {
                    $qr->where('code', $rankCode);
                });
            })
            ->allActive()
            ->with([
                'user:id,name,staff_code,email',
                'department:id,code',
                'position:id,code',
            ])
            ->orderBy('department_id')
            ->orderBy('position_id')
            ->orderBy('user_id')
            ->groupBy('user_id')
            ->get();
        # du lieu in out
        //$data = InOutGeneral::where('date', 'like', $month . '%')->whereRaw('((((TIME(check_in) > "08:10:00" AND TIME(check_in) < "12:00:00") OR TIME(check_in) > "13:00:00")) OR TIME(check_out) < "17:00:00")')->get();
        $data = InOutGeneral::where('date', 'like', $month . '%')->get();
        $dataInOut = vietec_convert_array_to_map($data, 'user_id', true);

        $inputFileName = 'templates/checkinoutmonth.xls';
        $fileType = \PhpOffice\PhpSpreadsheet\IOFactory::identify($inputFileName);
        $objReader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader($fileType);
        $objPHPExcel = $objReader->load($inputFileName);
        $sheet = $objPHPExcel->setActiveSheetIndex(0);
        $sheet->setCellValue("B2", "DỮ LIỆU CHẤM CÔNG - THÁNG: " . date_format(date_create($month), 'm-Y'));
        $j = 6;
        $k = 0;
        foreach ($userLists as $item) {
            $tmpDataInOut = $dataInOut[$item->user_id] ?? [];
            if (!$tmpDataInOut) {
                continue;
            }
            foreach ($tmpDataInOut as $tmpItem) {
                $timeIn = Carbon::createFromFormat('H:i:s', "08:10:00");
                $timeInAfternoon = Carbon::createFromFormat('H:i:s', "13:00:00");
                $checkIn = Carbon::createFromFormat('H:i:s', date_format(date_create($tmpItem->check_in), 'H:i:s'));

                $timeOut = Carbon::createFromFormat('H:i:s', "17:00:00");
                $timeOutMorning = Carbon::createFromFormat('H:i:s', "11:59:59");
                $checkOut = Carbon::createFromFormat('H:i:s', date_format(date_create($tmpItem->check_out), 'H:i:s'));

                $sheet->setCellValue("B" . ($j + $k), date_format(date_create($tmpItem->date), 'd-m-Y'));
                $sheet->setCellValue("C" . ($j + $k), $item->user->name);
                $sheet->setCellValue("D" . ($j + $k), date_format(date_create($tmpItem->check_in), 'H:i:s'));
                $sheet->setCellValue("E" . ($j + $k), date_format(date_create($tmpItem->check_out), 'H:i:s'));
                $sheet->setCellValue("I" . ($j + $k), $item->user->email);
                if ($checkIn->greaterThan($timeIn)) {
                    if ($checkIn->greaterThan($timeInAfternoon)) {
                        $difference = $checkIn->diff($timeInAfternoon);
                        if ($difference->format('%H') > 0) {
                            $startTime = Carbon::createFromTime($timeInAfternoon->hour, $timeInAfternoon->minute, $timeInAfternoon->second);
                            $endTime = Carbon::createFromTime($checkIn->hour, $checkIn->minute, $checkIn->second);
                            $sheet->setCellValue("F" . ($j + $k), $startTime->diffInMinutes($endTime));
                            if ($startTime->diffInMinutes($endTime) > 60) {
                                $sheet->setCellValue("H" . ($j + $k), "kiểm tra lại");
                            }
                        } else {
                            $sheet->setCellValue("F" . ($j + $k), $difference->format('%I'));
                        }
                    } else {
                        $difference = $checkIn->diff($timeIn);
                        if ($checkIn->greaterThan($timeOutMorning) && $checkIn->lessThan($timeInAfternoon)) {
                            $sheet->setCellValue("F" . ($j + $k), '');
                        } else {
                            if ($difference->format('%H') > 0) {
                                $startTime = Carbon::createFromTime($timeIn->hour, $timeIn->minute, $timeIn->second);
                                $endTime = Carbon::createFromTime($checkIn->hour, $checkIn->minute, $checkIn->second);
                                $sheet->setCellValue("F" . ($j + $k), $startTime->diffInMinutes($endTime));
                                if ($startTime->diffInMinutes($endTime) > 60) {
                                    $sheet->setCellValue("H" . ($j + $k), "kiểm tra lại");
                                }
                            } else {
                                $sheet->setCellValue("F" . ($j + $k), $difference->format('%I'));
                            }
                        }
                    }
                }

                if ($checkOut->lessThan($timeOut)) {
                    if ($checkOut->lessThan($timeOutMorning)) {
                        $difference = $checkOut->diff($timeOutMorning);
                        if ($difference->format('%H') > 0) {
                            $startTime = Carbon::createFromTime($checkOut->hour, $checkOut->minute, $checkOut->second);
                            $endTime = Carbon::createFromTime($timeOutMorning->hour, $timeOutMorning->minute, $timeOutMorning->second);
                            $sheet->setCellValue("G" . ($j + $k), $startTime->diffInMinutes($endTime));
                            if ($startTime->diffInMinutes($endTime) > 60) {
                                $sheet->setCellValue("H" . ($j + $k), "kiểm tra lại");
                            }
                        } else {
                            $sheet->setCellValue("G" . ($j + $k), $difference->format('%I'));
                        }
                    } else {
                        $difference = $checkOut->diff($timeOut);
                        if ($checkOut->lessThan($timeInAfternoon) && $checkOut->greaterThan($timeOutMorning)) {
                            $sheet->setCellValue("G" . ($j + $k), '');
                        } else {
                            if ($difference->format('%H') > 0) {
                                $startTime = Carbon::createFromTime($checkOut->hour, $checkOut->minute, $checkOut->second);
                                $endTime = Carbon::createFromTime($timeOut->hour, $timeOut->minute, $timeOut->second);
                                $sheet->setCellValue("G" . ($j + $k), $startTime->diffInMinutes($endTime));
                                if ($startTime->diffInMinutes($endTime) > 60) {
                                    $sheet->setCellValue("H" . ($j + $k), "kiểm tra lại");
                                }
                            } else {
                                $sheet->setCellValue("G" . ($j + $k), $difference->format('%I'));
                            }
                        }
                    }
                }

                $k++;
            }
            $j++;
        }

        $sheet->getStyle('A5:I' . ($j + $k))->getBorders()->getAllBorders()->setBorderStyle(Style\Border::BORDER_THIN);
        $objWriter = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($objPHPExcel, $fileType);
        try {
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename="bang_cong.xls"');
            header('Cache-Control: max-age=0');
            $objWriter->save("php://output");
        } catch (Exception $exception) {
            throw $exception;
        }
    }

    #Hàm dưới là hàm cũ hiện tại ko dùng
    public function exportExcelCheckInCheckOut(Request $request)
    {
        $date = !empty($request->date) ? $request->date : date('Y-m-d');
        $month = date_format(date_create($date), 'Y-m');
        $departmentId = $request->department_id ?? null;
        $rankCode = $request->rank_code ?? null;

        $userLists = UserWorking::notAdmin()
            ->department($departmentId)
            ->when($rankCode, function ($q, $rankCode) {
                $q->whereHas('position', function ($qr) use ($rankCode) {
                    $qr->where('code', $rankCode);
                });
            })
            ->allActive()
            ->with([
                'user:id,name,staff_code',
                'department:id,code',
                'position:id,code',
            ])
            ->orderBy('department_id')
            ->orderBy('position_id')
            ->orderBy('user_id')
            ->groupBy('user_id')
            ->get();
        # du lieu in out
        $data = InOutGeneral::where('date', 'like', $month . '%')->get();
        $dataInOut = vietec_convert_array_to_map($data, 'user_id', true);

        $inputFileName = 'templates/checkinout.xls';
        $fileType = \PhpOffice\PhpSpreadsheet\IOFactory::identify($inputFileName);
        $objReader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader($fileType);
        $objPHPExcel = $objReader->load($inputFileName);
        $sheet = $objPHPExcel->setActiveSheetIndex(0);

        $sheet->setCellValue("A2", "DỮ LIỆU CHẤM CÔNG - THÁNG: " . $month);

        $columns = ['E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', 'AA', 'AB', 'AC', 'AD', 'AE', 'AF', 'AG', 'AH', 'AI', 'AJ', 'AK', 'AL', 'AM', 'AN', 'AO', 'AP', 'AQ', 'AR', 'AS', 'AT', 'AU', 'AV', 'AW', 'AX', 'AY', 'AZ', 'BA', 'BB', 'BC', 'BD', 'BE', 'BF', 'BG', 'BH', 'BI', 'BJ', 'BK', 'BL', 'BM', 'BN', 'BO'];
        $j = 5;
        foreach ($userLists as $k => $item) {
            $j++;
            $sheet->setCellValue("A" . $j, ($k + 1));
            $sheet->setCellValue("B" . $j, $item->user->name ?: '');
            $sheet->setCellValue("C" . $j, $item->user->staff_code ?: '');
            $sheet->setCellValue("D" . $j, $item->department->code ?: '');
            $sheet->setCellValue("E" . $j, $item->position->code ?: '');

            $tmpDataInOut = $dataInOut[$item->user_id] ?? [];
            if (!$tmpDataInOut) {
                continue;
            }
            foreach ($tmpDataInOut as $tmpItem) {
                $tmpDate = $tmpItem->date;
                $dateIndex = date_format(date_create($tmpDate), 'j');
                $tmpColumnIn = ($dateIndex * 2) - 1;
                $tmpColumnOut = $tmpColumnIn + 1;
                $sheet->setCellValue($columns[$tmpColumnIn] . $j, date_format(date_create($tmpItem->check_in), 'H:i:s'));
                $sheet->setCellValue($columns[$tmpColumnOut] . $j, date_format(date_create($tmpItem->check_out), 'H:i:s'));
            }
        }

        $sheet->getStyle('A5:BO' . $j)->getBorders()->getAllBorders()->setBorderStyle(Style\Border::BORDER_THIN);
        $objWriter = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($objPHPExcel, $fileType);
        try {
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename="bang_cong.xls"');
            header('Cache-Control: max-age=0');
            $objWriter->save("php://output");
        } catch (Exception $exception) {
            throw $exception;
        }
    }

    public function exportExcel(Request $request)
    {
        $inputFileName = 'templates/timesheets.xls';
        $fileType = \PhpOffice\PhpSpreadsheet\IOFactory::identify($inputFileName);
        $objReader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader($fileType);
        $objPHPExcel = $objReader->load($inputFileName);
        $sheet = $objPHPExcel->setActiveSheetIndex(0);

        # danh mục công
        $workDaySymbol = WorkDaySymbol::getAllWorkDaySymbol();

        # Phần head table
        $month = !empty($request->month) ? date_format(date_create('01-' . $request->month), 'Y-m') : date('Y-m');
        $dateInMonth = [];
        for ($i = 1; $i <= 31; $i++) {
            $tmpDate = $i < 10 ? $month . '-0' . $i : $month . '-' . $i;
            if (checkdate((date_format(date_create($month . '-01'), 'm') + 0), $i, date_format(date_create($month . '-01'), 'Y'))) {
                $dateInMonth[] = [
                    'date' => date_format(date_create($tmpDate), 'd'),
                    'name' => date_format(date_create($tmpDate), 'N'),
                ];
            }
        }
        # danh sách nhân sự
        $p_month = date_format(date_create($month . '-01'), 'm');
        $p_year = date_format(date_create($month . '-01'), 'Y');
        $department_id = $request->department_id ? $request->department_id : '';
        $userLists = TimeSheet::year($p_year)
            ->month($p_month)
            ->with([
                'user:id,name,staff_code,start_date_of_work,end_date_of_probationary,end_date_of_work',
                'position:id,name',
                'user.user.employmentContract:id,name',
                'user.endOfWorking',
                'user.absence_years'
            ])
            ->orderBy('department_id')
            ->orderBy('dependent_position_id')
            ->orderBy('position_id')
            ->orderBy('user_id')
            ->groupBy('user_id')
            ->get()
            ->toArray();

        $arr_columns = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', 'AA', 'AB', 'AC', 'AD', 'AE', 'AF', 'AG', 'AH', 'AI', 'AJ', 'AK', 'AL', 'AM', 'AN', 'AO', 'AP', 'AQ', 'AR', 'AS', 'AT', 'AU', 'AV', 'AW', 'AX', 'AY', 'AZ', 'BA', 'BB', 'BC', 'BD', 'BE'];

        $i = 8;
        foreach ($dateInMonth as $item) {
            $sheet->setCellValue($arr_columns[$i] . "3", $item['date']);
            $sheet->setCellValue($arr_columns[$i] . "4", format_index_date($item['name']));
            $i++;
        }

        $newUserLists = [
            UserWorking::COMPANY_VIETEC => [],
            UserWorking::COMPANY_GOKIDS => [],
            UserWorking::COMPANY_METAKIDS => [],
            UserWorking::COMPANY_KIDSENGLISH => [],
            UserWorking::COMPANY_LANGUAGEHUB => [],
        ];
        foreach ($userLists as $k => $item) {
            if ($item['user']['end_of_working']['company'] == UserWorking::COMPANY_VIETEC) {
                $newUserLists[UserWorking::COMPANY_VIETEC][] = $item;
            } else if ($item['user']['end_of_working']['company'] == UserWorking::COMPANY_GOKIDS) {
                $newUserLists[UserWorking::COMPANY_GOKIDS][] = $item;
            } else if ($item['user']['end_of_working']['company'] == UserWorking::COMPANY_METAKIDS) {
                $newUserLists[UserWorking::COMPANY_METAKIDS][] = $item;
            } else if ($item['user']['end_of_working']['company'] == UserWorking::COMPANY_KIDSENGLISH) {
                $newUserLists[UserWorking::COMPANY_KIDSENGLISH][] = $item;
            } else if ($item['user']['end_of_working']['company'] == UserWorking::COMPANY_LANGUAGEHUB) {
                $newUserLists[UserWorking::COMPANY_LANGUAGEHUB][] = $item;
            }
        }

        $j = 7;
        foreach ($newUserLists as $key => $value) {

            $sheet->setCellValue("A" . $j, UserWorking::COMPANY[$key]);
            $j++;

            foreach ($value as $k => $item) {
                $sheet->setCellValue("A" . $j, ($k + 1));
                $sheet->setCellValue("B" . $j, $item['user']['name'] ?: '');
                $sheet->setCellValue("C" . $j, $item['user']['staff_code'] ?: '');
                $sheet->setCellValue("D" . $j, ($item['position'] && $item['position']['name']) ? $item['position']['name'] : '');
                $sheet->setCellValue("E" . $j, $item['user']['start_date_of_work'] ?: '');
                $sheet->setCellValue("F" . $j, $item['user']['end_date_of_probationary'] ?: '');
                $sheet->setCellValue("G" . $j, $item['user']['end_date_of_work'] ?: '');
                $sheet->setCellValue("H" . $j, ($item['user'] && $item['user']['user'] && $item['user']['user']['employment_contract']) ? $item['user']['user']['employment_contract']['name'] : '');
                $sheet->setCellValue("AN" . $j, $item['total_study_work'] ?: '');
                $sheet->setCellValue("AO" . $j, $item['total_probationary'] ?: '');
                $sheet->setCellValue("AP" . $j, $item['total_real'] ?: '');
                $sheet->setCellValue("AQ" . $j, $item['total_online1'] ?: '');
                $sheet->setCellValue("AR" . $j, $item['total_online2'] ?: '');
                $sheet->setCellValue("AS" . $j, $item['total_online_probationary'] ?: '');
                $sheet->setCellValue("AT" . $j, $item['total_bussiness'] ?: '');
                $sheet->setCellValue("AU" . $j, $item['total_bussiness_probationary'] ?: '');
                $sheet->setCellValue("AV" . $j, $item['total_holiday_absence'] ?: '');
                $sheet->setCellValue("AW" . $j, $item['total_salary'] ?: '');
                $sheet->setCellValue("AX" . $j, $item['total_no_salary'] ?: '');
                $sheet->setCellValue("AY" . $j, $item['total_lunch'] ?: '');
                $sheet->setCellValue("AZ" . $j, $item['union_fund'] ?: '');
                $sheet->setCellValue("BA" . $j, $item['user']['absence_years'] ? $item['user']['absence_years']['absence_year_val'] : '');
                $sheet->setCellValue("BB" . $j, $item['total_social_insurance'] ?: '');
                $columnIndex = 8;
                foreach ($dateInMonth as $date) {
                    if ($date['name'] != 6 && $date['name'] != 7) {
                        if (isset($workDaySymbol[$item['day_' . $date['date']]])) {
                            $sheet->setCellValue($arr_columns[$columnIndex] . $j, ($workDaySymbol[$item['day_' . $date['date']]] && $workDaySymbol[$item['day_' . $date['date']]]['symbol']) ? $workDaySymbol[$item['day_' . $date['date']]]['symbol'] : '');
                        }
                    }
                    $columnIndex++;
                }
                $j++;
            }
        }

        $sheet->getStyle('A5:BB' . ($j - 1))->getBorders()->getAllBorders()->setBorderStyle(Style\Border::BORDER_THIN);
        $objWriter = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($objPHPExcel, $fileType);
        try {
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename="bang_cong.xls"');
            header('Cache-Control: max-age=0');
            $objWriter->save("php://output");
        } catch (Exception $exception) {
            throw $exception;
        }
    }

    public function explainMiss(Request $request)
    {
        $month = $request->month;
        $department_id = $request->department_id ?? null;
        $rank_code = $request->rank_code ?? null;
        $user_id = auth()->user()->id;
        try {
            # list danh sách nhân sự đang active hiện tại
            $userLists = UserWorking::select(
                'user_workings.*'
            )
                ->notAdmin()
                ->notMe($user_id)
                ->department($department_id)
                ->when($rank_code, function ($q, $rankCode) {
                    $q->whereHas('position', function ($qr) use ($rankCode) {
                        $qr->where('code', $rankCode);
                    });
                })
                ->allActive()
                ->with([
                    'user',
                    'department',
                    'position',
                    'explanations' => function ($query) use ($month) {
                        $query->whereRaw("date LIKE '{$month}%'")->orderBy('status')->orderBy('date', 'DESC');
                    },
                    'explanations.confirm'
                ])
                ->orderBy('department_id')
                ->orderBy('position_id')
                ->orderBy('user_id')
                ->groupBy('user_id')
                ->paginate(20);

            return Response::formatResponse(config('apicode.SUCCESS'), $userLists);
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('TimeSheetController update: ' . $e->getMessage());
            return response()->json(['message' => 'Xảy ra lỗi trong quá trình xử lý'], 500);
        }
    }

    public function listCheckInCheckOutMobile(Request $request)
    {
        try {
            $date = !empty($request->date) ? $request->date : date('Y-m-d');
            $department_id = $request->department_id ?? null;
            $rank_code = $request->rank_code ?? null;
            $user_id = null;
            if (auth()->user()->user) {
                $user_id = auth()->user()->user->position->is_leader == null ? auth()->user()->id : null;
            }

            # list danh sách nhân sự đang active hiện tại
            $userLists = UserWorking::select(
                'user_workings.*',
                DB::raw("(SELECT MIN(`date`) FROM in_outs WHERE `date` like '$date%' AND create_by = 1 AND hash != 'hash' AND aliasID = (SELECT staff_code FROM users WHERE id = user_workings.user_id)) checkin"),
                DB::raw("(SELECT MAX(`date`) FROM in_outs WHERE `date` like '$date%' AND create_by = 1 AND hash != 'hash' AND aliasID = (SELECT staff_code FROM users WHERE id = user_workings.user_id)) checkout")
            )
                ->notAdmin()
                ->department($department_id)
                ->when($rank_code, function ($q, $rankCode) {
                    $q->whereHas('position', function ($qr) use ($rankCode) {
                        $qr->where('code', $rankCode);
                    });
                })->when($user_id, function ($q, $userId) {
                    $q->onlyMe($userId);
                })
                ->allActive()
                ->with([
                    'user',
                    'department',
                    'position',
                    'explanation' => function ($query) use ($date) {
                        $query->where('date', $date);
                    },
                    'explanation.explanationComment'
                ])
                ->orderBy('department_id')
                ->orderBy('position_id')
                ->orderBy('user_id')
                ->groupBy('user_id')->get();
            $rs = (new APIJsonResponse)->responseSuccess((new TimeSheetTransformer)->transforms($userLists, $date));
        } catch (\Exception $e) {
            $rs = (new APIJsonResponse)->responseError();
        }

        return $rs;
    }

    // index mobile

    public function indexMobile(Request $request)
    {
        try {
            $request->validate([
                'date_time' => 'required | date_format:m-Y'
            ]);
            $dateTime = Carbon::createFromFormat('m-Y', $request->date_time);
            # Phần head table
            # danh sách nhân sự
            $department_id = $request->department_id ?? null;
            $rank_code = $request->rank_code ?? null;
            $userId = auth()->user()->id;
            $holidayRemaining = auth()->user()->absence_years->absence_year_val ?? 0;
            $rs = TimeSheet::year($dateTime->year)
                ->month($dateTime->month)
                ->department($department_id)
                ->when($rank_code, function ($q, $rankCode) {
                    $q->whereHas('position', function ($qr) use ($rankCode) {
                        $qr->where('code', $rankCode);
                    });
                })
                ->where('user_id', $userId)
                ->orderBy('department_id')
                ->orderBy('position_id')
                ->orderBy('user_id')
                ->groupBy('user_id')->first();
            if (!isset($rs) || $rs == null) {
                return (new APIJsonResponse)->responseError(404, 'Không tìm thấy dữ liệu');
            }
            $workDays = [];
            $workDaySymbol = WorkDaySymbol::getAllSymbols();
            for ($i = 0; $i < 31; $i++) {
                $key = 'day_' . ($i + 1);
                if ($i < 9) {
                    $key = 'day_0' . ($i + 1);
                }
                if (isset($rs[$key]) && $rs[$key] != null) {
                    $symbol = $workDaySymbol->where('id', $rs[$key])->first();
                    $workDays[] = [
                        'day' => $i + 1,
                        'symbol' => [
                            'id' => $symbol->id,
                            'name' => $symbol->name,
                            'symbol' => $symbol->symbol
                        ]
                    ];
                }
            }
            $data = [
                'id' => $rs->id,
                'date_time' => $dateTime->format('m-Y'),
                'work_days' => $workDays,
                'total_study_work' => $rs->total_study_work,
                'total_probationary' => $rs->total_probationary,
                'total_real' => $rs->total_real,
                'total_online1' => $rs->total_online1,
                'total_online2' => $rs->total_online2,
                'total_online_probationary' => $rs->total_online_probationary,
                'total_bussiness' => $rs->total_bussiness,
                'total_bussiness_probationary' => $rs->total_bussiness_probationary,
                'total_holiday_absence' => $rs->total_holiday_absence,
                'total_salary' => $rs->total_salary,
                'total_no_salary' => $rs->total_no_salary,
                'total_lunch' => $rs->total_lunch,
                'total_social_insurance' => $rs->total_social_insurance,
                'total_holiday_remaining' => $holidayRemaining ?? 0,
            ];
            return (new APIJsonResponse())->responseSuccess($data);
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('A store: ' . $e->getMessage());
            return (new APIJsonResponse)->responseError();
        }
    }

    public function explainMissMobile(Request $request)
    {
        $month = $request->month;
        $department_id = $request->department_id ?? null;
        $rank_code = $request->rank_code ?? null;
        $user_id = auth()->user()->id;
        try {
            # list danh sách nhân sự đang active hiện tại
            $userLists = UserWorking::select(
                'user_workings.*'
            )
                ->notAdmin()
                ->notMe($user_id)
                ->department($department_id)
                ->when($rank_code, function ($q, $rankCode) {
                    $q->whereHas('position', function ($qr) use ($rankCode) {
                        $qr->where('code', $rankCode);
                    });
                })
                ->allActive()
                ->with([
                    'user',
                    'department',
                    'position',
                    'explanations' => function ($query) use ($month) {
                        $query->whereRaw("date LIKE '{$month}%'")->orderBy('status')->orderBy('date', 'DESC');
                    },
                    'explanations.confirm'
                ])
                ->orderBy('department_id')
                ->orderBy('position_id')
                ->orderBy('user_id')
                ->groupBy('user_id')
                ->paginate(20);
            return (new APIJsonResponse())->responseSuccess((new TimeSheetTransformer)->explains($userLists));
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('A store: ' . $e->getMessage());
            return (new APIJsonResponse)->responseError();
        }
    }
}
