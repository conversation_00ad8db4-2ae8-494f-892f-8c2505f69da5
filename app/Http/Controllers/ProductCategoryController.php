<?php

namespace App\Http\Controllers;

use App\Models\ProductCategory;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class ProductCategoryController extends Controller
{
    public function index(Request $request)
    {
        try {
            $pageSize = $request->pageSize ?: 20;
            $rs = ProductCategory::when($request->keyword, function ($q, $keyword) {
                $q->where('name', 'like', '%' . $keyword . '%')
                    ->orWhere('code', 'like', '%' . $keyword . '%');
            })
                ->orderBy('code', 'ASC')
                ->paginate($pageSize);

            return response()->json(['data' => $rs]);
        } catch (Exception $e) {
            Log::error('ProductCategoryController index: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function store(Request $request)
    {
        $rules = [
            'name' => 'required|max:255',
            'code' => 'required|max:100|unique:product_category,code'
        ];
        if ($request->id) {
            $rules['code'] = 'required|max:100|unique:product_category,code,' . $request->id;
        }

        $request->validate($rules, [], [
            'name' => 'Tên nhóm sản phẩm',
            'code' => 'Mã nhóm'
        ]);
        try {
            $model = new ProductCategory();
            $msg = 'Tạo mới danh mục sản phẩm thành công!';
            if ($request->id) {
                $model = ProductCategory::find($request->id);
                $msg = 'Cập nhật danh mục sản phẩm thành công!';
            }

            $model->name = $request->name;
            $model->code = $request->code;
            $model->save();

            return response()->json(['message' => $msg]);
        } catch (Exception $e) {
            Log::error('ProductCategoryController store: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }
}