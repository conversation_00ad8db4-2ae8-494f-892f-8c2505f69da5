<?php

namespace App\Http\Controllers;

use App\Models\Department;
use App\Models\ProvinceBusinessMarket;
use App\Models\TeamSaleArea;
use App\Transformer\ProvinceBusinessTransformer;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class ProvinceBusinessMarketController extends Controller
{
    public function index()
    {
        $rs = ProvinceBusinessMarket::orderBy('name', 'ASC')->with('districtBusinessMarket')->get();
        return response()->json($rs);
    }

    public function all()
    {
        try {
            $user = auth()->user();
            if (
                $user->user
                && $user->user->position
                && $user->user->position->department_id == Department::SALE_DEPARTMENT
            ) {
                if (auth()->user()->hasAnyPermission(['contract_help_upload'])) {
                    $rs = ProvinceBusinessMarket::with('districtBusinessMarket')
                        ->orderBy('name')
                        ->get();
                } else {
                    $area = TeamSaleArea::getProvincesAndDistrictsBySaleArea($user);
                    $provinceIds = array_unique($area['provinceIds']);
                    $rs = ProvinceBusinessMarket::whereIn('province_id', $provinceIds)->with('districtBusinessMarket')
                        ->orderBy('name')
                        ->get();
                }
            } else {
                $rs = ProvinceBusinessMarket::with('districtBusinessMarket')
                    ->orderBy('name')
                    ->get();
            }
            return response()->json(['data' => (new ProvinceBusinessTransformer)->transforms($rs)]);
        } catch (Exception $e) {
            Log::error("ProvinceBusinessMarketController all: " . $e->getMessage());
            return response()->json([], 500);
        }
    }
}
