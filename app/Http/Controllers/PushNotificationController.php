<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\UserDeviceForNotification;

class PushNotificationController extends Controller
{
    public function storeDeviceId(Request $request)
    {
        try {
            if (!$request->playerId) {
                return response()->json([], 400);
            }
            $model = UserDeviceForNotification::where('player_id', $request->playerId)
                ->first();
            if (!$model) {
                $model = new UserDeviceForNotification();
            }
            
            $model->player_id = $request->playerId;
            $model->user_id = $request->user()->id;
            $model->save();
            return response()->json(['message' => 'Xử lý thành công']);
        } catch (\Exception $e) {
            \Log::error("PushNotificationController storeDeviceId: ".$e->getMessage());
            return response()->json([], 500);
        }
    }
}