<?php

namespace App\Http\Controllers;

use App\Models\Product;
use Illuminate\Http\Request;
use App\Models\Response;
use App\Http\Requests\ProductStoreRequest;
use App\Http\Requests\ProductUpdateRequest;
use App\Models\ProductRank;
use App\Models\TeamSaleArea;
use App\Models\UserWorking;
use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class ProductController extends Controller
{
    /*
    * Danh sách sản phẩm
    */
    public function index()
    {
        $data = Product::with('leader.avatar', 'category')
            ->orderBy('sort', 'ASC')
            ->paginate(20);
        return Response::formatResponse(config('apicode.SUCCESS'), $data);
    }

    /*
    * Thêm mới sản phẩm mới
    */
    public function store(ProductStoreRequest $request)
    {
        try {
            DB::beginTransaction();
            $errors = [];
            $years = [];
            $product = $request->product;
            $pro = Product::create($product);
            # KE thì ko cần phải cấu hình giá ở đây
            if (empty($request->product_ranks) || $request->product['code'] == Product::KE_CODE) {
                DB::commit();
                return Response::formatResponse(config('apicode.SUCCESS'), $pro);
            }

            $rank = [];
            foreach ($request->product_ranks as $el) {
                array_push($years, $el['year']);
                $rank[] = [
                    'product_id' => $pro['id'],
                    'min' =>  $el['min'],
                    'max' => $el['max'],
                    'year' => $el['year']
                ];
            }
            $unique = array_unique($years);
            $duplicates = array_diff_assoc($years, $unique);
            if (count($duplicates) > 0) {
                foreach ($duplicates as $index => $item) {
                    $errors["product_ranks.$index.year"] =  "Năm" . " " . $item . " " . "đã tồn tại.";
                }
            }
            if (count($errors) > 0) {
                return response()->json(['errors' => $errors], 422);
            } else {
                if ($rank) {
                    ProductRank::insert($rank);
                    DB::commit();
                }
                return Response::formatResponse(config('apicode.SUCCESS'), $product);
            }
        } catch (\Exception $e) {
            DB::rollback();
            return Response::formatResponse(config('apicode.INTERNAL_SERVER_ERROR'), $e->getMessage());
        }
    }

    public function show($id)
    {
        $data = Product::where('id', $id)->with('product_ranks')->first();
        return Response::formatResponse(config('apicode.SUCCESS'), $data);
    }

    /*
    * Cập nhật sản phẩm
    */
    public function update(ProductUpdateRequest $request, $id)
    {
        try {
            DB::beginTransaction();
            $errors = [];
            $years = [];
            $product = [
                'code' => $request->product['code'],
                'name' => $request->product['name'],
                'short_name' => $request->product['short_name'],
                'type' => $request->product['type'],
                'link_recommend' => $request->product['link_recommend'],
                'leader' => $request->product['leader'],
                'product_category_id' => $request->product['product_category_id'],
            ];
            Product::where('id', $id)->update($product);

            # KE thì ko cần phải cấu hình giá ở đây
            if (empty($request->product_ranks) || $request->product['code'] == Product::KE_CODE) {
                DB::commit();
                return Response::formatResponse(config('apicode.SUCCESS'), $product);
            }
            $rank = [];
            foreach ($request->product_ranks as $index => $el) {
                array_push($years, $el['year']);
                $rank[] = [
                    'product_id' => $request->product['id'],
                    'min' =>  $el['min'],
                    'max' => $el['max'],
                    'year' => $el['year']
                ];
            }
            $unique = array_unique($years);
            $duplicates = array_diff_assoc($years, $unique);
            if (count($duplicates) > 0) {
                foreach ($duplicates as $index => $item) {
                    $errors["product_ranks.$index.year"] =  "Năm" . " " . $item . " " . "đã tồn tại.";
                }
            }
            if (count($errors) > 0) {
                return response()->json(['errors' => $errors], 422);
            } else {
                if ($rank) {
                    ProductRank::where('product_id', $request->product['id'])->delete();
                    ProductRank::insert($rank);
                    DB::commit();
                }
                return Response::formatResponse(config('apicode.SUCCESS'), $product);
            }
        } catch (\Exception $e) {
            DB::rollback();
            return Response::formatResponse(config('apicode.INTERNAL_SERVER_ERROR'), $e->getMessage());
        }
    }

    public function getAllowProductByTeam(Request $request)
    {
        try {
            $user = $request->user();
            if (!$user->user || !$user->user->position_id) {
                return response()->json([], 403);
            }

            $teamIds = UserWorking::getAllDependentPositionIdByPosition($user->user->position_id, []);
            $rs = TeamSaleArea::year(date('Y'))
                ->with('product')
                ->whereIn('position_id', $teamIds)
                ->groupBy('product_id')
                ->get();
            $data = [];
            foreach ($rs as $t) {
                $data[] = [
                    'id' => $t->product->id,
                    'short_name' => $t->product->short_name,
                    'code' => $t->product->code,
                    'min' => $t->min,
                    'max' => $t->max
                ];
            }

            return response()->json($data);
        } catch (Exception $e) {
            Log::error('ProductController getAllowProductByTeam: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function exceptKeCode()
    {
        try {
            $productKe = Product::where('code', Product::KE_CODE)->first();
            return $productKe;
            return response()->json(['code' => Product::KE_CODE, 'id' => $productKe->id]);
        } catch (\Exception $e) {
            Log::error('ProductController projects: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }
}
