<?php

namespace App\Http\Controllers;

use App\Models\SurveyAnswerForQuestion;
use App\Models\SurveyQuestion;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class SurveyQuestionController extends Controller
{
    public function index(Request $request)
    {
        try {
            $pageSize = $request->pageSize ?: 20;
            $exceptId = $request->exceptId ? explode(',', $request->exceptId) : [];
            $rs = SurveyQuestion::with('category')
                ->when($request->category_id, function ($query, $categoryId) {
                    $query->where('category_id', $categoryId);
                })
                ->when($request->content, function ($query, $content) {
                    $query->where('content', 'LIKE', '%' . $content . '%');
                })
                ->when($exceptId, function ($query, $exceptId) {
                    $query->whereNotIn('id', $exceptId);
                })
                ->orderBy('created_at', 'DESC')
                ->paginate($pageSize);

            return response()->json(['data' => $rs]);
        } catch (Exception $e) {
            Log::error('SurveyQuestionController index: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function store(Request $request)
    {
        $rules = $request->id
            ? [
                'content' => ['required', 'max:255', 'unique:survey_questions,content,' . $request->id],
                'answers' => ['array'],
                'answers.*.content' => ['required', 'max:255', 'distinct'],
                'deleted' => 'array'
            ]
            : [
                'content' => ['required', 'max:255', 'unique:survey_questions'],
                'answers' => ['array'],
                'answers.*.content' => ['required', 'max:255', 'distinct'],
                'deleted' => 'array'
            ];

        $request->validate($rules, [], ['content' => 'Câu hỏi', 'answers.*.content' => 'Câu trả lời']);

        DB::beginTransaction();
        try {
            $model = new SurveyQuestion();
            if ($request->id) {
                $model = SurveyQuestion::find($request->id);
                if (!$model) {
                    return response()->json(['message' => 'Dữ liệu không tồn tại'], 400);
                }
            }

            $model->content = $request->content;
            $model->category_id = $request->category_id;
            $model->save();

            if ($request->id && $request->deleted) {
                SurveyAnswerForQuestion::whereIn('id', $request->deleted)->delete();
            }

            $answers = [];
            foreach ($request->answers as $answer) {
                if ($answer['id']) {
                    SurveyAnswerForQuestion::where('id', $answer['id'])
                        ->update([
                            'content' => $answer['content'],
                            'is_correct' => (bool)$answer['is_correct'],
                        ]);
                } else {
                    $answers[] = [
                        'id' => Str::uuid()->toString(),
                        'question_id' => $model->id,
                        'content' => $answer['content'],
                        'is_correct' => (bool)$answer['is_correct'],
                        'created_at' => Carbon::now(),
                        'updated_at' => Carbon::now()
                    ];
                }
            }

            DB::table('survey_answer_for_questions')->insert($answers);

            DB::commit();

            return response()->json(['message' => $request->id ? 'Cập nhật câu hỏi thành công!' : 'Thêm câu hỏi thành công!']);
        } catch (Exception $e) {
            DB::rollBack();
            Log::error('SurveyQuestionController store: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function details(Request $request)
    {
        try {
            $model = SurveyQuestion::with(['category', 'answers'])
                ->where('id', $request->id)
                ->first();
            if (!$model) {
                return response()->json(['message' => 'Dữ liệu không tồn tại'], 404);
            }

            return response()->json($model);
        } catch (Exception $e) {
            Log::error('SurveyQuestionController details: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function delete(Request $request)
    {
        DB::beginTransaction();
        try {
            $model = SurveyQuestion::find($request->id);
            if (!$model) {
                return response()->json(['message' => 'Dữ liệu không tồn tại'], 404);
            }

            $model->answers()->delete();
            $model->delete();
            DB::commit();
            return response()->json(['message' => 'Xóa câu hỏi thành công!']);
        } catch (Exception $e) {
            DB::rollBack();
            Log::error('SurveyQuestionController delete: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }
}
