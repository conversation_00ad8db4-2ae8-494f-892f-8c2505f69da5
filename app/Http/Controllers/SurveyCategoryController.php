<?php

namespace App\Http\Controllers;

use App\Models\SurveyCategory;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class SurveyCategoryController extends Controller
{
    public function index(Request $request)
    {
        try {
            $type = $request->type ?: SurveyCategory::TYPE_QUESTION;
            $rs = SurveyCategory::where('type', $type)
                ->orderBy('created_at', 'DESC')
                ->paginate(20);

            return response()->json(['data' => $rs]);
        } catch (Exception $e) {
            Log::error('SurveyCategoryController index: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function getAll()
    {
        try {
            $rs = SurveyCategory::all();

            return response()->json($rs);
        } catch (Exception $e) {
            Log::error('SurveyCategoryController getAll: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function store(Request $request)
    {
        $rules = $request->id
            ? ['content' => ['required', 'max:255', 'unique:survey_category,content,' . $request->id]]
            : ['content' => ['required', 'unique:survey_category', 'max:255']];

        $request->validate($rules, [], ['content' => 'Tên danh mục']);

        try {
            $model = new SurveyCategory();
            if ($request->id) {
                $model = SurveyCategory::find($request->id);
                if (!$model) {
                    return response()->json(['message' => 'Dữ liệu không tồn tại'], 400);
                }
            }

            $model->content = $request->content;
            $model->type = $request->type;
            $model->save();

            return response()->json(['message' => $request->id ? 'Cập nhật danh mục thành công!' : 'Thêm danh mục thành công!']);
        } catch (Exception $e) {
            Log::error('SurveyCategoryController store: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function details(Request $request)
    {
        try {
            $model = SurveyCategory::find($request->id);
            if (!$model) {
                return response()->json(['message' => 'Dữ liệu không tồn tại'], 404);
            }

            return response()->json($model);
        } catch (Exception $e) {
            Log::error('SurveyCategoryController details: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function delete(Request $request)
    {
        try {
            $model = SurveyCategory::find($request->id);
            if (!$model) {
                return response()->json(['message' => 'Dữ liệu không tồn tại'], 404);
            }

            $model->delete();

            return response()->json(['message' => 'Xóa danh mục thành công!']);
        } catch (Exception $e) {
            Log::error('SurveyCategoryController delete: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }
}
