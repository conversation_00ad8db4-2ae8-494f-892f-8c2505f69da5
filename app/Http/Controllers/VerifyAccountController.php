<?php

namespace App\Http\Controllers;

use App\Models\Client;
use App\Models\Contract;
use App\Models\Product;
use Illuminate\Http\Request;
use Carbon\Carbon;

class VerifyAccountController extends Controller
{
    public function verifyAccount(Request $request)
    {
        $access_token = env('ACCESS_TOKEN');
        $decoded_access_token = utf8_decode(base64_decode($request->access_token));
        if ($decoded_access_token === $access_token) {
            $product = Product::where('code', $request->product_code)->first();
            $product_id = $product->id;
            if ($request->product_code == 'QA') {
                $client = Client::where('map_qa', $request->map_client)->first();
            }
            if ($request->product_code == 'EOS') {
                $client = Client::where('map_eos', $request->map_client)->first();
            }
            if ($request->product_code == 'PCGD') {
                $client = Client::where('map_pcgd', $request->map_client)->first();
            }
            $current_date = Carbon::now()->format('Y-m-d');
            $contracts = Contract::where('client_id', $client->id)->where('product_id', $product_id)->whereDate('contract_expire_date', '>=', $current_date)->get();
            if (count($contracts) > 0) {
                $arr_expire_date = [];
                foreach ($contracts as $contract) {
                    array_push($arr_expire_date, $contract->contract_expire_date);
                }
                $latest_expire_date = max($arr_expire_date);
                $sub_date = Carbon::parse($latest_expire_date)->subDays(30)->format('Y-m-d');
                $data = Contract::where('client_id', $client->id)->where('product_id', $product_id)->whereDate('contract_expire_date', $latest_expire_date)->first();
                if ($current_date >= $sub_date) {
                    return response()->json(['code' => 302, 'data' => $data, 'message' => "Hợp đồng sắp hết hạn"]);
                } else {
                    return response()->json(['code' => 200, 'data' => $data, 'message' => "Login thành công"]);
                }
            } else {
                $data = Contract::where('client_id', $client->id)->where('product_id', $product_id)->orderBy('contract_expire_date', 'DESC')->first();
                return response()->json(['code' => 400, 'data' => $data,  'message' => "Hợp đồng đã hết hạn"]);
            }
        } else {
            return response()->json(['code' => 403,  'message' => "Hệ thống không có quyền truy cập"]);
        }
    }
}
