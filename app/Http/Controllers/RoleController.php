<?php

namespace App\Http\Controllers;

use App\Models\Module;
use App\Models\Response;
use Illuminate\Http\Request;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\Models\User;
use App\Models\Department;
use Illuminate\Support\Facades\DB;

class RoleController extends Controller
{
    /**
     * Lấy ra all role và module
     * 
     * @return object
     */
    public function index()
    {
        $data = (object) [];
        $data->role = Role::with('permissions')->orderby('name')->get();
        $data->module = Module::with('permissions')->get();
        return Response::formatResponse(config('apicode.SUCCESS'), $data);
    }

    /**
     * <PERSON>ân quyền các permission cho từng role
     * 
     */
    public function update(Request $request)
    {
        \DB::beginTransaction();
        try {
            $code = config('apicode.WRONG_PARAMS');
            if ($request->id) {
                $role = Role::find($request->id);
            } else {
                $role = new Role();
            }

            $code = config('apicode.SUCCESS');
            $role->name = trim($request->name);
            $role->title = trim($request->title);
            $role->guard_name = 'api';
            $permissionIds = $request->input('permissions');
            $permissions = Permission::whereIn('id', $permissionIds)->get();
            $role->save();
            $role->syncPermissions($permissions);

            \DB::commit();
            return Response::formatResponse($code, $role);
        } catch (\Exception $e) {
            \Log::error('RoleController update: ' . $e->getMessage());
            \DB::rollBack();
            return response()->json([], 500);
        }
    }

    /**
     * Đồng bộ user và role 
     * Hàm này chỉ dùng cho admin và chỉ dùng 1 lần khi chạy seed user
     * Mạc định khi chạy UserSeeder và UserWorkingSeeder thì các nhân sự sẽ có vị trí phòng ban và hợp đồng hiện tại luôn tồn tại
     * id = 1 trong bảng users là admin, nên đã có sắn role_id = 1 (admin) rồi
     */
    public function syn_role(Request $request)
    {
        # tạm thời die, khi nào dùng thì bật
        die('ssssss');
        $users = User::with('user.employmentContract')->where('id', '<>', '1')->get();
        foreach ($users as $k => $user) {
            $employment = $user['user'];
            $department = Department::year(date('Y'))->find($employment->department_id);
            $role_id = $department->role_id;
            User::synRole($user['id'], $role_id);
        }
    }
}
