<?php

namespace App\Http\Controllers;

use App\Models\BusinessAreaDetail;
use App\Models\BusinessArea;
use App\Models\ProductRank;
use App\Models\Product;
use App\Models\TeamSaleArea;
use App\Models\TeamSaleAreaHistory;
use App\Models\User;
use App\Models\UserWorking;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use PhpParser\Node\Expr\Cast\Object_;
use App\Transformer\TeamSaleAreaTransformer;

class TeamSaleAreaController extends Controller
{
    public function index(Request $request)
    {
        try {
            if (!$request->teamId) {
                return response()->json([], 400);
            }

            $teamId = $request->teamId;
            $user = $request->user();
            // if (!$user->hasPermissionTo('product_config_range_for_team')) {
            //     $allowTeamIds = [$user->user->position_id];
            //     if ($user->user->subUserWorkings) {
            //         foreach ($user->user->subUserWorkings as $p) {
            //             if (!in_array($p->position_id, $allowTeamIds)) {
            //                 $allowTeamIds[] = $p->position_id;
            //             }
            //         }
            //     }
            //     if ($user->user->position->is_leader) {
            //         $rs = UserWorking::allActive()
            //             ->where('dependent_position_id', $user->user->position_id)
            //             ->distinct()
            //             ->pluck('position_id')
            //             ->toArray();
            //         $allowTeamIds = array_merge($allowTeamIds, $rs);
            //     } else {
            //         $allowTeamIds[] = $user->user->dependent_position_id;
            //     }

            //     if (!in_array($teamId, $allowTeamIds)) {
            //         return response()->json([], 403);
            //     }
            // }

            $query = TeamSaleArea::year(date('Y'))
                ->with(['province', 'district'])
                ->where('position_id', $teamId);

            if ($request->has('provinceId')) {
                $query->where('province_id', $request->provinceId);
            }

            if ($request->has('districtId')) {
                $query->where('district_id', $request->districtId);
            }

            $rs = $query->orderBy('province_id', 'asc')
                ->orderBy('district_id', 'asc')
                ->orderBy('created_at', 'DESC')
                ->paginate(10)->through(function ($item) {
                    return (new TeamSaleAreaTransformer)->transform($item);
                });

            return response()->json(['data' => $rs]);
        } catch (Exception $e) {
            Log::error("TeamSaleAreaController index: " . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function getTeamSaleAreas(Request $request, $teamId)
    {
        $provinceAreas = [];
        $districtAreas = [];
        $query = TeamSaleArea::year(date('Y'))->where('position_id', $teamId);
        $provinces = with(clone $query)->whereNull('district_id')->with('provinceBusinessMarket.districtBusinessMarket')->get();
        foreach ($provinces as $e) {
            array_push($provinceAreas, $e->provinceBusinessMarket);
        }
        $districts = with(clone $query)->whereNotNull('district_id')->with('provinceBusinessMarket', 'districtBusinessMarket')->get();
        foreach ($districts as $e) {
            array_push($districtAreas, $e->districtBusinessMarket);
        }
        return response()->json(['provinceAreas' => $provinceAreas, 'districtAreas' => $districtAreas]);
    }

    public function store(Request $request, $teamId)
    {
        $request->validate([
            'provinceAreas' => 'required|array',
            'districtAreas' => 'required|array'
        ]);

        DB::beginTransaction();
        try {
            TeamSaleArea::year(date('Y'))->where('position_id', $teamId)->delete();
            $teamSaleAreas = [];
            // $dataHistoryArea = [];
            // foreach ($request->data as $businessArea) {
            //     $businessAreaDetail = BusinessAreaDetail::with('districtBusinessMarket')->where('business_area_id', $businessArea['id'])->get();
            //     foreach ($businessAreaDetail as $areaDetail) {
            //         $dataHistoryArea[$areaDetail->province_id] = [];
            //         foreach ($areaDetail->districtBusinessMarket as $district) {
            //             array_push($teamSaleAreas, [
            //                 'position_id' => $request->id,
            //                 'province_id' => $district->province_id,
            //                 'district_id' => $district->district_id,
            //                 'created_at' => Carbon::now(),
            //                 'updated_at' => Carbon::now(),
            //                 'business_area_id' => $businessArea['id'],
            //             ]);
            //             $dataHistoryArea[$areaDetail->province_id][] = $district->district_id;
            //         }
            //     }
            // }

            // DB::table('team_sale_areas_' . date('Y'))->insert($teamSaleAreas);

            // $this->storeHistory($request->id, $dataHistoryArea);

            $province_ids = [];

            foreach ($request->provinceAreas as $province) {
                $area = BusinessAreaDetail::where('province_id', $province['province_id'])->first();
                if (!$area) {
                    return response()->json(['message' => 'Tỉnh ' . $province['name'] . ' chưa được phân khu vực'], 404);
                }
                array_push($teamSaleAreas, [
                    'position_id' => $teamId,
                    'province_id' => $province['province_id'],
                    'district_id' => null,
                    'business_area_id' => $area->business_area_id,
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now(),
                ]);
                array_push($province_ids, $province['province_id']);
            }
            foreach ($request->districtAreas as $district) {
                $area = BusinessAreaDetail::where('province_id', $district['province_id'])->first();
                array_push($teamSaleAreas, [
                    'position_id' => $teamId,
                    'province_id' => $district['province_id'],
                    'district_id' => $district['district_id'],
                    'business_area_id' => $area->business_area_id,
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now(),
                ]);
            }

            DB::table('team_sale_areas_' . date('Y'))->insert($teamSaleAreas);
            DB::commit();
            return response()->json([]);
        } catch (Exception $e) {
            DB::rollback();
            Log::error("TeamSaleAreaController store: " . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function ownerUpdateRange(Request $request)
    {
        try {
            $model = TeamSaleArea::year(date('Y'))->find($request->id);
            if (!$model) {
                Log::error("TeamSaleAreaController ownerUpdateRange: Không tìm thấy bản ghi với Id: $request->id");

                return response()->json([], 404);
            }

            if ($request->user()->cannot('ownerUpdateRange', $model)) {
                return response()->json([], 403);
            }

            if ($request->max <= 0 || $request->min <= 0 || $request->max < $request->min) {
                return response()->json(['message' => 'Giá lớn nhất không được nhỏ hơn giá nhỏ nhất'], 400);
            }

            if ($model->min > 0 && $request->min < $model->min) {
                return response()->json(['message' => 'Giá nhỏ nhất phải lớn hơn ' . $model->min], 400);
            }

            if ($model->max > 0 && $request->max > $model->max) {
                return response()->json(['message' => 'Giá lớn nhất phải nhỏ hơn ' . $model->max], 400);
            }



            $model->min = $request->min;
            $model->max = $request->max;
            $model->save();

            return response()->json([]);
        } catch (Exception $e) {
            Log::error("TeamSaleAreaController ownerUpdateRange: " . $e->getMessage());

            return response()->json([], 500);
        }
    }

    public function getProductRangeDefault()
    {
        try {
            $config = ProductRank::select('product_id', 'max', 'min')
                ->active()
                ->orderBy('product_id', 'ASC')
                ->get();

            return response()->json($config);
        } catch (Exception $e) {
            Log::error("TeamSaleAreaController getProductRangeDefault: " . $e->getMessage());

            return response()->json([], 500);
        }
    }

    private function storeHistory($position_id, $dataHostoryArea)
    {
        TeamSaleAreaHistory::year(date('Y'))
            ->where([
                'position_id' => $position_id,
                'updated_at' => null
            ])->update([
                'updated_at' => date('Y-m-d H:i:s')
            ]);

        $history = new TeamSaleAreaHistory();
        $history->setYear(date('Y'));
        $history->position_id = $position_id;
        $history->areas = $dataHostoryArea;
        $history->created_at = date('Y-m-d H:i:s');
        $history->save();
    }
}
