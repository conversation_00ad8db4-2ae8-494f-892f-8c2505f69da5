<?php

namespace App\Http\Controllers;

use App\Events\ConfirmBusinessTripEvent;
use App\Models\User;
use App\Notifications\BusinessTripNotification;
use App\Services\UserService;
use App\Services\FirebaseNotification;
use Illuminate\Http\Request;
use App\Jobs\SendNotifyToAppJob;


class TestController extends Controller
{
    public function index(Request $request)
    {
        // $user = auth()->user();
        $user = User::find(2);
        // $user->assignRole('super-admin');

        dd($user->hasRole('super-admin'));
    }

    public function testSendEvent()
    {
        // # bắn notify cho annp để test
        // $userNoti = User::find(5);
        // $message = [
        //     'title' => 'Duyệt đăng ký công tác',
        //     'link' => '/business-trip',
        //     'content' => 'Bạn có thông báo duyệt đăng ký công tác cho nhân viên: Test',
        //     'option' => 'add',
        // ];

        // $userNoti->notify(new BusinessTripNotification($message));
        // $newMessage = $userNoti->unreadNotifications()->where("type", "App\Notifications\BusinessTripNotification")->first();
        // event(new ConfirmBusinessTripEvent($userNoti, $newMessage));

        # đẩy msg đến mobile
        try {
            $user = User::find(4);// Chỗ này là tài khoản 4. Tuấn NA
            $msg = [
                'title' => 'Tuanna test',
                'message'  => 'Test new notification to app',
                'notification_id' => 'klasdfgalskdfgasl'
            ];
            dispatch(new SendNotifyToAppJob($user, $msg, FirebaseNotification::PUSH_TYPE_NOTIFICATION))->onQueue('test_mobile');
        } catch(\Exception $ex) {
            dd($ex);
        }


        return true;
    }

    public function calculateDiligence(Request $request)
    {
        $userId = $request->input('userId');
        $fromDate = $request->input('fromDate');
        $toDate = $request->input('toDate');
        return (new UserService())->calculateDiligence($userId, $fromDate, $toDate);
    }

}
