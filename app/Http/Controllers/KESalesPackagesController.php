<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Exception;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use App\Models\SalesPackages;
use App\Models\SalesPackagesAttachedAppendix;
use App\Models\EquipmentType;

class KESalesPackagesController extends Controller
{
    public function index(Request $request)
    {
        try {
            $pageSize = $request->pageSize ?: 20;
            $rs = SalesPackages::when($request->name, function ($subQuery, $name) {
                return $subQuery->where('name', 'LIKE', '%' . $name . '%');
            })
                ->orderBy('end_date', 'DESC')
                ->orderBy('created_at', 'DESC')
                ->paginate($pageSize);

            return response()->json(['data' => $rs]);
        } catch (Exception $e) {
            Log::error('KESalesPackagesController index: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function getEquipmentTypes(Request $request)
    {
        try {
            $rs = EquipmentType::orderBy('name')
                ->get();
            return response()->json(['data' => $rs]);
        } catch (Exception $e) {
            Log::error('KESalesPackagesController getEquipmentTypes: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function store(Request $request)
    {
        // $rules = $request->id
        //     ? [
        //         'name' => ['required', 'max:255', 'unique:ke_sales_packages,name,' . $request->id],
        //         'price' => 'required|numeric',
        //         'start_date' => 'required|date',
        //         'end_date' => 'required|date',
        //         'allowed_overdue_time' => 'numeric',
        //         'attachedAppendix' => ['array'],
        //         'attachedAppendix.*.equipment_type_id' => ['required'],
        //         'deleted' => 'array'
        //     ]
        //     : [
        //         'name' => ['required', 'max:255', 'unique:ke_sales_packages'],
        //         'price' => 'required|numeric',
        //         'start_date' => 'required|date',
        //         'end_date' => 'required|date',
        //         'allowed_overdue_time' => 'numeric',
        //         'attachedAppendix' => ['array'],
        //         'attachedAppendix.*.equipment_type_id' => ['required'],
        //         'attachedAppendix.*.amount' => ['required', 'min:1'],
        //         'deleted' => 'array'
        //     ];

        // $request->validate($rules, [], [
        //     'name' => 'Tên gói bán',
        //     'price' => 'Giá bán',
        //     'start_date' => 'Thời gian bắt đầu',
        //     'end_date' => 'Thời gian hết hạn',
        //     'allowed_overdue_time' => 'Thời gian quá hạn cho phép',
        //     'attachedAppendix.*.equipment_type_id' => 'Tên vật tư - hàng hóa',
        //     'attachedAppendix.*.amount' => 'Số lượng vật tư - hàng hóa'
        // ]);

        DB::beginTransaction();
        try {
            $model = new SalesPackages();
            if ($request->id) {
                $model = SalesPackages::find($request->id);
                if (!$model) {
                    return response()->json(['message' => 'Dữ liệu không tồn tại'], 400);
                }
            }

            $model->name = $request->name;
            // $model->price = $request->price;
            // $model->start_date = $request->start_date;
            // $model->end_date = $request->end_date;
            // $model->allowed_overdue_time = $request->allowed_overdue_time ?: 0;
            $model->save();

            if ($request->id && $request->deleted) {
                SalesPackagesAttachedAppendix::whereIn('id', $request->deleted)->delete();
            }

            // $attachments = [];
            // foreach ($request->attachedAppendix as $attachedAppendix) {
            //     if ($attachedAppendix['id']) {
            //         SalesPackagesAttachedAppendix::where('id', $attachedAppendix['id'])
            //             ->update([
            //                 'amount' => $attachedAppendix['amount']
            //             ]);
            //     } else {
            //         $attachments[] = [
            //             'id' => Str::uuid()->toString(),
            //             'ke_sales_packages_id' => $model->id,
            //             'equipment_type_id' => $attachedAppendix['equipment_type_id'],
            //             'amount' => $attachedAppendix['amount'],
            //             'created_at' => Carbon::now(),
            //             'updated_at' => Carbon::now()
            //         ];
            //     }
            // }

            // DB::table('ke_sales_packages_attached_appendix')->insert($attachments);

            DB::commit();

            return response()->json(['message' => $request->id ? 'Cập nhật gói bán hàng thành công!' : 'Thêm gói bán hàng thành công!']);
        } catch (Exception $e) {
            DB::rollBack();
            Log::error('KESalesPackagesController store: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function details(Request $request)
    {
        try {
            $model = SalesPackages::with('attachedAppendix')
                ->where('id', $request->id)
                ->first();
            if (!$model) {
                return response()->json(['message' => 'Dữ liệu không tồn tại'], 404);
            }

            return response()->json($model);
        } catch (Exception $e) {
            Log::error('KESalesPackagesController details: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function delete(Request $request)
    {
        DB::beginTransaction();
        try {
            $model = SalesPackages::find($request->id);
            if (!$model) {
                return response()->json(['message' => 'Dữ liệu không tồn tại'], 404);
            }

            $model->attachedAppendix()->delete();
            $model->delete();
            DB::commit();
            return response()->json(['message' => 'Xóa gói bán hàng thành công!']);
        } catch (Exception $e) {
            DB::rollBack();
            Log::error('KESalesPackagesController delete: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }
}
