<?php

namespace App\Http\Controllers;

use App\Models\Client;
use App\Models\Contract;
use App\Models\KPISummaryUpgrade;
use App\Models\Department;
use App\Models\ImportContract;
use App\Models\Position;
use Illuminate\Http\Request;
use App\Models\Response;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use PhpOffice\PhpSpreadsheet\IOFactory;
use App\Models\Product;
use App\Models\TmpContract;
use App\Models\User;
use App\Models\UserWorking;
use Exception;
use App\Transformer\APIJsonResponse;
use App\Transformer\TmpContractTransformer;

class ImportContractController extends Controller
{
    public function importTmpContractBase(Request $request)
    {
        $request->validate([
            'file' => 'required|file|mimes:xls,xlsx',
            'team' => 'required',
            'company' => 'required',
            'province_id' => 'required',
            'district_id' => 'required'
        ], [], [
            'file' => 'File',
            'team' => 'Team',
            'company' => 'Công ty',
            'province_id' => 'Tỉnh/Thành phố',
            'district_id' => 'Quận/Huyện'
        ]);
        $file = $request->file('file');

        DB::beginTransaction();

        try {
            $spreadsheet = IOFactory::load($file->getRealPath());
            $sheet        = $spreadsheet->getActiveSheet(0);
            $row_limit    = $sheet->getHighestDataRow();
            $row_range    = range(2, $row_limit);

            $errors = [];
            $importContract = new ImportContract();
            $importContract->province_id = $request->province_id;
            $importContract->district_id = $request->district_id;
            $importContract->team = $request->team;
            $importContract->company = $request->company;
            $importContract->created_by = auth()->user()->id;
            // $importContract->total_record = count($row_range);
            $importContract->status = ImportContract::SELF_REVIEW;
            $importContract->save();

            $columns = [
                'B' => 'Tên đơn vị dòng B:row không được bỏ trống',
                'D' => 'Địa chỉ dòng D:row không được bỏ trống',
                'E' => 'Tên người đại diện dòng E:row không được bỏ trống',
                'F' => 'Chức vụ dòng F:row không được bỏ trống',
                'G' => 'Số điện thoại người đại diện dòng G:row không được bỏ trống',
                'L' => 'Ngày tính doanh số dòng L:row không được bỏ trống',
                'M' => 'Ngày bắt đầu dòng M:row không được bỏ trống',
                'N' => 'Ngày hết hạn dòng N:row không được bỏ trống',
                'S' => 'Mã hàng hóa/dịch vụ dòng S:row không được bỏ trống',
                'U' => 'Số lượng dòng U:row không được bỏ trống',
                'V' => 'Đơn giá thực dòng V:row không được bỏ trống',
                'X' => 'Tổng giá trị hợp đồng dòng X:row không được bỏ trống',
                'AB' => 'Đề nghị xuất hóa đơn dòng AB:row không được bỏ trống',
                'AF' => 'Mã nhân viên dòng AF:row không được bỏ trống',
            ];

            $products = Product::select('id', 'code', 'product_category_id')->get()->toArray();
            $products = vietec_convert_array_to_map($products, 'code');

            $staffs = User::select('id', 'staff_code', 'name')->whereHas('user', function ($q) {
                $q->where('department_id', Department::SALE_DEPARTMENT);
            })->get()->toArray();
            $staffs = vietec_convert_array_to_map($staffs, 'staff_code');

            $teams = Position::year(date('Y'))->select('id', 'representatives')->where('representatives', 'LIKE', '%TEAM%')->get()->toArray();
            $teams = vietec_convert_array_to_map($teams, 'representatives');
            //tìm leader_id
            $leader = UserWorking::where('status', true)->where('position_id', $teams[$request->team]['id'])->first();
            if (!$leader) {
                return response()->json(['message' => 'Không tìm thấy leader ' . $request->team . ' !'], 404);
            }
            $tmpContracts = [];
            foreach ($row_range as $row) {
                if (!$sheet->getCell('A' . $row)->getValue() && !$sheet->getCell('B' . $row)->getValue()) {
                    break;
                }
                foreach ($columns as $key => $msg) {
                    $value = $sheet->getCell($key . $row)->getValue();
                    if (!$value) {
                        $errors[] = str_replace(':row', $row, $msg);
                    }
                }

                $sale_date = $sheet->getCell('L' . $row)->getFormattedValue();
                $from_date = $sheet->getCell('M' . $row)->getFormattedValue();
                $to_date = $sheet->getCell('N' . $row)->getFormattedValue();

                $productId = null;
                $productCategoryId = null;
                if (!array_key_exists($sheet->getCell('S' . $row)->getValue(), $products)) {
                    $errors[] = "Mã sản phẩm dòng S" . $row . " không tồn tại";
                } else {
                    $productId = $products[$sheet->getCell('S' . $row)->getValue()]['id'];
                    $productCategoryId = $products[$sheet->getCell('S' . $row)->getValue()]['product_category_id'];
                }

                $invoice_request = $sheet->getCell('AB' . $row)->getValue();
                $received_money = $sheet->getCell('AC' . $row)->getValue();
                $payment_date = $sheet->getCell('AD' . $row)->getValue();
                $contract_value = $sheet->getCell('X' . $row)->getValue();
                $payment_amount = $sheet->getCell('V' . $row)->getValue();
                if ($received_money && !$payment_date) {
                    $errors[] = "Ngày thanh toán dòng AD" . $row . " không được bỏ trống";
                }
                if (!$received_money && $payment_date) {
                    $errors[] = "Số tiền thanh toán dòng AC" . $row . " không được bỏ trống";
                }
                if ($received_money > $contract_value) {
                    $errors[] = "Số tiền thanh toán dòng AC" . $row . " không được lớn hơn giá trị hợp đồng";
                }
                if ($payment_amount > $contract_value) {
                    $errors[] = "Đơn giá thực dòng V" . $row . " không được lớn hơn giá trị hợp đồng";
                }
                $sale_id = null;
                if (!array_key_exists($sheet->getCell('AF' . $row)->getValue(), $staffs)) {
                    $errors[] = "Mã nhân viên dòng AF" . $row . " không tồn tại";
                } else {
                    $sale_id = $staffs[$sheet->getCell('AF' . $row)->getValue()]['id'];
                }

                Log::info(['row' => $row]);
                // lưu thông tin vào bảng tạm
                $tmpContracts[] = [
                    'import_contract_id' => $importContract->id,
                    'province_id' => $request->province_id,
                    'district_id' => $request->district_id,
                    'company' => $request->company,
                    'team' => $request->team,
                    'client_name' => $sheet->getCell('B' . $row)->getValue(),
                    'client_code' => $sheet->getCell('C' . $row)->getValue(),
                    'tax_code' => $sheet->getCell('C' . $row)->getValue(),
                    'address' => $sheet->getCell('D' . $row)->getValue(),
                    'head_master_name' => $sheet->getCell('E' . $row)->getValue(),
                    'position' => $sheet->getCell('F' . $row)->getValue(),
                    'head_master_phone' => $sheet->getCell('G' . $row)->getValue(),
                    'accountant_name' => $sheet->getCell('H' . $row)->getValue(),
                    'accountant_phone' => $sheet->getCell('I' . $row)->getValue(),
                    'contact_name' => $sheet->getCell('J' . $row)->getValue(),
                    'contact_phone' => $sheet->getCell('K' . $row)->getValue(),
                    'sale_date' => $sale_date ? date_create_from_format('d/m/Y', trim($sale_date))->format('Y-m-d') : null,
                    'from_date' => $from_date ? date_create_from_format('d/m/Y', trim($from_date))->format('Y-m-d') : null,
                    'to_date' => $to_date ? date_create_from_format('d/m/Y', trim($to_date))->format('Y-m-d') : null,
                    'fb_school' => $sheet->getCell('O' . $row)->getValue(),
                    'fb_district' => $sheet->getCell('P' . $row)->getValue(),
                    'fb_province' => $sheet->getCell('Q' . $row)->getValue(),
                    'buyer' => $sheet->getCell('R' . $row)->getValue(),
                    'product_id' => $productId,
                    'product_category_id' => $productCategoryId,
                    'user_name_product' => $sheet->getCell('T' . $row)->getValue(),
                    'amount' => $sheet->getCell('U' . $row)->getValue(),
                    'payment_amount' => $payment_amount,
                    'unequal' => $sheet->getCell('W' . $row)->getValue(),
                    'contract_value' => $contract_value,
                    'tax' => $sheet->getCell('Y' . $row)->getValue(),
                    'tax_money' => $sheet->getCell('Z' . $row)->getCalculatedValue(),
                    'vat' => $sheet->getCell('AA' . $row)->getValue(),
                    'invoice_request' => $invoice_request == ImportContract::YES ? true : false,
                    'received_money' => $received_money,
                    'payment_date' => $payment_date ? date_create_from_format('d/m/Y', trim($payment_date))->format('Y-m-d') : null,
                    'note' => $sheet->getCell('AE' . $row)->getValue(),
                    'sale_id' => $sale_id,
                    'leader_id' => $leader->user_id,
                    'position_leader_id' => $teams[$request->team]['id']
                ];
            }

            if (count($errors) > 0) {
                return response()->json(['message_errors' => $errors], 422);
                DB::rollBack();
            }

            if (count($tmpContracts) > 300) {
                return response()->json(['message' => 'Chỉ được import 300 hợp đồng trên 1 lần!'], 404);
                DB::rollBack();
            }

            # lưu mảng hợp đồng
            TmpContract::insert($tmpContracts);

            # lưu lại tổng số dòng thực tế
            $importContract->total_record = count($tmpContracts);
            $importContract->save();

            DB::commit();
            return ['import_contract_id' => $importContract->id];
        } catch (Exception $e) {
            Log::error('ImportContractController importTmpContractBase: ' . $e->getMessage());
            DB::rollBack();
            return Response::formatResponse(config('apicode.INTERNAL_SERVER_ERROR'), $e->getMessage());
        }
    }

    public function getTmpContractBaseList(Request $request)
    {
        try {
            $review_status = ImportContract::REVIEW_STATUS;
            $query = ImportContract::when($request->province_id, function ($subQuery, $province_id) {
                return $subQuery->where('province_id', $province_id);
            })->when($request->district_id, function ($subQuery, $district_id) {
                return $subQuery->where('district_id', $district_id);
            })->when($request->year, function ($subQuery, $year) {
                return $subQuery->whereYear('created_at', $year);
            })->with('provinceBusinessMarket', 'districtBusinessMarket', 'created_by', 'confirm_by');
            if (auth()->user()->hasAnyPermission(['review_contract'])) {
                $query->whereIn('status', [ImportContract::WAIT_STATUS, ImportContract::APPROVE_STATUS, ImportContract::REJECT_STATUS]);
            } else {
                $userIds = [auth()->user()->id];
                $child = KPISummaryUpgrade::getAllUser(KPISummaryUpgrade::getAllPosition());
                $userIds = array_merge($userIds, $child);
                $query->whereIn('created_by', $userIds);
            }
            $data = $query->orderBy('created_at', 'DESC')->paginate(20);
            $rs = [
                'data' => $data,
                'review_status' => $review_status
            ];
            return Response::formatResponse(config('apicode.SUCCESS'), $rs);
        } catch (Exception $e) {
            Log::error('ImportContractController getTmpContractBaseList: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function getTmpContractBaseDetail(Request $request, $importContractId)
    {
        try {
            if ($request->user_id != auth()->user()->id && !auth()->user()->hasAnyPermission(['review_contract'])) {
                return response()->json(['message' => 'Bạn không có quyền xem bản ghi này!'], 403);
            }
            $data = TmpContract::where('import_contract_id', $importContractId)->with('product', 'sale', 'province', 'district')->paginate(300);
            $import_contract = ImportContract::where('id', $importContractId)->first();
            $show_status_btn = false;
            $accountant_status = false;
            if ($request->user_id == auth()->user()->id && $import_contract->action_column == true) {
                $show_status_btn = true;
            }
            if (auth()->user()->hasAnyPermission(['review_contract']) && $import_contract == true) {
                $accountant_status = true;
            }
            return Response::formatResponse(config('apicode.SUCCESS'), ['data' => $data, 'show_status_btn' => $show_status_btn, 'accountant_status' => $accountant_status]);
        } catch (Exception $e) {
            Log::error('ImportContractController getTmpContractBaseDetail: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function getClient(Request $request)
    {
        try {
            $clients = Client::select('id', 'code', 'tax_code', 'province_id', 'district_id', 'name_upper', 'address')->where('province_id', $request->province_id)
                ->where('district_id', $request->district_id)->when($request->keyword, function ($q, $keyWord) {
                    $q->where(function ($sq) use ($keyWord) {
                        $sq->where('name', 'LIKE', '%' . $keyWord . '%')->orWhere('code', 'LIKE', '%' . $keyWord . '%')
                            ->orWhere('tax_code', 'LIKE', '%' . $keyWord . '%');
                    });
                })->with('provinceBusinessMarket', 'districtBusinessMarket')->get();
            return $clients;
        } catch (Exception $e) {
            Log::error('ImportContractController getClient: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function sendAccountantReview(Request $request, $importContractId)
    {
        $request->validate([
            'import_contracts.*.client_code' => 'required',
        ], [], [
            'import_contracts.*.client_code' => 'Mã khách hàng',
        ]);
        try {
            if ($request->user_id != auth()->user()->id) {
                return response()->json(['message' => 'Bạn không có quyền thao tác hành động này!'], 403);
            }
            $import_contracts = $request->import_contracts;

            DB::beginTransaction();
            foreach ($import_contracts as $item) {
                $e = (object) $item;
                $client = Client::searchClient($e->province_id, $e->district_id, $e->code);
                if (!$client) {
                    Client::store($e);
                    $client_code = $e->client_code;
                } else {
                    $client_code = $client->code;
                    Client::updateClient($e, $client->id);
                }
                //cập nhật mã khách hàng, tên xuất hóa đơn, địa chỉ xuất hóa đơn
                TmpContract::where('id', $e->id)->update(['client_code' => $client_code, 'invoice_name' => $e->invoice_name, 'invoice_address' => $e->invoice_address]);
            }

            ImportContract::where('id', $importContractId)->update(['status' => ImportContract::WAIT_STATUS]);
            DB::commit();
            return response()->json(['message' => 'Yêu cầu gửi kế toán thành công!'], 200);
        } catch (Exception $e) {
            DB::rollBack();
            Log::error('ImportContractController sendAccountantReview: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function deleteTmpContractBase(Request $request, $importContractId)
    {
        try {
            if ($request->user_id != auth()->user()->id) {
                return response()->json(['message' => 'Bạn không có quyền xóa bản ghi này!'], 403);
            }
            ImportContract::where('id', $importContractId)->delete();
            TmpContract::where('import_contract_id', $importContractId)->delete();
            return response()->json(['message' => 'Xử lý thành công!'], 200);
        } catch (Exception $e) {
            Log::error('ImportContractController deleteTmpContractBase: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function rejectTmpContractBase(Request $request, $importContractId)
    {
        try {
            if (!$request->note) {
                return response()->json(['message' => 'Ghi chú/Lý do từ chối không được bỏ trống!'], 422);
            }
            if (!auth()->user()->hasAnyPermission(['review_contract'])) {
                return response()->json(['message' => 'Bạn không có quyền thao tác hành động này!'], 403);
            }
            ImportContract::where('id', $importContractId)->update(['confirm_by' => auth()->user()->id, 'note' => $request->note, 'status' => ImportContract::REJECT_STATUS]);
            TmpContract::where('import_contract_id', $importContractId)->delete();
            return response()->json(['message' => 'Xử lý thành công!'], 200);
        } catch (Exception $e) {
            Log::error('ImportContractController rejectTmpContractBase: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function approveTmpContractBase($importContractId)
    {
        try {
            if (!auth()->user()->hasAnyPermission(['review_contract'])) {
                return response()->json(['message' => 'Bạn không có quyền thao tác hành động này!'], 403);
            }
            $contracts = TmpContract::where('import_contract_id', $importContractId)->with('import_contract')->get();
            DB::beginTransaction();
            $clients = Client::select('id', 'code')->where('province_id', $contracts[0]->province_id)->where('district_id', $contracts[0]->district_id)->get()->toArray();
            $clients = vietec_convert_array_to_map($clients, 'code');

            foreach ($contracts as $e) {
                //lưu hợp đồng chính thức
                Contract::store($e, $clients[$e->client_code]['id'], $e->import_contract->created_by, $importContractId);
            }

            //thay đổi trạng thái bảng import contract
            ImportContract::where('id', $importContractId)->update(['confirm_by' => auth()->user()->id, 'status' => ImportContract::APPROVE_STATUS]);
            TmpContract::where('import_contract_id', $importContractId)->delete();
            DB::commit();
            return response()->json(['message' => 'Xử lý thành công!'], 200);
        } catch (Exception $e) {
            DB::rollBack();
            Log::error('ImportContractController approveTmpContractBase: ' . $e->getLine() . " | " . $e->getMessage());
            return response()->json([], 500);
        }
    }
    // Mobile
    public function getTmpContractBaseDetailMobile(Request $request)
    {
        try {
            $data = TmpContract::where('id', $request->importContractId)->with('product', 'sale', 'province', 'district', 'createdBy', 'confirmBy')->first();
            if (!empty($data)) {
                if ($data->created_by != auth()->user()->id && !auth()->user()->hasAnyPermission(['review_contract'])) {
                    return response()->json(['title' => 'Bạn không có quyền xem bản ghi này!'], 403);
                }
                return (new APIJsonResponse)->responseSuccess((new TmpContractTransformer)->viewDetail($data));
            } else {
                return (new APIJsonResponse)->responseError();
            }
        } catch (Exception $e) {
            return (new APIJsonResponse)->responseError();
        }
    }
    public function getContractReview(Request $request, $importContractId)
    {
        try {
            if ($request->user_id != auth()->user()->id && !auth()->user()->hasAnyPermission(['review_contract'])) {
                return response()->json(['message' => 'Bạn không có quyền xem bản ghi này!'], 403);
            }
            $data = Contract::where('import_contract_id', $importContractId)->with('product', 'salePerson', 'provinceBusinessMarket', 'district', 'clients')->paginate(50);
            $import_contract = ImportContract::where('id', $importContractId)->first();
            $show_status_btn = false;
            $accountant_status = false;
            if ($request->user_id == auth()->user()->id && $import_contract->action_column == true) {
                $show_status_btn = true;
            }
            if (auth()->user()->hasAnyPermission(['review_contract']) && $import_contract == true) {
                $accountant_status = true;
            }
            return Response::formatResponse(config('apicode.SUCCESS'), ['data' => $data, 'show_status_btn' => $show_status_btn, 'accountant_status' => $accountant_status]);
        } catch (Exception $e) {
            Log::error('ImportContractController getTmpContractBaseDetail: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }
}
