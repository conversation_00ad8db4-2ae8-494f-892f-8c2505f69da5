<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Exception;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use App\Models\KEExWarehouseSummary;
use App\Models\KEContractAttachedAppendix;
use App\Models\Equipment;
use App\Models\KEConfirmExWarehouseHistory;
use App\Models\SubEquipment;
use App\Models\KEContract;
use App\Models\KEContractAttachedAppendixDetails;
use App\Models\SystemConfig;
use App\Models\TeamSaleArea;
use App\Models\User;
use App\Notifications\EmailNotification;
use Illuminate\Support\Facades\Notification;
use PhpOffice\PhpSpreadsheet\Style;

class KEExWareHouseController extends Controller
{
    public function index(Request $request)
    {
        try {
            $query = KEExWarehouseSummary::with(['keContractAttachedAppendix', 'createdBy', 'confirmedBy', 'keExWareHouseHistory'])
                ->when($request->ke_contract_code, function ($query, $keContractCode) {
                    $query->where('ke_contract_code', 'like', '%' . $keContractCode . '%');
                })
                ->when($request->ex_warehouse_code, function ($query, $exWarehouseCode) {
                    $query->where('ex_warehouse_code', 'like', '%' . strtoupper($exWarehouseCode) . '%');
                })
                ->when($request->bill_of_lading_no, function ($query, $billOfLadingNo) {
                    $query->where('bill_of_lading_no', 'like', '%' . $billOfLadingNo . '%');
                })
                ->when($request->created_by, function ($query, $createdBy) {
                    $query->whereHas('createdBy', function ($subQuery) use ($createdBy) {
                        return $subQuery->where('name', 'like', '%' . $createdBy . '%');
                    });
                })
                ->when($request->attached_appendix_name, function ($query, $attachedAppendixName) {
                    $query->whereHas('keContractAttachedAppendix', function ($subQuery) use ($attachedAppendixName) {
                        return $subQuery->where('attached_appendix_name', 'like', '%' . $attachedAppendixName . '%');
                    });
                })
                ->when($request->created_at, function ($query, $createdAt) {
                    $from_date = substr($createdAt, 0, 10);
                    $to_date = substr($createdAt, 14, 25);
                    $query->whereDate('created_at', '>=', $from_date)
                        ->whereDate('created_at', '<=', $to_date);
                });
            if (auth()->user()->user) {
                $checkTeamSale = TeamSaleArea::getProvincesAndDistrictsBySaleArea(auth()->user());
                if ($checkTeamSale['provinceIds']) {
                    $query->whereHas('contract', function ($q) {
                        return $q->where('sale_id', auth()->user()->id);
                    });
                }
            }

            $rs = $query->orderBy('updated_at', 'DESC')
                ->orderBy('ke_contract_id')
                ->paginate(20);

            return response()->json([
                'list' => $rs,
                'status' => KEExWarehouseSummary::STATUS
            ]);
        } catch (Exception $e) {
            Log::error('KEExWareHouseController index: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function getContracts(Request $request)
    {
        try {
            $pageSize = $request->pageSize ?: 20;
            $rs = KEContract::with([
                'clients',
                'attachedAppendixs' => function ($subQuery) {
                    return $subQuery->where('isExWareHouse', false)
                        ->whereHas('attachedDetails', function ($q) {
                            $q->whereColumn('amount', '>', 'quantity_shipped');
                        });
                },
                'attachedAppendixs.attachedDetails' => function ($q) {
                    $q->whereColumn('amount', '>', 'quantity_shipped');
                },
                'attachedAppendixs.attachedDetails.equipmentType'
            ])
                ->whereHas('attachedAppendixs.attachedDetails', function ($q) {
                    $q->whereColumn('amount', '>', 'quantity_shipped');
                })
                ->when($request->contractCode, function ($subQuery, $contractCode) {
                    $subQuery->where('contract_code', 'LIKE', '%' . $contractCode . '%');
                })
                ->whereDate('end_date', '>=', Carbon::now())
                ->orderBy('updated_at', 'DESC')
                ->orderBy('end_date', 'DESC')
                ->paginate($pageSize);

            return response()->json(['data' => $rs]);
        } catch (Exception $e) {
            Log::error('KEExWareHouseController index: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function getWareHouses(Request $request)
    {
        $request->validate(['equipment_type_id' => 'required']);
        try {
            $rs = Equipment::with([
                'warehouse',
                'sub_equipments' => function ($subQuery) {
                    return $subQuery->where('is_used', false);
                }
            ])
                ->whereIn('equipment_type_id', explode(",", $request->equipment_type_id))
                ->get();

            return response()->json($rs);
        } catch (Exception $e) {
            Log::error('KEExWareHouseController getWareHouses: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function store(Request $request)
    {
        $request->validate([
            'ke_contract_id' => 'required|uuid',
            'ke_contract_code' => 'required',
            'ke_contract_attached_appendix_id' => 'required|uuid',
            'attachedAppendixItems' => 'array|required|min:1'
        ], [], [
            'ke_contract_id' => 'Mã hợp đồng',
            'ke_contract_code' => 'Mã hợp đồng',
            'ke_contract_attached_appendix_id' => 'Phụ lục',
            'attachedAppendixItems' => 'Hàng hóa'
        ]);

        $attachedDetails = KEContractAttachedAppendixDetails::where('ke_contract_attached_appendix_id', $request->ke_contract_attached_appendix_id)
            ->get();
        if (!$attachedDetails) {
            return response()->json(['message' => 'Dữ liệu không tồn tại!'], 404);
        }

        DB::beginTransaction();
        try {
            $model = new KEExWarehouseSummary();
            $model->ke_contract_id = $request->ke_contract_id;
            $model->ke_contract_code = $request->ke_contract_code;
            $model->ke_contract_attached_appendix_id = $request->ke_contract_attached_appendix_id;
            $model->ex_warehouse_code = strtoupper($request->ex_warehouse_code);
            $model->bill_of_lading_no = $request->bill_of_lading_no;
            $model->costs_incurred = $request->costs_incurred;
            $model->created_by = $request->user()->id;
            $model->status = KEExWarehouseSummary::STATUS_PENDING;
            $model->save();

            $data = [];
            $checkAmount = [];
            foreach ($request->attachedAppendixItems as $item) {
                if (is_array($item['sub_level'])) {
                    if (count($item['sub_level']) != $item['amount']) {
                        DB::rollBack();
                        return response()->json(['message' => 'Số lượng của 1 loại hàng hóa không hợp lệ!'], 400);
                    }

                    SubEquipment::whereIn('code', $item['sub_level'])
                        ->update(['is_used' => true]);
                }

                $data[] = [
                    'id' => Str::uuid()->toString(),
                    'ke_contract_id' => $request->ke_contract_id,
                    'ke_contract_code' => $request->ke_contract_code,
                    'ke_contract_attached_appendix_id' => $request->ke_contract_attached_appendix_id,
                    'ke_contract_attached_appendix_detail_id' => $item['ke_contract_attached_appendix_detail_id'],
                    'equipment_type_id' => $item['equipment_type_id'],
                    'warehouse_id' => $item['warehouse_id'],
                    'amount' => $item['amount'],
                    'sub_level' => is_array($item['sub_level']) ? implode(",", $item['sub_level']) : $item['sub_level'],
                    'created_by' => $request->user()->id,
                    'ke_ex_warehouse_summary_id' => $model->id,
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now(),
                ];

                if (!isset($checkAmount[$item['ke_contract_attached_appendix_detail_id']])) {
                    $checkAmount[$item['ke_contract_attached_appendix_detail_id']] = 0;
                }

                $checkAmount[$item['ke_contract_attached_appendix_detail_id']] += (int)$item['amount'];

                if ($item['warehouse_id'] && $item['amount'] && (int)$item['amount'] > 0) {
                    Equipment::where('equipment_type_id', $item['equipment_type_id'])
                        ->where('warehouse_id', $item['warehouse_id'])
                        ->update(['amount' => DB::raw('amount - ' . (int)$item['amount'])]);
                }
            }
            foreach ($checkAmount as $id => $amount) {
                $model = $attachedDetails->where('id', $id)->first();
                if (!$model || $model->amount < $model->quantity_shipped + $amount) {
                    DB::rollBack();
                    return response()->json(['message' => 'Số lượng của 1 loại hàng hóa không hợp lệ!'], 400);
                } else {
                    $model->quantity_shipped += $amount;
                    $model->save();
                }
            }

            // $attachedDetailsIsDone = KEContractAttachedAppendixDetails::where('ke_contract_attached_appendix_id', $request->ke_contract_attached_appendix_id)
            //     ->whereColumn('amount', 'quantity_shipped')
            //     ->count();

            // if ($attachedDetailsIsDone == count($attachedDetails)) {
            //     KEContractAttachedAppendix::where('id', $request->ke_contract_attached_appendix_id)
            //         ->update(['isExWareHouse' => true]);
            // }

            if ($data) {
                DB::table('ke_ex_ware_house_history')->insert($data);
            }

            DB::commit();

            // # bắn notify cho người đc assign
            $rs = SystemConfig::confirmExWarehouse()->first();
            if ($rs && !empty($rs->content)) {
                $receiveEmails = User::whereIn('email', explode(',', $rs->content))->get();
                $message = [
                    'subject' => '[' . env('APP_NAME') . ' - KE] - Yêu cầu xác nhận phiếu xuất kho số ' . $model->ex_warehouse_code,
                    'greeting' => 'Xin chào,',
                    'body' => 'Bạn vừa nhận được yêu cầu xác nhận phiếu xuất kho số ' . $model->ex_warehouse_code . ' từ hợp đồng số: ' . $model->ke_contract_code,
                    'actionTxt' => 'Xem chi tiết',
                    'actionUrl' => env('APP_URL') . '/ex-warehouse/detail/' . $model->id,
                ];

                Notification::send($receiveEmails, new EmailNotification($message));
            }
            return response()->json(['message' => 'Tạo phiếu xuất kho thành công, chờ xác nhận!']);
        } catch (Exception $e) {
            DB::rollBack();
            Log::error('KEExWareHouseController store: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function details(Request $request)
    {
        try {
            $rs = KEExWarehouseSummary::with([
                'keContractAttachedAppendix',
                'keExWarehouseHistory' => function ($q) {
                    $q->withTrashed();
                },
                'keExWarehouseHistory.warehouse',
                'keExWarehouseHistory.equipmentType',
                'confirmedBy',
                'keConfirmExWarehouseHistory' => function ($q) {
                    $q->orderBy('created_at', 'DESC');
                },
                'keConfirmExWarehouseHistory.createdBy.avatar'
            ])
                ->where('id', $request->id)
                ->first();
            if (!$rs) {
                return response()->json(['message' => 'Dữ liệu không tồn tại'], 404);
            }

            return response()->json([
                'info' => $rs,
                'status' => KEExWarehouseSummary::STATUS
            ]);
        } catch (Exception $e) {
            Log::error('KEExWareHouseController details: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function confirmExWarehouse(Request $request)
    {
        $request->validate([
            'reason' => 'max:255'
        ], [], [
            'reason' => 'Lý do'
        ]);
        DB::beginTransaction();
        try {
            $model = KEExWarehouseSummary::where('id', $request->id)
                ->where('ex_warehouse_code', $request->code)
                ->where('status', KEExWarehouseSummary::STATUS_PENDING)
                ->first();

            if (!$model) {
                return response()->json(['message' => 'Dữ liệu không tồn tại!'], 404);
            }

            $model->confirmed_by = $request->user()->id;
            $model->status = KEExWarehouseSummary::STATUS_OK;
            $model->save();

            KEConfirmExWarehouseHistory::create([
                'ke_ex_warehouse_summary_id' => $model->id,
                'reason' => $request->reason,
                'status' => $model->status,
                'created_by' => $request->user()->id
            ]);

            $attachedDetailsIsDone = KEContractAttachedAppendixDetails::where('ke_contract_attached_appendix_id', $model->ke_contract_attached_appendix_id)
                ->whereColumn('amount', '>', 'quantity_shipped')
                ->count();

            if ($attachedDetailsIsDone == 0) {
                KEContractAttachedAppendix::where('id', $request->ke_contract_attached_appendix_id)
                    ->update(['isExWareHouse' => true]);
            }

            DB::commit();
            // # bắn notify cho người đc assign
            $rs = SystemConfig::confirmExWarehouse()->first();
            if ($rs && !empty($rs->content)) {
                $receiveEmails = User::whereIn('email', explode(',', $rs->content))->get();
                $message = [
                    'subject' => '[' . env('APP_NAME') . ' - KE] - Yêu cầu xác nhận phiếu xuất kho số ' . $model->ex_warehouse_code,
                    'greeting' => 'Xin chào,',
                    'body' => 'Yêu cầu xác nhận phiếu xuất kho số ' . $model->ex_warehouse_code . ' đã được "Đồng ý"',
                    'actionTxt' => 'Xem chi tiết',
                    'actionUrl' => env('APP_URL') . '/ex-warehouse/detail/' . $model->id,
                ];

                Notification::send($receiveEmails, new EmailNotification($message));
            }
            // thông báo cho người sở hữu hợp đồng
            $model->contract()->seller()->notify(new EmailNotification([
                'subject' => '[' . env('APP_NAME') . ' - KE] - Yêu cầu xuất kho hợp đồng số ' . $model->ex_warehouse_code,
                'greeting' => 'Xin chào,',
                'body' => 'Yêu cầu xuất kho hợp đồng số ' . $model->ke_contract_code . ' đã được xác nhận. Vui lòng liên hệ bộ phận dự án để bàn giao.',
                'actionTxt' => 'Xem chi tiết',
                'actionUrl' => env('APP_URL') . '/ex-warehouse/detail/' . $model->id,
            ]));

            return response()->json(['message' => 'Xác nhận thành công!']);
        } catch (Exception $e) {
            Log::error('KEExWareHouseController confirmExWarehouse: ' . $e->getMessage());
            DB::rollBack();
            return response()->json([], 500);
        }
    }

    public function rejectExWarehouse(Request $request)
    {
        $request->validate([
            'reason' => 'max:255'
        ], [], [
            'reason' => 'Lý do'
        ]);
        DB::beginTransaction();
        try {
            $model = KEExWarehouseSummary::with('keExWarehouseHistory.equipmentType', 'keExWarehouseHistory.keContractAttachedAppendixDetails')
                ->where('id', $request->id)
                ->where('ex_warehouse_code', $request->code)
                ->where('status', KEExWarehouseSummary::STATUS_PENDING)
                ->first();

            if (!$model) {
                return response()->json(['message' => 'Dữ liệu không tồn tại!'], 404);
            }

            $model->confirmed_by = $request->user()->id;
            $model->status = KEExWarehouseSummary::STATUS_REJECT;
            $model->save();

            KEConfirmExWarehouseHistory::create([
                'ke_ex_warehouse_summary_id' => $model->id,
                'reason' => $request->reason,
                'status' => $model->status,
                'created_by' => $request->user()->id
            ]);

            /** cập nhật lại trạng thái các mã thiết bị về chưa sử dụng */
            $codes = [];
            foreach ($model->keExWarehouseHistory as $item) {
                if ($item->equipmentType->require_code) {
                    $codes = array_merge($codes, explode(',', $item->sub_level));
                }
            }

            if ($codes) {
                SubEquipment::whereIn('code', $codes)
                    ->update(['is_used' => false]);
            }

            /** cập nhật lại số lượng trong kho */
            foreach ($model->keExWarehouseHistory as $item) {
                $item->keContractAttachedAppendixDetails->quantity_shipped = $item->keContractAttachedAppendixDetails->quantity_shipped - $item->amount;
                $item->keContractAttachedAppendixDetails->save();

                Equipment::where('equipment_type_id', $item->equipment_type_id)
                    ->where('warehouse_id', $item->warehouse_id)
                    ->update(['amount' => DB::raw('amount + ' . (int)$item->amount)]);
            }

            /** xóa lịch sử xuất kho */
            $model->keExWarehouseHistory()->delete();

            DB::commit();
            // # bắn notify cho người đc assign
            $rs = SystemConfig::requestExWarehouse()->first();
            if ($rs && !empty($rs->content)) {
                $receiveEmails = User::whereIn('email', explode(',', $rs->content))->get();
                $message = [
                    'subject' => '[' . env('APP_NAME') . ' - KE] - Yêu cầu xác nhận phiếu xuất kho số ' . $model->ex_warehouse_code,
                    'greeting' => 'Xin chào,',
                    'body' => 'Yêu cầu xác nhận phiếu xuất kho số ' . $model->ex_warehouse_code . ' đã bị "Từ chối"',
                    'text' => 'Lý do: ' . $model->reason,
                    'actionTxt' => 'Xem chi tiết',
                    'actionUrl' => env('APP_URL') . '/ex-warehouse/detail/' . $model->id,
                ];

                Notification::send($receiveEmails, new EmailNotification($message));
            }

            return response()->json(['message' => 'Từ chối thành công!']);
        } catch (Exception $e) {
            Log::error('KEExWareHouseController rejectExWarehouse: ' . $e->getMessage());
            DB::rollBack();
            return response()->json([], 500);
        }
    }

    public function commentExWarehouse(Request $request)
    {
        $request->validate([
            'reason' => 'max:255',
        ], [], [
            'reason' => 'Nội dung',
        ]);
        DB::beginTransaction();
        try {
            $model = KEExWarehouseSummary::with('contract')
                ->where('id', $request->id)
                ->where('ex_warehouse_code', $request->code)
                ->first();

            if (!$model) {
                return response()->json(['message' => 'Dữ liệu không tồn tại!'], 404);
            }

            if ($request->user()->cannot('commentExWarehouse', $model)) {
                return response()->json([], 403);
            }

            $rs = KEConfirmExWarehouseHistory::create([
                'ke_ex_warehouse_summary_id' => $model->id,
                'reason' => $request->reason,
                'status' => $model->status,
                'created_by' => $request->user()->id
            ]);

            DB::commit();

            // # bắn notify cho người đc assign
            $rsRequest = SystemConfig::requestExWarehouse()->first();
            $rsConfirm = SystemConfig::confirmExWarehouse()->first();
            if (($rsRequest && !empty($rsRequest->content)) || ($rsConfirm && !empty($rsConfirm->content))) {
                $emails = !empty($rsRequest->content) ? explode(',', $rsRequest->content) : [];
                $emails = array_merge($emails, !empty($rsConfirm->content) ? explode(',', $rsConfirm->content) : []);
                $receiveEmails = User::whereIn('email', $emails)->get();
                $message = [
                    'subject' => '[' . env('APP_NAME') . ' - KE] - Phản hồi phiếu xuất kho số ' . $model->ex_warehouse_code,
                    'greeting' => 'Xin chào,',
                    'body' => 'Có ai đó đã phản hồi về phiếu xuất kho số ' . $model->ex_warehouse_code . ' từ hợp đồng số: ' . $model->ke_contract_code,
                    'actionTxt' => 'Xem chi tiết',
                    'actionUrl' => env('APP_URL') . '/ex-warehouse/detail/' . $model->id,
                ];

                Notification::send($receiveEmails, new EmailNotification($message));
            }

            return response()->json([
                'message' => 'Phản hồi thành công!',
                'comments' => KEConfirmExWarehouseHistory::with('createdBy.avatar')
                    ->orderBy('created_at', 'DESC')
                    ->get()
            ]);
        } catch (Exception $e) {
            Log::error('KEExWareHouseController commentExWarehouse: ' . $e->getMessage());
            DB::rollBack();
            return response()->json([], 500);
        }
    }

    public function delete(Request $request)
    {
        DB::beginTransaction();
        try {
            $model = KEExWarehouseSummary::with([
                'keExWarehouseHistory.keContractAttachedAppendixDetails'
            ])
                ->where('id', $request->id)
                ->whereNull('confirmed_by')
                ->first();
            if (!$model) {
                return response()->json(['message' => 'Dữ liệu không tồn tại'], 404);
            }

            foreach ($model->keExWarehouseHistory as $item) {
                $item->keContractAttachedAppendixDetails->quantity_shipped = $item->keContractAttachedAppendixDetails->quantity_shipped - $item->amount;
                $item->keContractAttachedAppendixDetails->save();
            }

            $model->keExWarehouseHistory()->delete();
            $model->keConfirmExWarehouseHistory()->delete();
            $model->delete();
            DB::commit();
            return response()->json(['message' => 'Xóa phiếu xuất kho thành công!']);
        } catch (Exception $e) {
            Log::error('KEExWareHouseController delete: ' . $e->getMessage());
            DB::rollback();
            return response()->json([], 500);
        }
    }

    public function exportWarehouse(Request $request, $id)
    {
        $inputFileName = 'templates/ex_warehouse.xlsx';
        $fileType = \PhpOffice\PhpSpreadsheet\IOFactory::identify($inputFileName);
        $objReader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader($fileType);
        $objPHPExcel = $objReader->load($inputFileName);
        $sheet = $objPHPExcel->setActiveSheetIndex(0);

        $ex_warehouse = KEExWarehouseSummary::with([
            'contract.seller',
            'contract.clients',
            'createdBy',
            'keExWarehouseHistory.warehouse',
            'keExWarehouseHistory.equipmentType'
        ])
            ->where('id', $id)
            ->where('status', KEExWarehouseSummary::STATUS_OK)
            ->first();
        $ke_contract_attach_appendix = $ex_warehouse->keExWarehouseHistory;
        $sum = count($ke_contract_attach_appendix);

        $year = $ex_warehouse->created_at->year;
        $month = $ex_warehouse->created_at->month;
        $day = $ex_warehouse->created_at->day;

        $sheet->setCellValue('A6', 'Mã phiếu xuất kho: ' . $ex_warehouse->ex_warehouse_code);
        $sheet->setCellValue('A7', 'Nhập, Ngày ' . $day . ' tháng ' . $month . ' năm ' . $year);
        $sheet->setCellValue('C9', $ex_warehouse->contract->seller->name);
        $sheet->setCellValue('C10', $ex_warehouse->contract->clients->name);
        $sheet->setCellValue('C11', $ex_warehouse->contract->clients->head_master_name);
        $sheet->setCellValue('C12', $ex_warehouse->contract->clients->address);
        $sheet->setCellValue('C16', $ke_contract_attach_appendix[0]->warehouse->name);

        $row = 19;
        foreach ($ke_contract_attach_appendix as $k => $e) {
            $sheet->setCellValue('A' . $row, $k + 1)->getStyle('A' . $row)->getAlignment()->setHorizontal('center');
            $sheet->mergeCells('B' . 19 + $k . ':C' . 19 + $k  . '')->setCellValue('B' . $row, $e->equipmentType->name);
            $sheet->setCellValue('D' . $row, $e->equipmentType->unit)->getStyle('D' . $row)->getAlignment()->setHorizontal('center');
            $sheet->setCellValue('E' . $row, $e->amount)->getStyle('E' . $row)->getAlignment()->setHorizontal('center');
            $sheet->setCellValue('F' . $row, $e->sub_level ? $e->sub_level : "");
            $sheet->getStyle('A' . 19 + $k  . ':F' . 19 + $k . '')->getBorders()->getAllBorders()->setBorderStyle(Style\Border::BORDER_THIN);
            $row++;
        }

        $sheet->mergeCells('D' . 20 + $sum  . ':F' . 20 + $sum . '')->setCellValue('D' . $sum + 20, 'Nhập, Ngày ' . $day . ' tháng ' . $month . ' năm ' . $year)
            ->getStyle('D' . 20 + $sum . ':F' . 20 + $sum . '')->getFont()->setItalic(true);
        $sheet->getStyle('D' . 20 + $sum . ':F' . 20 + $sum . '')->getAlignment()->setHorizontal('right');

        $sheet->mergeCells('A' . 21 + $sum  . ':B' . 21 + $sum  . '')->setCellValue('A' . $sum + 21, 'Người nhận hàng')
            ->getStyle('A' . 21 + $sum  . ':B' . 21 + $sum . '')->getFont()->setBold(true);
        $sheet->mergeCells('C' . 21 + $sum  . ':D' . 21 + $sum . '')->setCellValue('C' . $sum + 21, 'Nhân viên kinh doanh')
            ->getStyle('C' . 21 + $sum . ':D' . 21 + $sum . '')->getFont()->setBold(true);
        $sheet->mergeCells('E' . 21 + $sum . ':F' . 21 + $sum . '')->setCellValue('E' . $sum + 21, 'Người lập phiếu')
            ->getStyle('E' . 21 + $sum . ':F' . 21 + $sum . '')->getFont()->setBold(true);

        $sheet->mergeCells('A' . 22 + $sum . ':B' . 22 + $sum  . '')->setCellValue('A' . $sum + 22, '(Ký và ghi rõ họ tên)')
            ->getStyle('A' . 22 + $sum  . ':B' . 22 + $sum  . '')->getFont()->setItalic(true);
        $sheet->mergeCells('C' . 22 + $sum  . ':D' . 22 + $sum  . '')->setCellValue('C' . $sum + 22, '(Ký và ghi rõ họ tên)')
            ->getStyle('C' . 22 + $sum  . ':D' . 22 + $sum  . '')->getFont()->setItalic(true);
        $sheet->mergeCells('E' . 22 + $sum  . ':F' . 22 + $sum  . '')->setCellValue('E' . $sum + 22, '(Ký và ghi rõ họ tên)')
            ->getStyle('E' . 22 + $sum  . ':F' . 22 + $sum  . '')->getFont()->setItalic(true);
        $sheet->getStyle('E' . 22 + $sum  . ':F' . 22 + $sum  . '')->getAlignment()->setHorizontal('center');

        $sheet->getStyle('E' . 27 + $sum  . ':F' . 27 + $sum   . '')->getAlignment()->setHorizontal('center');
        $sheet->mergeCells('E' . 27 + $sum  . ':F' . 27 + $sum  . '')->setCellValue('E' . $sum + 27, $ex_warehouse->createdBy->name)
            ->getStyle('E' . 27 + $sum  . ':F' . 27 + $sum   . '')->getFont()->setBold(true);
        $sheet->getStyle('E' . 27 + $sum  . ':F' . 27 + $sum   . '')->getAlignment()->setHorizontal('center');

        $objWriter = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($objPHPExcel, $fileType);
        try {
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename="Phieu xuat kho.xlsx"');
            header('Cache-Control: max-age=0');
            $objWriter->save("php://output");
        } catch (\Exception $exception) {
            throw $exception;
        }
    }
}