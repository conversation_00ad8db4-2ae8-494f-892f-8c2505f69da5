<?php

namespace App\Http\Controllers;

use App\Jobs\QueueName;
use App\Jobs\StoreHistoryAssessmentKpiJob;
use Illuminate\Http\Request;
use Exception;
use Illuminate\Support\Facades\Log;
use App\Models\KPI;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use App\Models\HistoryAssessmentKpi;
use App\Models\KPISummary;
use App\Models\Position;
use App\Models\SystemConfig;
use App\Models\UserWorking;
use App\Notifications\EmailNotification;
use App\Services\UserService;
use PhpOffice\PhpSpreadsheet\Style;

class KPIAssessmentController extends Controller
{
    private $fromDate;
    private $toDate;

    public function __construct()
    {
        $rs = KPISummary::whereNotIn('status', KPISummary::STATUS_REGISTERED_KPI)
            ->orderBy('to_date', 'DESC')
            ->first();

        if ($rs) {
            $this->fromDate = $rs->from_date;
            $this->toDate = $rs->to_date;
        } else {
            $this->fromDate = date('Y-m-d');
            $this->toDate = date('Y-m-d');
        }
    }

    public function index(Request $request)
    {
        try {
            $query = KPISummary::with(['owner.avatar', 'owner.user', 'department', 'position', 'leader'])->whereHas('owner.user', function ($q) {
                $q->whereNull('end_date')
                    ->orWhere('end_date', '>=', date('Y-m-d'));
            });
            $department_id = $request->department_id;
            $rank_code = $request->rank_code;
            //query theo tung vi tri truyen vao
            KPISummary::getConditionList($request, $query, $department_id, $rank_code);
            $date = explode('-', $request->month);
            $year = $date[1] ?: Carbon::parse($this->toDate)->format('Y');
            $month = $date[0] ?: Carbon::parse($this->toDate)->format('m');
            $rs = $query->whereNotIn('status', [KPISummary::STAFF_REGISTER_KPI, KPISummary::LEADER_REOPEN_KPI])
                ->when($year, function ($q, $year) {
                    $q->whereYear('to_date', $year);
                })
                ->when($month, function ($q, $month) {
                    $q->whereMonth('to_date', $month);
                })
                ->orderBy('to_date', 'DESC')
                ->orderBy('user_id')
                ->paginate(20);

            return response()->json(['data' => $rs]);
        } catch (Exception $e) {
            Log::error("KpiAssessmentController index: " . $e->getMessage());
            return response()->json([], 500);
        }
    }


    /** Lấy nhóm tiêu chí */
    public function getCriteriaGroup()
    {
        return response()->json(KPI::CRITERIA_GROUP);
    }

    /** Lấy thông tin tiêu chí KPI 
     * để Nhân viên tự đánh giá 
     * hoặc CBLĐ đánh giá nhân viên
     * @param int kpiSummaryId
     * */
    public function getCriteriaForAssessment(Request $request, $kpiSummaryId)
    {
        try {
            $rs = KPISummary::with('owner.user', 'kpi.tasks.project', 'kpi.tasks.status')
                ->whereNotIn('status', KPISummary::STATUS_REGISTERED_KPI)
                ->where('id', $kpiSummaryId)
                ->first();

            if (!$rs) {
                return response()->json(['message' => 'Không tìm thấy dữ liệu'], 404);
            }

            if (
                $rs->user_id == auth()->user()->id
                || in_array($rs->owner->user->dependent_position_id, KPISummary::getAllPosition())
                || auth()->user()->hasAnyPermission(['leader_send_kpi_to_hr', 'approve_kpi', 'kpi_list_management'])
            ) {
                $config = SystemConfig::getKpiAssessmentTime();

                $isAllowStaffSelfAssessment = $this->isAllowStaffSelfAssessment($rs, $config)->status;

                $isAllowAssessmentLeader = $this->isAllowAssessmentLeader($rs, $config)->status;

                $isAllowReopenAssessmentLeader = $this->isAllowReopenAssessmentLeader($rs, $config)->status;
                // $a = (object) $this->calcDiligent($rs->user_id);
                // return $rs;
                return response()->json([
                    'criterias' => $rs,
                    'diligent' => $this->calcDiligent($rs->user_id, $rs->from_date, $rs->to_date),
                    'isAllowStaffSelfAssessment' => $isAllowStaffSelfAssessment,
                    'isAllowAssessmentLeader' => $isAllowAssessmentLeader,
                    'isAllowReopenAssessmentLeader' => $isAllowReopenAssessmentLeader,
                ]);
            } else {
                return response()->json([], 403);
            }
        } catch (Exception $e) {
            Log::error("KpiAssessmentController getCriteriaForAssessment: " . $e->getMessage());
            return response()->json([], 500);
        }
    }

    /** 
     * CBNV/Trưởng phòng tự đánh giá kết quả KPI 
     * @param int kpiSummaryId
     * @param array criteria
     * */
    public function saveStaffSelfAssessment(Request $request)
    {
        $request->validate([
            'kpiSummaryId' => 'required',
            'criteria' => 'required|array',
            'criteria.*.id' => 'required',
            'criteria.*.score' => 'required|numeric',
        ], [], [
            'kpiSummaryId' => 'Thông tin đánh giá',
            'criteria' => 'Tiêu chí',
            'criteria.*.id' => 'Tiêu chí',
            'criteria.*.score' => 'Điểm tự đánh giá',
        ]);

        try {

            $originalData = KPISummary::with('kpi.tasks.project', 'kpi.tasks.status')
                ->where('id', $request->kpiSummaryId)
                // ->where('from_date', $this->fromDate)
                // ->where('to_date', $this->toDate)
                // ->whereIn('status', KPISummary::STATUS_FOR_SELF_ASSESSMENT_KPI)
                ->where('user_id', auth()->user()->id)
                ->first();

            $checkPermission = $this->isAllowStaffSelfAssessment($originalData);

            if (!$checkPermission->status) {
                return response()->json(['message' => $checkPermission->msg], $checkPermission->code);
            }

            $originalDataHistory = json_encode($originalData);
            DB::beginTransaction();

            $ids = [];
            $selfScore = 0;
            foreach ($request->criteria as $item) {
                $ids[] = $item['id'];
                $model = KPI::where('kpi_summary_id', $originalData->id)
                    ->where('id', $item['id'])
                    ->first();
                if (!$model) continue;
                $model->self_assessment_score = $item['score'];
                $model->target_content = $item['target_content'];
                $model->measure_method = $item['measure_method'];
                $model->leader_assessment_score = 0;
                $model->leader_note = '';
                $model->save();

                $selfScore += $model->proportion * $item['score'] / 100;
            }

            $originalData->self_assessment_score = $selfScore;
            $originalData->leader_assessment_score = 0;
            $originalData->status = KPISummary::STAFF_SELF_ASSESSMENT_KPI;
            $originalData->leader_assessment_by = null;
            $originalData->hr_assessment_by = null;
            $originalData->self_assessment_rank = KPISummary::getRank($selfScore, $originalData->level_position);
            $originalData->leader_assessment_rank = '';
            $originalData->result_assessment_rank = '';
            $originalData->save();

            DB::commit();

            /** 
             * save history 
             * */
            $working = auth()->user()->user;
            $newData = json_encode($originalData);
            $jobData = [
                'user_id' => auth()->user()->id,
                'department_id' => $working->department_id,
                'owner' => auth()->user()->id,
                'position_of_owner' => $working->position_id,
                'action_name' => HistoryAssessmentKpi::STAFF_SELF_ASSESSMENT_KPI,
                'original_data' => $originalDataHistory,
                'new_data' => $newData
            ];

            $job = (new StoreHistoryAssessmentKpiJob($jobData))->onQueue(QueueName::STORE_HISTORY_ASSESSMENT_KPI);
            dispatch($job);

            /**
             * send email thông báo cho CBLĐ
             */

            $manager = UserWorking::with('user')
                ->allActive()
                ->where('position_id', $request->user()->user->leader->id)
                ->first();

            if ($manager) {
                $notifier = $manager->user;
                $message = [
                    'subject' => '[' . env('APP_NAME') . '][' . auth()->user()->name . '] - Kết quả tự đánh giá KPI tháng' . Carbon::parse($originalData->to_date)->format('m/Y'),
                    'greeting' => 'Xin chào, ' . $notifier->name,
                    'body' => 'Bạn vừa nhận được kết quả tự đánh giá KPI của nhân viên:' . auth()->user()->name,
                    'actionTxt' => 'Xem chi tiết',
                    'actionUrl' => env('APP_URL') . '/kpi/assessment-details-kpi/' . $originalData->id,
                    'type' => '6',
                ];

                $notifier->notify(new EmailNotification($message));
            }

            return response()->json(['message' => 'Kết quả tự đánh giá của bạn đã được gửi tới CBLĐ']);
        } catch (Exception $e) {
            DB::rollBack();
            Log::error("KpiAssessmentController saveStaffSelfAssessment: " . $e->getMessage());
            return response()->json([], 500);
        }
    }

    /** 
     * CBLĐ/Trưởng phòng đánh giá nhân viên 
     * @param int kpiSummaryId
     * @param array criteria
     * */
    public function saveAssessmentLeader(Request $request)
    {
        $request->validate([
            'kpiSummaryId' => 'required',
            'userId' => 'required',
            'criteria' => 'required|array',
            'criteria.*.id' => 'required',
            'criteria.*.score' => 'required|numeric',
        ], [], [
            'kpiSummaryId' => 'Thông tin đánh giá',
            'userId' => 'CBNV',
            'criteria' => 'Tiêu chí',
            'criteria.*.id' => 'Tiêu chí',
            'criteria.*.score' => 'Điểm CBLĐ đánh giá',
        ]);

        try {
            $status = [
                KPISummary::STAFF_SELF_ASSESSMENT_KPI,
                KPISummary::LEADER_REOPEN_SELF_ASSESSMENT_KPI,
            ];
            // return $request->all();
            $originalData = KPISummary::with('owner', 'kpi.tasks.project', 'kpi.tasks.status')
                ->whereIn('status', $status)
                ->where('id', $request->kpiSummaryId)
                ->where('user_id', $request->userId)
                // ->where('from_date', $this->fromDate)
                // ->where('to_date', $this->toDate)
                ->first();

            $checkPermission = $this->isAllowAssessmentLeader($originalData);
            if (!$checkPermission->status) {
                return response()->json(['message' => $checkPermission->msg], $checkPermission->code);
            }

            $originalDataHistory = json_encode($originalData);
            DB::beginTransaction();
            $leaderScore = 0;
            foreach ($request->criteria as $item) {
                $model = KPI::where('kpi_summary_id', $originalData->id)
                    ->where('id', $item['id'])
                    ->first();
                if (!$model) continue;
                $model->leader_assessment_score = $item['score'];
                $model->leader_note = $item['leader_note'];
                $model->save();

                $leaderScore += $model->proportion * $item['score'] / 100;
            }

            $originalData->leader_assessment_score = $leaderScore;
            $originalData->status = KPISummary::LEADER_ASSESSMENT_KPI;
            $originalData->leader_assessment_rank = KPISummary::getRank($leaderScore, $originalData->level_position);
            $originalData->result_assessment_rank = KPISummary::getRank($leaderScore, $originalData->level_position);
            $originalData->leader_assessment_by = auth()->user()->id;
            $originalData->save();
            DB::commit();

            /** 
             * save history 
             * */
            $owner = auth()->user()->user;
            $newData = json_encode($originalData);
            $jobData = [
                'user_id' => $originalData->user_id,
                'department_id' => $owner->department_id,
                'owner' => auth()->user()->id,
                'position_of_owner' => $owner->position_id,
                'action_name' => HistoryAssessmentKpi::LEADER_ASSESSMENT_KPI,
                'original_data' => $originalDataHistory,
                'new_data' => $newData
            ];
            $job = (new StoreHistoryAssessmentKpiJob($jobData))->onQueue(QueueName::STORE_HISTORY_ASSESSMENT_KPI);
            dispatch($job);

            /**
             * send email thông báo cho nhân viên
             */
            $notifier = $originalData->owner;
            $message = [
                'subject' => '[' . env('APP_NAME') . '] - Kết quả CBLĐ đánh giá KPI tháng ' . Carbon::parse($originalData->to_date)->format('m/Y'),
                'greeting' => 'Xin chào, ' . $notifier->name,
                'body' => 'Bạn vừa nhận được kết quả CBLĐ đánh giá KPI tháng ' . Carbon::parse($originalData->to_date)->format('m/Y'),
                'actionTxt' => 'Xem chi tiết',
                'actionUrl' => env('APP_URL') . '/kpi/assessment-details-kpi/' . $originalData->id,
                'type' => '6',
            ];

            $notifier->notify(new EmailNotification($message));

            return response()->json(['message' => 'Xử lý thành công']);
        } catch (Exception $e) {
            DB::rollBack();
            Log::error("KpiAssessmentController saveAssessmentLeader: " . $e->getMessage());
            return response()->json([], 500);
        }
    }

    /**
     * Leader mở lại tự đánh giá
     * @param int kpiSummaryId
     * @param int userId
     */
    public function leaderReopenStaffSelfAssessment(Request $request)
    {
        try {
            $status = [
                KPISummary::STAFF_SELF_ASSESSMENT_KPI,
                KPISummary::LEADER_ASSESSMENT_KPI,
            ];

            $originalData = KPISummary::with('kpi.tasks.project', 'kpi.tasks.status')
                ->whereIn('status', $status)
                ->where('id', $request->kpiSummaryId)
                ->where('user_id', $request->userId)
                // ->where('department_id', auth()->user()->user->department_id)
                // ->where('from_date', $this->fromDate)
                // ->where('to_date', $this->toDate)
                ->first();

            $checkPermission = $this->isAllowReopenAssessmentLeader($originalData);
            if (!$checkPermission->status) {
                return response()->json(['message' => $checkPermission->msg], $checkPermission->code);
            }
            $originalDataHistory = json_encode($originalData);

            $originalData->status = KPISummary::LEADER_REOPEN_SELF_ASSESSMENT_KPI;
            $originalData->save();

            /** 
             * save history 
             * */
            $owner = auth()->user()->user;
            $newData = json_encode($originalData);
            $jobData = [
                'user_id' => $originalData->user_id,
                'department_id' => $owner->department_id,
                'owner' => auth()->user()->id,
                'position_of_owner' => $owner->position_id,
                'action_name' => HistoryAssessmentKpi::LEADER_REOPEN_SELF_ASSESSMENT_KPI,
                'original_data' => $originalDataHistory,
                'new_data' => $newData
            ];
            $job = (new StoreHistoryAssessmentKpiJob($jobData))->onQueue(QueueName::STORE_HISTORY_ASSESSMENT_KPI);
            dispatch($job);

            return response()->json(['message' => 'Xử lý thành công']);
        } catch (Exception $e) {
            DB::rollBack();
            Log::error("KpiAssessmentController leaderReopenStaffSelfAssessment: " . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function exportExcel(Request $request)
    {
        try {
            $query = KPISummary::with(['kpi', 'department', 'position', 'leader'])->whereHas('owner.user', function ($q) {
                $q->whereNull('end_date')
                    ->orWhere('end_date', '>=', date('Y-m-d'));
            });
            $department_id = $request->department_id;
            $rank_code = $request->rank_code;
            //query theo tung vi tri truyen vao
            KPISummary::getConditionList($request, $query, $department_id, $rank_code);
            $date = explode('-', $request->month);
            $year = $date[1] ?: Carbon::parse($this->toDate)->format('Y');
            $month = $date[0] ?: Carbon::parse($this->toDate)->format('m');
            $rs = $query->whereNotIn('status', [KPISummary::STAFF_REGISTER_KPI, KPISummary::LEADER_REOPEN_KPI])
                ->when($year, function ($q, $year) {
                    $q->whereYear('to_date', $year);
                })
                ->when($month, function ($q, $month) {
                    $q->whereMonth('to_date', $month);
                })
                ->orderBy('department_id');
            $kpi_array = with(clone $rs)->get();
            $count_staff_of_department =  with(clone $rs)->selectRaw('count(*) as total_staff, department_id')->groupBy('department_id')->get();
            $count_staff_of_company = with(clone $rs)->count();
            $position_ranks = Position::year(date('Y'))->select('id', 'code', 'name')->groupBy('code')->get();
            $inputFileName = 'templates/kpi.xlsx';
            $fileType = \PhpOffice\PhpSpreadsheet\IOFactory::identify($inputFileName);
            $objReader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader($fileType);
            $objPHPExcel = $objReader->load($inputFileName);
            $sheet = $objPHPExcel->setActiveSheetIndex(0);
            $row = 9;
            // return $kpi_array;
            foreach ($kpi_array as $key => $item) {
                $sheet->setCellValue("A" . $row, $key + 1);
                $sheet->setCellValue("B" . $row, $item->owner->staff_code);
                $sheet->setCellValue("C" . $row, $item->owner->name);
                $sheet->setCellValue("D" . $row, $item->position->code);
                $sheet->setCellValue("E" . $row, $item->department->code);
                $sheet->setCellValue("F" . $row, $item->leader->name);

                // lấy tiêu chí chuyên cần
                $attitude = $item->kpi->where('group_criteria', KPI::ATTITUDE_CRITERIA_GROUP)->sortBy('id')->values()->all();
                $attitudeOne = isset($attitude[0]) ? $attitude[0] : null;
                $attitudeTwo = isset($attitude[1]) ? $attitude[1] : null;
                $attitudeThree = isset($attitude[2]) ? $attitude[2] : null;
                $selfPercentOne = $attitudeOne ? $this->convertPercent($attitudeOne->proportion, $attitudeOne->self_assessment_score) : 0;
                $selfPercentTwo = $attitudeTwo ? $this->convertPercent($attitudeTwo->proportion, $attitudeTwo->self_assessment_score) : 0;
                $selfPercentThree = $attitudeThree ? $this->convertPercent($attitudeThree->proportion, $attitudeThree->self_assessment_score) : 0;
                $selfWork = $item->kpi->where('group_criteria', KPI::EXPERTISE_CRITERIA_GROUP)->values()->all();
                $selfWorkScore = 0;
                $leaderWorkScore = 0;
                foreach ($selfWork as $s) {
                    $selfWorkScore += $s->self_assessment_score * $s->proportion / 100;
                    $leaderWorkScore += $s->leader_assessment_score * $s->proportion / 100;
                }

                $sheet->setCellValue("G" . $row, $selfPercentOne . '%');
                $sheet->setCellValue("H" . $row, $selfPercentTwo . '%');
                $sheet->setCellValue("I" . $row, $selfPercentThree . '%');
                $sheet->setCellValue("J" . $row, (($selfWorkScore * 100) / 10) . '%');
                $sheet->setCellValue("K" . $row, ($item->self_assessment_score * 100 / 10) . '%');
                $sheet->setCellValue("L" . $row, $item->self_assessment_rank ? $item->self_assessment_rank : 'C');

                $leaderPercentOne = $attitudeOne ? $this->convertPercent($attitudeOne->proportion, $attitudeOne->leader_assessment_score) : 0;
                $leaderPercentTwo = $attitudeTwo ? $this->convertPercent($attitudeTwo->proportion, $attitudeTwo->leader_assessment_score) : 0;
                $leaderPercentThree = $attitudeThree ? $this->convertPercent($attitudeThree->proportion, $attitudeThree->leader_assessment_score) : 0;

                $sheet->setCellValue("M" . $row, $leaderPercentOne . '%');
                $sheet->setCellValue("N" . $row, $leaderPercentTwo . '%');
                $sheet->setCellValue("O" . $row, $leaderPercentThree . '%');
                $sheet->setCellValue("P" . $row, (($leaderWorkScore * 100) / 10) . '%');
                $sheet->setCellValue("Q" . $row, ($item->leader_assessment_score * 100 / 10) . '%');
                $sheet->setCellValue("R" . $row, $item->leader_assessment_rank ? $item->leader_assessment_rank : 'C');
                $sheet->setCellValue("S" . $row, $item->result_assessment_rank ? $item->result_assessment_rank : 'C');
                $sheet->setCellValue("T" . $row, $item->note);
                $row++;
            }
            $sheet->getStyle('A9:T' . ($row - 1))->getBorders()->getAllBorders()->setBorderStyle(Style\Border::BORDER_THIN);

            //tổng hợp theo phòng
            $sheetDepartment = $objPHPExcel->setActiveSheetIndex(1);
            $row = 5;
            $rank_A = 0;
            $rank_B = 0;
            $rank_C = 0;
            $rank_D = 0;
            $rank_NULL = 0;
            foreach ($count_staff_of_department as $key => $item) {
                $assessment_rank_A = [];
                $assessment_rank_B = [];
                $assessment_rank_C = [];
                $assessment_rank_D = [];
                $assessment_rank_NULL = [];
                foreach ($kpi_array as $e) {
                    if ($e->department_id == $item->department_id) {
                        if ($e->leader_assessment_rank === "A") {
                            array_push($assessment_rank_A, $e);
                        }
                        if ($e->leader_assessment_rank === "B") {
                            array_push($assessment_rank_B, $e);
                        }
                        if ($e->leader_assessment_rank === "C") {
                            array_push($assessment_rank_C, $e);
                        }
                        if ($e->leader_assessment_rank === "D") {
                            array_push($assessment_rank_D, $e);
                        }
                        if ($e->leader_assessment_rank === "") {
                            array_push($assessment_rank_NULL, $e);
                        }
                    }
                }
                $item->assessment_rank_A = $assessment_rank_A;
                $item->assessment_rank_B = $assessment_rank_B;
                $item->assessment_rank_C = $assessment_rank_C;
                $item->assessment_rank_D = $assessment_rank_D;
                $item->assessment_rank_NULL = $assessment_rank_NULL;

                $rank_A += count($item->assessment_rank_A);
                $rank_B += count($item->assessment_rank_B);
                $rank_C += count($item->assessment_rank_C);
                $rank_D += count($item->assessment_rank_D);
                $rank_NULL += count($item->assessment_rank_NULL);

                $sheetDepartment->setCellValue("A" . $row, $key + 1);
                $sheetDepartment->setCellValue("B" . $row, $item->department->code);
                $sheetDepartment->setCellValue("C" . $row, count($item->assessment_rank_A) / $count_staff_of_company);
                $sheetDepartment->setCellValue("D" . $row, count($item->assessment_rank_A));
                $sheetDepartment->setCellValue("E" . $row, count($item->assessment_rank_B) / $count_staff_of_company);
                $sheetDepartment->setCellValue("F" . $row, count($item->assessment_rank_B));
                $sheetDepartment->setCellValue("G" . $row, count($item->assessment_rank_C) + count($item->assessment_rank_NULL) / $count_staff_of_company);
                $sheetDepartment->setCellValue("H" . $row, count($item->assessment_rank_C) + count($item->assessment_rank_NULL));
                $sheetDepartment->setCellValue("I" . $row, count($item->assessment_rank_D) / $count_staff_of_company);
                $sheetDepartment->setCellValue("J" . $row, count($item->assessment_rank_D));
                $sheetDepartment->setCellValue("K" . $row, $item->total_staff / $count_staff_of_company);
                $sheetDepartment->setCellValue("L" . $row, $item->total_staff);

                $sheetDepartment->setCellValue("B" . 5 + $key + 1, "Total");
                $sheetDepartment->setCellValue("C" . 5 + $key + 1, $rank_A / $count_staff_of_company);
                $sheetDepartment->setCellValue("D" . 5 + $key + 1, $rank_A);
                $sheetDepartment->setCellValue("E" . 5 + $key + 1, $rank_B / $count_staff_of_company);
                $sheetDepartment->setCellValue("F" . 5 + $key + 1, $rank_B);
                $sheetDepartment->setCellValue("G" . 5 + $key + 1, $rank_C + $rank_NULL / $count_staff_of_company);
                $sheetDepartment->setCellValue("H" . 5 + $key + 1, $rank_C + $rank_NULL);
                $sheetDepartment->setCellValue("I" . 5 + $key + 1, $rank_D / $count_staff_of_company);
                $sheetDepartment->setCellValue("J" . 5 + $key + 1, $rank_D);
                $sheetDepartment->setCellValue("K" . 5 + $key + 1, 1);
                $sheetDepartment->setCellValue("L" . 5 + $key + 1, $count_staff_of_company);
                $row++;
            }
            $sheetDepartment->getStyle('A5:L' . ($row))->getBorders()->getAllBorders()->setBorderStyle(Style\Border::BORDER_THIN);
            //tổng hợp theo cấp bậc
            $sheetLevel = $objPHPExcel->setActiveSheetIndex(2);
            $row = 5;
            $position_rank_A = 0;
            $position_rank_B = 0;
            $position_rank_C = 0;
            $position_rank_D = 0;
            $position_rank_NULL = 0;
            foreach ($position_ranks as $key => $item) {
                $assessment_rank_A = [];
                $assessment_rank_B = [];
                $assessment_rank_C = [];
                $assessment_rank_D = [];
                $assessment_rank_NULL = [];
                foreach ($kpi_array as $e) {
                    if ($e->position->code == $item->code) {
                        if ($e->leader_assessment_rank === "A") {
                            array_push($assessment_rank_A, $e);
                        }
                        if ($e->leader_assessment_rank === "B") {
                            array_push($assessment_rank_B, $e);
                        }
                        if ($e->leader_assessment_rank === "C") {
                            array_push($assessment_rank_C, $e);
                        }
                        if ($e->leader_assessment_rank === "D") {
                            array_push($assessment_rank_D, $e);
                        }
                        if ($e->leader_assessment_rank === "") {
                            array_push($assessment_rank_NULL, $e);
                        }
                    }
                }
                $item->assessment_rank_A = $assessment_rank_A;
                $item->assessment_rank_B = $assessment_rank_B;
                $item->assessment_rank_C = $assessment_rank_C;
                $item->assessment_rank_D = $assessment_rank_D;
                $item->assessment_rank_NULL = $assessment_rank_NULL;

                $position_rank_A += count($item->assessment_rank_A);
                $position_rank_B += count($item->assessment_rank_B);
                $position_rank_C += count($item->assessment_rank_C);
                $position_rank_D += count($item->assessment_rank_D);
                $position_rank_NULL += count($item->assessment_rank_NULL);

                $sheetLevel->setCellValue("A" . $row, $key + 1);
                $sheetLevel->setCellValue("B" . $row, $item->code);
                $sheetLevel->setCellValue("C" . $row, count($item->assessment_rank_A) / $count_staff_of_company);
                $sheetLevel->setCellValue("D" . $row, count($item->assessment_rank_A));
                $sheetLevel->setCellValue("E" . $row, count($item->assessment_rank_B) / $count_staff_of_company);
                $sheetLevel->setCellValue("F" . $row, count($item->assessment_rank_B));
                $sheetLevel->setCellValue("G" . $row, count($item->assessment_rank_C) + count($item->assessment_rank_NULL) / $count_staff_of_company);
                $sheetLevel->setCellValue("H" . $row, count($item->assessment_rank_C) + count($item->assessment_rank_NULL));
                $sheetLevel->setCellValue("I" . $row, count($item->assessment_rank_D) / $count_staff_of_company);
                $sheetLevel->setCellValue("J" . $row, count($item->assessment_rank_D));
                $sheetLevel->setCellValue("K" . $row, (count($item->assessment_rank_A) + count($item->assessment_rank_B) + count($item->assessment_rank_C) + count($item->assessment_rank_D) + count($item->assessment_rank_NULL)) / $count_staff_of_company);
                $sheetLevel->setCellValue("L" . $row, count($item->assessment_rank_A) + count($item->assessment_rank_B) + count($item->assessment_rank_C) + count($item->assessment_rank_D) + count($item->assessment_rank_NULL));

                $sheetLevel->setCellValue("B" . 5 + $key + 1, "Total");
                $sheetLevel->setCellValue("C" . 5 + $key + 1, $position_rank_A / $count_staff_of_company);
                $sheetLevel->setCellValue("D" . 5 + $key + 1, $position_rank_A);
                $sheetLevel->setCellValue("E" . 5 + $key + 1, $position_rank_B / $count_staff_of_company);
                $sheetLevel->setCellValue("F" . 5 + $key + 1, $position_rank_B);
                $sheetLevel->setCellValue("G" . 5 + $key + 1, $position_rank_C + $position_rank_NULL / $count_staff_of_company);
                $sheetLevel->setCellValue("H" . 5 + $key + 1, $position_rank_C + $position_rank_NULL);
                $sheetLevel->setCellValue("I" . 5 + $key + 1, $position_rank_D / $count_staff_of_company);
                $sheetLevel->setCellValue("J" . 5 + $key + 1, $position_rank_D);
                $sheetLevel->setCellValue("K" . 5 + $key + 1, 1);
                $sheetLevel->setCellValue("L" . 5 + $key + 1, $count_staff_of_company);
                $row++;
            }

            $sheetLevel->getStyle('A5:L' . ($row))->getBorders()->getAllBorders()->setBorderStyle(Style\Border::BORDER_THIN);

            //điểm chuyên cần theo phòng ban
            $sheetDiligence = $objPHPExcel->setActiveSheetIndex(3);
            $row = 3;
            foreach ($kpi_array as $key => $item) {
                $sheetDiligence->setCellValue("A" . $row, $key + 1);
                $sheetDiligence->setCellValue("B" . $row, $item->owner->staff_code);
                $sheetDiligence->setCellValue("C" . $row, $item->owner->name);
                $sheetDiligence->setCellValue("D" . $row, $item->position->code);
                $sheetDiligence->setCellValue("E" . $row, $item->department->code);
                $sheetDiligence->setCellValue("F" . $row, $item->leader->name);
                $scoreDiligent = (object) $this->calcDiligent($item->user_id, $item->from_date, $item->to_date);
                $sheetDiligence->setCellValue("G" . $row, $scoreDiligent->score);
                $sheetDiligence->setCellValue("H" . $row, $item->kpi[0]->self_assessment_score);
                $sheetDiligence->setCellValue("I" . $row, $item->kpi[0]->leader_assessment_score);
                $row++;
            }
            $sheetDiligence->getStyle('A3:I' . ($row - 1))->getBorders()->getAllBorders()->setBorderStyle(Style\Border::BORDER_THIN);
            $objWriter = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($objPHPExcel, $fileType);
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename="KPI_' .  $request->month . '.xlsx"');
            header('Cache-Control: max-age=0');
            $objWriter->save("php://output");
        } catch (Exception $e) {
            Log::error("KpiAssessmentController exportExcel: " . $row . $e->getMessage());
            return response()->json([], 500);
        }
    }

    private function isAllowStaffSelfAssessment($data, $config = null)
    {
        if (!$data) {
            return (object) [
                'status' => false,
                'code' => 404,
                'msg' => 'Không tìm thấy dữ liệu',
            ];
        }

        $config = $config ?: SystemConfig::getKpiAssessmentTime();

        if (
            !$config
            || $data->user_id != auth()->user()->id
            || !in_array($data->status, [KPISummary::LEADER_CONFIRM_KPI, KPISummary::LEADER_REOPEN_SELF_ASSESSMENT_KPI])
        ) {
            return (object) [
                'status' => false,
                'code' =>  403,
                'msg' => 'Bạn không có quyền',
            ];
        }


        return (object) [
            'status' => true,
            'code' => 200,
            'msg' => 'Allowed'
        ];
    }

    private function isAllowAssessmentLeader($data, $config = null)
    {
        if (!$data) {
            return (object) [
                'status' => false,
                'code' => 404,
                'msg' => 'Không tìm thấy dữ liệu',
            ];
        }

        $config = $config ?: SystemConfig::getKpiAssessmentTime();

        //lấy leader của nhân viên được CBLĐ đánh giá KPI
        $leaderPositions = UserWorking::allActive()
            ->where('user_id', $data->user_id)
            ->pluck('dependent_position_id')
            ->toArray();
        //lấy ra vị trí chung
        $count = array_intersect($leaderPositions, KPISummary::getAllPosition());

        $status = [
            KPISummary::STAFF_SELF_ASSESSMENT_KPI,
            KPISummary::LEADER_REOPEN_SELF_ASSESSMENT_KPI,
        ];

        if (
            !$config
            || count($count) == 0 || $data->user_id == auth()->user()->id
            || !in_array($data->status, $status)
            || ($data->status == KPISummary::STAFF_SELF_ASSESSMENT_KPI && Carbon::now()->gt(Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-') . $config[SystemConfig::LEADER_ASSESSMENT_KPI] . ' 23:59:59')))
        ) {
            return (object) [
                'status' => false,
                'code' => count($count) == 0 ? 403 : 404,
                'msg' => count($count) == 0 ? 'Bạn không có quyền' : 'Đã quá thời gian cho phép',
            ];
        }

        return (object) [
            'status' => true,
            'code' => 200,
            'msg' => 'Allowed'
        ];
    }

    private function isAllowReopenAssessmentLeader($data, $config = null)
    {
        if (!$data) {
            return (object) [
                'status' => false,
                'code' => 404,
                'msg' => 'Không tìm thấy dữ liệu',
            ];
        }

        $config = $config ?: SystemConfig::getKpiAssessmentTime();
        $leaderPositions = UserWorking::allActive()
            ->where('user_id', $data->user_id)
            ->pluck('dependent_position_id')
            ->toArray();

        //lấy ra vị trí chung
        $count = array_intersect($leaderPositions, KPISummary::getAllPosition());

        $status = [
            KPISummary::STAFF_SELF_ASSESSMENT_KPI,
            KPISummary::LEADER_ASSESSMENT_KPI
        ];

        if (
            !$config
            || count($count) == 0 || $data->user_id == auth()->user()->id
            || !in_array($data->status, $status)

        ) {
            return (object) [
                'status' => false,
                'code' => 403,
                'msg' => 'Bạn không có quyền',
            ];
        }

        return (object) [
            'status' => true,
            'code' => 200,
            'msg' => 'Allowed'
        ];
    }


    private function convertPercent($proportion, $score)
    {
        return ($score * $proportion / 5);
    }

    private function calcDiligent($userId, $fromDate, $toDate)
    {
        $diligent = (new UserService())->calculateDiligence($userId, $fromDate, $toDate);
        // số lần đi sớm về muộn
        $wrongTimeKeeping = $diligent->wrongTimeKeeping;
        // số lần vi phạm xin nghỉ phép
        $wrongAbsenceLetter = $diligent->wrongAbsenceLetter;
        // số ngày xin nghỉ trong khoảng thời gian
        $totalDayAbsenceLetter = $diligent->totalDayAbsenceLetter;
        $msg = [];
        if (($totalDayAbsenceLetter > 5 || $wrongTimeKeeping >= 4) && $wrongAbsenceLetter >= 3) {
            if ($totalDayAbsenceLetter > 0) $msg[] = 'Số ngày xin nghỉ: ' . $totalDayAbsenceLetter;
            if ($wrongTimeKeeping > 0) $msg[] = 'Số lần đi muộn/về sớm: ' . $wrongTimeKeeping;
            if ($wrongAbsenceLetter > 0) $msg[] = 'Xin nghỉ không đúng quy định: ' . $wrongAbsenceLetter;
            return [
                'score' => 2,
                'msg' => $msg
            ];
        }

        if (($totalDayAbsenceLetter > 4 || $wrongTimeKeeping >= 3) && $wrongAbsenceLetter >= 2) {
            if ($totalDayAbsenceLetter > 0) $msg[] = 'Số ngày xin nghỉ: ' . $totalDayAbsenceLetter;
            if ($wrongTimeKeeping > 0) $msg[] = 'Số lần đi muộn/về sớm: ' . $wrongTimeKeeping;
            if ($wrongAbsenceLetter > 0) $msg[] = 'Xin nghỉ không đúng quy định: ' . $wrongAbsenceLetter;
            return [
                'score' => 4,
                'msg' => $msg
            ];
        }

        if (($totalDayAbsenceLetter > 3 || $wrongTimeKeeping >= 2) && $wrongAbsenceLetter >= 1) {
            if ($totalDayAbsenceLetter > 0) $msg[] = 'Số ngày xin nghỉ: ' . $totalDayAbsenceLetter;
            if ($wrongTimeKeeping > 0) $msg[] = 'Số lần đi muộn/về sớm: ' . $wrongTimeKeeping;
            if ($wrongAbsenceLetter > 0) $msg[] = 'Xin nghỉ không đúng quy định: ' . $wrongAbsenceLetter;
            return [
                'score' => 6,
                'msg' => $msg
            ];
        }

        if (($totalDayAbsenceLetter >= 2 || $wrongTimeKeeping >= 1) && $wrongAbsenceLetter == 0) {
            if ($totalDayAbsenceLetter > 0) $msg[] = 'Số ngày xin nghỉ: ' . $totalDayAbsenceLetter;
            if ($wrongTimeKeeping > 0) $msg[] = 'Số lần đi muộn/về sớm: ' . $wrongTimeKeeping;
            if (count($msg) > 0) $msg[] = 'Xin nghỉ không đúng quy định: ' . $wrongAbsenceLetter;
            return [
                'score' => 8,
                'msg' => $msg
            ];
        }

        if ($totalDayAbsenceLetter > 0) $msg[] = 'Số ngày xin nghỉ: ' . $totalDayAbsenceLetter;
        if ($wrongTimeKeeping > 0) $msg[] = 'Số lần đi muộn/về sớm: ' . $wrongTimeKeeping;
        if (count($msg) > 0) $msg[] = 'Xin nghỉ không đúng quy định: ' . $wrongAbsenceLetter;
        return [
            'score' => 10,
            'msg' => $msg
        ];
    }
}
