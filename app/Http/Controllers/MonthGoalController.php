<?php

namespace App\Http\Controllers;

// use App\Http\Requests\MonthGoalStoreRequest;
use App\Models\Position;
use App\Models\MonthGoal;
use Illuminate\Http\Request;
use App\Models\Response;
use App\Models\SubMonthGoal;
use Illuminate\Support\Facades\DB;

class MonthGoalController extends Controller
{
    public function getLeaderTeamSales()
    {
        $leader_team_sales = Position::year(date('Y'))
            ->sale()
            ->where('is_leader', true)
            ->whereNotNull('representatives')
            ->get();
        return $leader_team_sales;
    }

    public function index()
    {
        $data = MonthGoal::with('leader')
            ->paginate(20);
        return Response::formatResponse(config('apicode.SUCCESS'), $data);
    }

    public function store(Request $request)
    {
        try {
            DB::beginTransaction();
            $mont_goal = MonthGoal::create([
                'team_sale_id' => $request->team_sale_id,
                'year' => $request->year
            ]);
            $data_array = [];
            foreach ($request->goals as $e) {
                $data = [
                    'month_goal_id' => $mont_goal->id,
                    'month' => $e['month'],
                    'new' => $e['new'],
                    'renew' => $e['renew'],
                    'goal' => $e['goal']
                ];
                array_push($data_array, $data);
            }
            SubMonthGoal::insert($data_array);
            DB::commit();
            return Response::formatResponse(config('apicode.SUCCESS'), $data_array);
        } catch (\Exception $e) {
            DB::rollBack();
            return Response::formatResponse(config('apicode.PROCESS_SERVER_ERROR'), $e->getMessage());
        }
    }

    public function show($id)
    {
        $data = MonthGoal::where('id', $id)->with('goals')->first();
        return Response::formatResponse(config('apicode.SUCCESS'), $data);
    }

    public function update(Request $request, $id)
    {
        try {
            MonthGoal::where('id', $id)->update(['team_sale_id' => $request->team_sale_id, 'year' => $request->year]);
            foreach ($request->goals as $e) {
                $data = [
                    'month' => $e['month'],
                    'new' => $e['new'],
                    'renew' => $e['renew'],
                    'goal' => $e['goal']
                ];
                if (isset($e['id'])) {
                    SubMonthGoal::where('id', $e['id'])->update($data);
                } else {
                    $data['month_goal_id'] = $request->id;
                    SubMonthGoal::create($data);
                }
            }
            DB::commit();
            return Response::formatResponse(config('apicode.SUCCESS'), $data);
        } catch (\Exception $e) {
            DB::rollBack();
            return Response::formatResponse(config('apicode.PROCESS_SERVER_ERROR'), $e->getMessage());
        }
    }

    public function destroy($id)
    {
        $data = MonthGoal::where('id', $id)->delete();
        return Response::formatResponse(config('apicode.SUCCESS'), $data);
    }
}
