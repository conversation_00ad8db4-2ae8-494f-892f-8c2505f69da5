<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\HanetCamUser;
use App\Models\Response;
use App\Services\HanetApiService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class HanetCamUserController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $data = HanetCamUser::searchText($request->keyword)->paginate(20);
        return Response::formatResponse(config('apicode.SUCCESS'), $data);
    }

    public function syncUsers(Request $request) 
    {
        DB::beginTransaction();

        try {
            $option = $request->option ?? 1;
            $placeId = $option == 1 ? getenv('HANET_API_PLACEID') : getenv('HANET_API_PLACEID_HCM');
            $user = auth()->user();
            $hanetApiService = new HanetApiService();
            # List person by place sẽ trả tối đa là 50 item. <PERSON>ếu bạn muốn lấy nhiều dữ liệu hơn nữa, vui lòng nhập thêm page và size
            # vì bên hanet đang giới hạn nên mới phải call api như thế này

            # xoá đi các face hiện tại để đồng bộ lại
            HanetCamUser::whereNotNull('id')->where('placeID', $placeId)->delete();

            $page = 1;
            $option = $request->option;
            while (true) {
                $userCamAI = $hanetApiService->getPersonLisstByPlace(0, $user->id, $page, 50, $placeId);
                if (!empty($userCamAI) && isset($userCamAI->data) && is_array($userCamAI->data) && count($userCamAI->data) > 0) {
                    foreach($userCamAI->data as $item) {
                        HanetCamUser::create([
                            'personID' => $item->personID,
                            'placeID' => $placeId,
                            'aliasID' => $item->aliasID,
                            'name' => $item->name,
                            'avatar' => $item->avatar,
                            'title' => $item->title,
                            'type' => 0,
                        ]);
                    }
                } else {
                    break;
                }
                $page++;
            }

            DB::commit();
            return Response::formatResponse(config('apicode.SUCCESS'), true);
        } catch (\Exception $e) {
            DB::rollback();
            \Log::error($e);
            return Response::formatResponse(config('apicode.INTERNAL_SERVER_ERROR'), null, $e->getMessage());
        }
    }

    public function update(Request $request) 
    {
        $user = auth()->user();
        $id = $request->id;
        $camUser = HanetCamUser::find($id);
        if (empty($camUser)) {
            return Response::formatResponse(config('apicode.INTERNAL_SERVER_ERROR'), 'Lỗi trong quá trình xử lý!');
        } else {
            $hanetApiService = new HanetApiService();
            $oldAliasID = $camUser->aliasID;
            if (empty($oldAliasID) || $oldAliasID != $request->aliasID) {
                // call API updateAliasID
                $params = [
                    'personID' => $camUser->personID,
                    'aliasID' => $request->aliasID,
                ];
                $rs = $hanetApiService->updateAliasID($params, $user->id);
                if (!empty($rs) && isset($rs->returnCode) && $rs->returnCode == 1) {
                    $camUser->aliasID = $request->aliasID;
                    $camUser->save();
                }
            }

            // call API updateInfo
            $params = [
                'personID' => $camUser->personID,
                'name' => $request->name,
                'title' => $request->title,
            ];
            $rs = $hanetApiService->updateInfo($params, $user->id);
            if (!empty($rs) && isset($rs->returnCode) && $rs->returnCode == 1) {
                $camUser->name = $request->name;
                $camUser->title = $request->title;
                $camUser->save();
                return Response::formatResponse(config('apicode.SUCCESS'), true);
            } else {
                return Response::formatResponse(config('apicode.INTERNAL_SERVER_ERROR'), null);
            }
        }
    }

    public function destroy(Request $request)
    {
        $user = auth()->user();
        $id = $request->id;
        $camUser = HanetCamUser::find($id);
        if (empty($camUser)) {
            return Response::formatResponse(config('apicode.INTERNAL_SERVER_ERROR'), null);
        } else {
            $hanetApiService = new HanetApiService();
            $params = [
                'personID' => $camUser->personID,
            ];
            $rs = $hanetApiService->removeByPersionID($params, $user->id);
            if (!empty($rs) && isset($rs->returnCode) && $rs->returnCode == 1) {
                $camUser->delete();
                return Response::formatResponse(config('apicode.SUCCESS'), true);
            } else {
                return Response::formatResponse(config('apicode.INTERNAL_SERVER_ERROR'), null);
            }
        }
    }

    public function register(Request $request)
    {
        # code...
        $user = auth()->user();
        $p = (Object) $request->params;
        if (!empty($p->file) && !empty($p->fileName) && !empty($p->fileExtension)) {
            $attachedFile = $p->file;
            $explod = explode(',', $attachedFile);
            $decod = base64_decode($explod[1]); // file_contents
            $fileName = $p->fileName;
            $fileExtension = $p->fileExtension;
            $fileAttached = md5($fileName) . '_' . time() . '.' . $fileExtension;

            if (!Storage::exists("app/public/avatar")) {
                Storage::makeDirectory("app/public/avatar", 777, true);
            }
            Storage::disk('avatar')->put($fileAttached, $decod);
            $path = 'storage/avatar/' . $fileAttached;
            $path = url($path);
            // $path = 'https://dev.internal.vietec.com.vn/storage/avatar/Abl0GcBRuU.png';
            $hanetApiService = new HanetApiService();
            $params = [
                'name' => $p->name,
                'aliasID' => $p->aliasID,
                'title' => $p->title,
                'url' => $path,
            ];
            $rs = $hanetApiService->registerByUrl($params, $user->id);
            if (!empty($rs) && isset($rs->returnCode) && $rs->returnCode == 1) {
                $dataUser = $rs->data;
                HanetCamUser::create([
                    'personID' => $dataUser->personID,
                    'aliasID' => $dataUser->aliasID,
                    'name' => $dataUser->name,
                    'avatar' => $dataUser->file,
                    'title' => $dataUser->title,
                    'type' => 0,
                ]);
                return Response::formatResponse(config('apicode.SUCCESS'), true);
            } else {
                return Response::formatResponse(config('apicode.INTERNAL_SERVER_ERROR'), null, "Có lỗi trong quá trình đăng ký User với Hanet!");
            }
        }
    }
}
