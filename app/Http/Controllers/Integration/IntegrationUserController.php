<?php

namespace App\Http\Controllers\Integration;

use App\Notifications\UserNotification;
use App\Models\User;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use App\Http\Controllers\Controller;
use App\Http\Requests\Integration\UserSendNotificationRequest;

class IntegrationUserController extends Controller
{
    public function sendNotification(UserSendNotificationRequest $request)
    {
        try {
            $user = User::where('email', $request->email)->first();

            if ($user == null)
                return response()->json([
                    'result' => false,
                    'message' => 'Tài khoản ' . $request->email . ' không tồn tại'
                ], 401);

            $message = [
                'title' => $request->title,
                'body' => $request->body,
                'image' => $request->image,
                'badge' => strval(1),
                'data' => [
                    'push_type' => $request->type,
                    'badge' => strval(1),
                    'id' => '',
                ]
            ];

            $user->notify((new UserNotification($user, $message))->onQueue(env('QUEUE_NAME_PREFIX').'_user_notification'));

            return response()->json([
                'result' => true,
                'message' => 'Gửi thành công thông báo'
            ]);
        } catch (\Exception $e) {
            dd($e);
            return response()->json([
                'result' => false,
                'message' => 'System Error',
            ], 401);
        }
    }
}
