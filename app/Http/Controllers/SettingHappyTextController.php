<?php

namespace App\Http\Controllers;

use App\Models\SettingHappyText;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class SettingHappyTextController extends Controller
{
    public function index(Request $request)
    {
        try {
            $rs = SettingHappyText::where('type', '<>', SettingHappyText::SOUND_HPBD)
                ->paginate(20);
            return response()->json(['data' => $rs]);
        } catch (Exception $e) {
            Log::error('SettingHappyTextController index: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function store(Request $request)
    {
        try {
            $model = new SettingHappyText();
            $model->content = trim($request->content);
            $model->male = $request->male;
            $model->female = $request->female;
            $model->type = $request->type ?: SettingHappyText::HPBD;
            $model->save();

            return response()->json($model);
        } catch (Exception $e) {
            Log::error('SettingHappyTextController store: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function update(Request $request)
    {
        try {
            if (!$request->has('id')) {
                return response()->json(['message' => 'Dữ liệu không tồn tại'], 400);
            }

            $model = SettingHappyText::find($request->id);
            if (!$model) {
                return response()->json(['message' => 'Dữ liệu không tồn tại'], 400);
            }

            $model->content = trim($request->content);
            $model->male = $request->male;
            $model->female = $request->female;
            $model->type = $request->type;
            $model->save();

            return response()->json($model);
        } catch (Exception $e) {
            Log::error('SettingHappyTextController update: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function details(Request $request)
    {
        try {
            if (!$request->has('id')) {
                return response()->json(['message' => 'Dữ liệu không tồn tại'], 400);
            }

            $model = SettingHappyText::select('id', 'content', 'male', 'female', 'type')
                ->find($request->id);
            if (!$model) {
                return response()->json(['message' => 'Dữ liệu không tồn tại'], 400);
            }

            return response()->json($model);
        } catch (Exception $e) {
            Log::error('SettingHappyTextController update: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function sound(Request $request)
    {
        try {
            $model = SettingHappyText::select('id', 'content', 'male', 'female', 'type')
                ->where('type', SettingHappyText::SOUND_HPBD)
                ->first();
            if (!$model) {
                $model = new SettingHappyText();
                $model->id = '';
                $model->content = '';
                $model->male = true;
                $model->female = true;
                $model->type = SettingHappyText::SOUND_HPBD;
            }

            return response()->json($model);
        } catch (Exception $e) {
            Log::error('SettingHappyTextController update: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function getSound()
    {
        try {
            $model = SettingHappyText::select('id', 'content', 'male', 'female', 'type')
                ->where('type', SettingHappyText::SOUND_HPBD)
                ->first();

            return response()->json($model->content);
        } catch (Exception $e) {
            return response()->json('');
        }
    }

    public function getGreetings()
    {
        try {
            $query = SettingHappyText::where('type', SettingHappyText::HPBD);
            if (auth()->user()->gender == 1) {
                $query->where('male', true);
            } elseif (auth()->user()->gender == 2) {
                $query->where('female', true);
            }

            $rs = $query->pluck('content')->toArray();

            return response()->json($rs[array_rand($rs)]);
        } catch (Exception $e) {
            return response()->json('');
        }
    }
}
