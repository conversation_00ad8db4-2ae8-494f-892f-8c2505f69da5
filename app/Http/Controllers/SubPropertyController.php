<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Property;
use App\Models\SubPropertyHistory;
use App\Models\Response;
use App\Models\SubProperty;
use App\Models\User;
use PhpOffice\PhpSpreadsheet\Reader\Exception;
use PhpOffice\PhpSpreadsheet\IOFactory;
use Illuminate\Support\Facades\DB;

class SubPropertyController extends Controller
{
    public function index(Request $request)
    {
        $query = SubProperty::with('created_by','property_id')->orderBy('property_id');
        if ($request->property_name) {
            $query->where('name_equipment', 'like', '%' . $request->property_name . '%');
        }
        $data = $query->paginate(20);
        //dd($query->toSql(), $query->getBindings());
        return Response::formatResponse(config('apicode.SUCCESS'), $data);
    }

    public function store(Request $request)
    {
        $request->validate([
            'name_equipment' => 'required',
            'property' => 'required',
        ]);
        try {
            $data = $request->all();
            $data['property_id'] = $request['property']['id'];
            $data['created_by'] = auth()->user()->id;
            SubProperty::create($data);
            return Response::formatResponse(config('apicode.SUCCESS'), $data);
        } catch (\Exception $e) {
            return Response::formatResponse(config('apicode.INTERNAL_SERVER_ERROR'), $e->getMessage());
        }
    }

    public function update(Request $request)
    {
        $request->validate([
            'name_equipment' => 'required',
            'property' => 'required',
        ]);
     
        try {
            $data['property_id'] = $request['property']['id'];
            $data['name_equipment'] = $request['name_equipment'];
            $data['created_by'] = auth()->user()->id;
            SubProperty::where('id', $request->id)->update($data);
            return Response::formatResponse(config('apicode.SUCCESS'), $data);
        } catch (\Exception $e) {
            return Response::formatResponse(config('apicode.INTERNAL_SERVER_ERROR'), $e->getMessage());
        }
    }

    public function destroy($id)
    {
        $data = SubProperty::where('id', $id)->delete();
        return Response::formatResponse(config('apicode.SUCCESS'), $data);
    }

    public function importExcel(Request $request)
    {
        $this->validate($request, [
            'file' => 'required|file|mimes:xls,xlsx'
        ]);
        $file = $request->file('file');
        try {
            $spreadsheet = IOFactory::load($file->getRealPath());
            $sheet        = $spreadsheet->getActiveSheet();
            $row_limit    = $sheet->getHighestDataRow();
            $row_range    = range(2, $row_limit);
            DB::beginTransaction();
            $errors = [];
            foreach ($row_range as $row) {
                //check require Mã nhân viên, tên loại tài sản, mã tài sản
                $staff_code = $sheet->getCell('A' . $row)->getValue();
                $user_use = User::where('staff_code', $staff_code)->first();
                $pro_name = $sheet->getCell('C' . $row)->getValue();
                $code = $sheet->getCell('D' . $row)->getValue();
                if (!$staff_code) {
                    $errors[] = "Mã nhân viên ở dòng " . 'A' . $row . " không được bỏ trống";
                }
                if ($staff_code && !$user_use) {
                    $errors[] = "Mã nhân viên ở dòng " . 'A' . $row . " không tồn tại";
                }
                if (!$pro_name) {
                    $errors[] = "Tên loại tài sản ở dòng " . 'C' . $row . " không được bỏ trống";
                }
                if (!$code) {
                    $errors[] = "Mã tài sản ở dòng " . 'D' . $row . " không được bỏ trống";
                }
                //check xem loại tài sản đã tồn tại chưa
                $property_db = Property::where('name', 'LIKE', '%' . $pro_name . '%')->first();
                if (isset($property_db)) {
                    $amount = $property_db->amount + 1;
                    $property_db->update(['tatol' => $amount]);
                    $property_id = $property_db->id;
                } else {
                    $property = Property::create([
                        'name' => $pro_name,
                        'tatol' => 1,
                        'created_by' => auth()->user()->id
                    ]);
                    $property_id = $property->id;
                }
                //create tài sản
                $sub_property = SubProperty::create([
                    'property_id' => $property_id,
                    'code' => $code ? $code : '',
                    'description' => $sheet->getCell('E' . $row)->getValue(),
                    'purchase_date' => $sheet->getCell('G' . $row)->getValue() ? date_format(date_create_from_format("d/m/Y", $sheet->getCell('G' . $row)->getValue()), "Y-m-d") : null,
                    'price' => $sheet->getCell('H' . $row)->getValue(),
                    'supplier' => $sheet->getCell('K' . $row)->getValue(),
                    'phone_number' => $sheet->getCell('L' . $row)->getValue(),
                    'created_by' => auth()->user()->id,
                    'note' => $sheet->getCell('M' . $row)->getValue()
                ]);
                $sub_property_id = $sub_property->id;
                //lưu lịch sử tài sản
                SubPropertyHistory::create([
                    'property_id' => $property_id,
                    'sub_property_id' => $sub_property_id,
                    'user_use_id' => $user_use ? $user_use->id : null,
                    'trade_date' => $sheet->getCell('I' . $row)->getValue() ?  date_format(date_create_from_format("d/m/Y", $sheet->getCell('I' . $row)->getValue()), "Y-m-d") : null,
                    'recover_date' => $sheet->getCell('J' . $row)->getValue() ? date_format(date_create_from_format("d/m/Y", $sheet->getCell('J' . $row)->getValue()), "Y-m-d") : null,
                    'status' => SubPropertyHistory::ACTIVE_STATUS,
                    'created_by' => auth()->user()->id
                ]);
            }
            if (count($errors) > 0) {
                return response()->json($errors, 422);
            }
            DB::commit();
            return Response::formatResponse(config('apicode.SUCCESS'), 1);
        } catch (Exception $e) {
            DB::rollBack();
            return Response::formatResponse(config('apicode.INTERNAL_SERVER_ERROR'), $e->getMessage());
        }
    }

    public function getAllProperties()
    {
        $data = SubProperty::all();
        return $data;
    }

    public function detail($id)
    {
        $data = SubProperty::where('id', $id)->with('histories.user_use', 'created_by', 'property')->first();
        return $data;
    }
}
