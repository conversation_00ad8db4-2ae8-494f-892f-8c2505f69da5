<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use Illuminate\Support\Str;
use App\Models\User;
use Illuminate\Http\Request;
use App\Models\PasswordReset;
//use App\Notifications\ResetPasswordRequest;
use App\Mail\ResetPasswordEmail;
use Illuminate\Support\Facades\Mail;
use App\Jobs\SendEmailResetPassword;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class ResetPasswordController extends Controller
{
    public function sendMail(Request $request)
    {
        $user = User::where('email', $request->email)->firstOrFail();
        
        $passwordReset = PasswordReset::updateOrCreate([
            'email' => $user->email,
        ], [
            'token' => Str::random(60),
        ]);

        if ($passwordReset) {

            try {
                DB::beginTransaction();
                $user_reset_pass = User::query()->where('id', $user->id)->first();
                $pass_new = Str::random(6);
                $update = User::query()->where('id', $user_reset_pass->id)->update(['password' => Hash::make($pass_new)]);
                DB::commit();
            } catch (\Exception $e) {
                return $e;
            }

            $content = (object) [];
            $content->receiver = $user->email;
            $content->name = $user->name;
            $content->subject = '[VIETEC-QLDN]-KHÔI PHỤC MẬT KHẨU';
            $content->message = $pass_new;

            $emailJob = (new SendEmailResetPassword($content))->onQueue('email');
            dispatch($emailJob);
        }
  
        return response()->json([
            'message' => 'Hệ thống đã gửi mật khẩu mới vào mail của bạn !'
        ]);
    }

    // public function reset(Request $request, $token)
    // {
    //     $passwordReset = PasswordReset::where('token', $token)->firstOrFail();
    //     if (Carbon::parse($passwordReset->updated_at)->addMinutes(720)->isPast()) {
    //         $passwordReset->delete();

    //         return response()->json([
    //             'message' => 'This password reset token is invalid.',
    //         ], 422);
    //     }
    //     $user = User::where('email', $passwordReset->email)->firstOrFail();
    //     $updatePasswordUser = $user->update($request->only('password'));
    //     $passwordReset->delete();

    //     return response()->json([
    //         'success' => $updatePasswordUser,
    //     ]);
    // }
}
