<?php

namespace App\Http\Controllers;

use App\Models\SystemConfig;
use App\Models\UserWorking;
use Illuminate\Http\Request;
use Exception;
use Illuminate\Support\Facades\Log;


class SystemConfigController extends Controller
{
    public function getEmailConfig(Request $request)
    {
        try {
            $rs = SystemConfig::whereIn('type', array_keys(SystemConfig::EMAIL_CONFIG))
                ->orderBy('type', 'asc')
                ->get();
            return response()->json(['config' => $rs, 'labels' => SystemConfig::EMAIL_CONFIG]);
        } catch (Exception $e) {
            Log::error('SystemConfigController getEmailConfig: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function getEmployees(Request $request)
    {
        try {
            $data = UserWorking::with('user')
                ->active()
                ->orderBy('department_id', 'asc')
                ->get();

            return response()->json($data);
        } catch (Exception $e) {
            Log::error('SystemConfigController getEmployees: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function saveEmailConfig(Request $request)
    {
        $request->validate([
            'type' => 'required',
            'content' => ['nullable', 'array'],
            'content.*' => 'email'
        ], [], [
            'type' => 'Loại Email',
            'content' => 'Người nhận'
        ]);

        try {
            $model = SystemConfig::where('type', $request->type)->first();
            if (!$model) {
                $model = new SystemConfig();
            }
            $model->type = $request->type;
            $model->content = implode(',', $request->content);
            $model->save();

            return response()->json(true);
        } catch (Exception $e) {
            Log::error('TicketController saveEmailConfig: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function getKpiAssessmentTimeConfig(Request $request)
    {
        $rs = SystemConfig::getKpiAssessmentTime();
        return response()->json($rs);
    }

    public function saveKpiAssessmentTimeConfig(Request $request)
    {
        $request->validate([
            'content' => ['required', 'array'],
            'content.EMPLOYEE_REGISTER_KPI' => 'required|max:2',
            'content.EMPLOYEE_SELF_ASSESSMENT_KPI' => 'required|max:2',
            'content.LEADER_ASSESSMENT_KPI' => 'required|max:2',
            'content.SEND_RESULT_ASSESSMENT_KPI_TO_HR' => 'required|max:2',
            'content.CONFIRM_RESULT_ASSESSMENT_KPI' => 'required|max:2',
        ], [], [
            'content' => 'Thời gian'
        ]);

        try {
            $model = SystemConfig::kpiAssessmentTime()->first();
            if (!$model) {
                $model = new SystemConfig();
            }
            $model->type = SystemConfig::KPI_ASSESSMENT_TIME;
            $model->content = json_encode($request->content);
            $model->save();

            return response()->json(true);
        } catch (Exception $e) {
            Log::error('TicketController saveKpiAssessmentTimeConfig: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function getExplainConfig(Request $request){
        $rs = SystemConfig::getExplain();
        return response()->json($rs);
    }

    public function saveExplainConfig(Request $request){
        $request->validate([
            'content' => ['required', 'array'],
            'content.EXPLAIN_ALLOW' => 'required|max:2',
            'content.EXPLAIN_ALLOW_MISS' => 'required|max:2',
            'content.EXPLAIN_ONLINE_ALLOW' => 'required|max:2'
        ], [], [
            'content' => 'Số lần'
        ]);

        try {
            $model = SystemConfig::explain()->first();
            if (!$model) {
                $model = new SystemConfig();
            }
            $model->type = SystemConfig::EXPLAIN;
            $model->content = json_encode($request->content);
            $model->save();

            return response()->json(true);
        } catch (Exception $e) {
            Log::error('TicketController saveExplainConfig: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }
}
