<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Supplier;
use App\Models\Response;

class SupplierController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $data = Supplier::paginate(20);
        return Response::formatResponse(config('apicode.SUCCESS'), $data);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|max:255',
            'code' => 'required|max:255',
            'contact' => 'required|max:255',
            'address' => 'required|max:255',
            'phone' => 'nullable|regex:/(0)[0-9]/|not_regex:/[a-z]/|min:10|max:13|unique:suppliers'
        ], [], [
            'name' => 'Tên kho',
            'code' => 'Mã Kho',
            'contact' => 'Người liên hệ',
            'address' => 'Địa chỉ',
            'phone' => 'Số điện thoại'
        ]);
        try {
            $params = $request->all();
            Supplier::create($params);
            return Response::formatResponse(config('apicode.SUCCESS'), $params);
        } catch (\Exception $e) {
            return Response::formatResponse(config('apicode.INTERNAL_SERVER_ERROR'), $e->getMessage());
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Supplier $Warehouse
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request)
    {
        $request->validate([
            'name' => 'required|max:255',
            'code' => 'required|max:255',
            'contact' => 'required|max:255',
            'address' => 'required|max:255',
            'phone' => 'nullable|regex:/(0)[0-9]/|not_regex:/[a-z]/|min:10|max:13|unique:suppliers,phone,' . $request->id
        ], [], [
            'name' => 'Tên kho',
            'code' => 'Mã Kho',
            'contact' => 'Người liên hệ',
            'address' => 'Địa chỉ',
            'phone' => 'Số điện thoại'
        ]);
        try {
            $params = $request->all();
            Supplier::where('id', $request->id)->update($params);
            return Response::formatResponse(config('apicode.SUCCESS'), $params);
        } catch (\Exception $e) {
            return Response::formatResponse(config('apicode.INTERNAL_SERVER_ERROR'), $e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Supplier $Warehouse
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $data = Supplier::where('id', $id)->delete();
        return Response::formatResponse(config('apicode.SUCCESS'), $data);
    }

    public function getAll()
    {
        $datas = Supplier::all();
        return Response::formatResponse(config('apicode.SUCCESS'), $datas);
    }
}
