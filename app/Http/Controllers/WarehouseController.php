<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Warehouse;
use App\Models\Response;

class WarehouseController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $data = Warehouse::paginate(30);
        return Response::formatResponse(config('apicode.SUCCESS'), $data);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|max:255',
            'code' => 'required|max:255',
            'address' => 'required|max:255',
            'phone_number' => 'regex:/(0)[0-9]/|not_regex:/[a-z]/|min:10|max:13|unique:warehouses'
        ], [], [
            'name' => 'Tên kho',
            'code' => 'Mã Kho',
            'address' => 'Địa chỉ',
            'phone_number' => 'Số điện thoại'
        ]);
        try {
            $params = $request->all();
            Warehouse::create($params);
            return Response::formatResponse(config('apicode.SUCCESS'), $params);
        } catch (\Exception $e) {
            return Response::formatResponse(config('apicode.INTERNAL_SERVER_ERROR'), $e->getMessage());
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Warehouse $Warehouse
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request)
    {
        $request->validate([
            'name' => 'required|max:255',
            'code' => 'required|max:255',
            'address' => 'required|max:255',
            'phone_number' => 'regex:/(0)[0-9]/|not_regex:/[a-z]/|min:10|max:13|unique:warehouses,phone_number,' . $request->id
        ], [], [
            'name' => 'Tên kho',
            'code' => 'Mã Kho',
            'address' => 'Địa chỉ',
            'phone_number' => 'Số điện thoại'
        ]);
        try {
            $params = $request->all();
            Warehouse::where('id', $request->id)->update($params);
            return Response::formatResponse(config('apicode.SUCCESS'), $params);
        } catch (\Exception $e) {
            return Response::formatResponse(config('apicode.INTERNAL_SERVER_ERROR'), $e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Warehouse $Warehouse
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $data = Warehouse::where('id', $id)->delete();
        return Response::formatResponse(config('apicode.SUCCESS'), $data);
    }

    public function getAll()
    {
        $data = Warehouse::all();
        return Response::formatResponse(config('apicode.SUCCESS'), $data);
    }
}
