<?php

namespace App\Http\Controllers\Tasks;

use Illuminate\Http\Request;
use App\Models\Tasks\Project;
use App\Models\User;
use App\Models\Tasks\ProjectResource;
use Exception;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use App\Models\Tasks\ProjectState;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class ProjectController extends Controller
{
    public function index(Request $request)
    {
        try {
            $rs = Project::with(['leader', 'createdBy'])
                ->when(!auth()->user()->hasRole('admin'), function ($q, $role) {
                    $q->whereHas('projectResources', function ($s) {
                        $s->where('member_id', auth()->user()->id);
                    });
                })
                ->get();

            return response()->json($rs);
        } catch (Exception $e) {
            Log::error("ProjectController index: " . $e->getMessage());
            return response()->json([], 500);
        }
    }

    /* Thêm dự án mới */
    public function store(Request $request)
    {
        $rules = $request->id
            ? [
                'name' => 'required|max:255',
                'code' => 'required|max:255|unique:task_projects,code,' . $request->id,
                'description' => 'nullable|max:500',
                'status' => 'required|boolean'
            ]
            : [
                'name' => 'required|max:255',
                'code' => 'required|max:255|unique:task_projects',
                'description' => 'nullable|max:500'
            ];
        $request->validate($rules, [], [
            'name' => 'Tên dự án',
            'code' => 'Mã dự án',
            'description' => 'Mô tả dự án'
        ]);
        try {
            DB::beginTransaction();
            $model = new Project();
            $msg = 'Thêm dự án thành công!';
            if ($request->id) {
                $model = Project::where('id', $request->id)
                    ->when(!auth()->user()->hasRole('admin'), function ($q, $role) {
                        $q->where('leader_id', auth()->user()->id);
                    })
                    ->first();
                if (!$model) {
                    return response()->json(['message' => 'Bạn không có quyền truy cập'], 403);
                }
                $msg = 'Cập nhật dự án thành công!';
            }

            $model->name = trim($request->name);
            $model->code = trim($request->code);
            $model->description = trim($request->description);
            $model->status = $request->id ? $request->status : true;
            $model->leader_id = $request->leader_id ?: auth()->user()->id;
            $model->created_by = auth()->user()->id;
            $model->save();

            if (!$request->id) {
                $resource = new ProjectResource();
                $resource->task_project_id = $model->id;
                $resource->member_id = $model->leader_id;
                $resource->save();
            }

            DB::commit();
            return response()->json(['message' => $msg]);
        } catch (Exception $e) {
            DB::rollback();
            Log::error("ProjectController store: " . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function details(Request $request)
    {
        try {
            $model = Project::with(['projectStates', 'projectResources.member'])->find($request->id);
            if (!$model) {
                return response()->json(["message" => 'Dữ liệu không tồn tại'], 400);
            }

            return response()->json($model);
        } catch (Exception $e) {
            Log::error("ProjectController delete: " . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function delete(Request $request)
    {
        try {
            $model = Project::where('id', $request->id)
                ->when(!auth()->user()->hasRole('admin'), function ($q, $role) {
                    $q->where('leader_id', auth()->user()->id);;
                })
                ->first();
            if (!$model) {
                return response()->json([], 403);
            }

            $model->delete();
            return response()->json(['message' => 'Xoá thành công!']);
        } catch (Exception $e) {
            Log::error("ProjectController delete: " . $e->getMessage());
            return response()->json([], 500);
        }
    }

    /** quản lý resource */
    public function getResource(Request $request)
    {
        try {
            $rs = User::whereHas('user')->orderBy('name')->get();
            return response()->json($rs);
        } catch (Exception $e) {
            Log::error("ProjectController getResource: " . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function updateResource(Request $request)
    {
        $request->validate([
            'members' => 'required|array',
        ], [], [
            'members' => 'Thành viên'
        ]);

        try {
            $model = Project::with('projectResources')
                ->where('id', $request->id)
                ->when(!auth()->user()->hasRole('admin'), function ($q, $role) {
                    $q->where('leader_id', auth()->user()->id);;
                })
                ->first();
            if (!$model) {
                return response()->json([], 403);
            }
            DB::beginTransaction();
            ProjectResource::where('task_project_id', $model->id)
                ->whereNotIn('member_id', $request->members)
                ->delete();
            $memberExist = $model->projectResources->pluck('member_id')->toArray();
            $newMembers = array_diff($request->members, $memberExist);
            $insertData = [];
            foreach ($newMembers as $mem) {
                $insertData[] = [
                    'id' => Str::uuid()->toString(),
                    'task_project_id' => $model->id,
                    'member_id' => $mem,
                    'created_at' => date("Y-m-d H:i:s"),
                    'updated_at' => date("Y-m-d H:i:s"),
                ];
            }

            DB::table('task_project_resources')->insert($insertData);
            DB::commit();
            return response()->json(['message' => 'Cập nhật thành viên thành công!']);
        } catch (Exception $e) {
            DB::rollBack();
            Log::error('ProjectController updateResource: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    /** Quản lý trạng thái */
    public function updateSortState(Request $request)
    {
        $request->validate([
            'states' => 'array',
            'states.*.id' => 'required',
            'states.*.sort' => 'required|numeric'
        ], [], [
            'states' => 'Trạng thái',
            'states.*.id' => 'Trạng thái',
            'states.*.sort' => 'Thứ tự'
        ]);

        try {
            $model = Project::where('id', $request->id)
                ->when(!auth()->user()->hasRole('admin'), function ($q, $role) {
                    $q->where('leader_id', auth()->user()->id);;
                })
                ->first();
            if (!$model) {
                return response()->json([], 403);
            }
            foreach ($request->states as $s) {
                ProjectState::where('id', $s['id'])
                    ->update([
                        'sort' => $s['sort']
                    ]);
            }

            return response()->json(['message' => 'Cập nhật trạng thái thành công!']);
        } catch (Exception $e) {
            Log::error('ProjectController updateSortState: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function updateState(Request $request)
    {
        $request->validate([
            'state_name' => 'required|max:255',
            'sort' => 'required|numeric'
        ], [], [
            'state_name' => 'Trạng thái',
            'sort' => 'Thứ tự'
        ]);

        try {
            $project = Project::with('projectStates')
                ->where('id', $request->id)
                ->when(!auth()->user()->hasRole('admin'), function ($q, $role) {
                    $q->where('leader_id', auth()->user()->id);;
                })
                ->first();
            if (!$project) {
                return response()->json([], 403);
            }

            $model = new ProjectState();
            if ($request->state_id) {
                $model = ProjectState::find($request->state_id);
                if (!$model) {
                    return response()->json([], 403);
                }
            }

            $model->task_project_id = $project->id;
            $model->state_name = trim($request->state_name);
            $model->sort = $request->sort;
            $model->save();

            return response()->json($model);
        } catch (Exception $e) {
            Log::error('ProjectController updateStates: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function deleteStates(Request $request)
    {
        $request->validate([
            'state_id' => 'required',
        ], [], [
            'state_id' => 'Trạng thái'
        ]);

        try {
            $project = Project::with('projectStates')
                ->where('id', $request->id)
                ->when(!auth()->user()->hasRole('admin'), function ($q, $role) {
                    $q->where('leader_id', auth()->user()->id);;
                })
                ->first();
            if (!$project) {
                return response()->json([], 403);
            }

            $model = ProjectState::where('task_project_id', $project->id)
                ->where('id', $request->state_id)
                ->first();
            if (!$model) {
                return response()->json(['message' => 'Dữ liệu không tồn tại!'], 400);
            }

            $model->delete();

            return response()->json(['message' => 'Xử lý thành công!']);
        } catch (Exception $e) {
            Log::error('ProjectController deleteStates: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }
}
