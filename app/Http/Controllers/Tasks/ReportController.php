<?php

namespace App\Http\Controllers\Tasks;

use Illuminate\Http\Request;
use App\Models\Tasks\Project;
use App\Models\User;
use App\Models\Tasks\ProjectResource;
use Exception;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use App\Models\Tasks\ProjectState;
use App\Models\Tasks\Ticket;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class ReportController extends Controller
{
    /** Báo cáo issue done / inprogress */
    // public function index(Request $request)
    // {
    //     try {
    //         $latestState = ProjectState::with(['tickets'])
    //             ->where('task_project_id', $request->projectId)
    //             ->latest('sort')
    //             ->first();
    //         $otherState = Ticket::with(['status'])
    //             ->where('task_project_id', $request->projectId)
    //             ->where('status', '<>', $latestState->id)
    //             ->get();


    //         return response()->json($rs);
    //     } catch (Exception $e) {
    //         Log::error("ProjectController index: " . $e->getMessage());
    //         return response()->json([], 500);
    //     }
    // }

    /** Gantt */
    public function gantt(Request $request)
    {
        try {
            $fromDate = $request->fromDate ?: Carbon::now()->startOfMonth()->format('Y-m-d');
            $toDate = $request->toDate ?: Carbon::now()->endOfMonth()->format('Y-m-d');
            $rs = Ticket::with(['assignTo.avatar', 'status'])
                ->withCount('workLogs')
                ->when($request->userIds, function ($q, $userIds) {
                    $q->whereIn('assign_to', explode(',', $userIds));
                })
                ->where('task_project_id', $request->projectId)
                ->where('start_date', '>=', $fromDate)
                ->where('end_date', '<=', $toDate)
                ->orderBy('start_date')
                ->orderBy('end_date')
                ->get();

            return response()->json($rs);
        } catch (Exception $e) {
            Log::error("ReportController gantt: " . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function delete(Request $request)
    {
        try {
            $model = Project::where('id', $request->id)
                ->when(!auth()->user()->hasRole('admin'), function ($q, $role) {
                    $q->where('leader_id', auth()->user()->id);;
                })
                ->first();
            if (!$model) {
                return response()->json([], 403);
            }

            $model->delete();
            return response()->json(['message' => 'Xoá thành công!']);
        } catch (Exception $e) {
            Log::error("ProjectController delete: " . $e->getMessage());
            return response()->json([], 500);
        }
    }

    /** quản lý resource */
    public function getResource(Request $request)
    {
        try {
            $rs = User::whereHas('user')->orderBy('name')->get();
            return response()->json($rs);
        } catch (Exception $e) {
            Log::error("ProjectController getResource: " . $e->getMessage());
            return response()->json([], 500);
        }
    }
}
