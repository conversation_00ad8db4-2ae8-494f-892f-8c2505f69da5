<?php

namespace App\Http\Controllers\Tasks;

use App\Http\Controllers\BaseController;
use App\Models\Client;
use App\Models\Department;
use App\Models\File as ModelsFile;
use App\Models\Response;
use App\Models\Tasks\LogWork;
use App\Models\Tasks\Project;
use App\Models\Tasks\ProjectState;
use App\Models\Tasks\Ticket;
use App\Models\Tasks\TicketComment;
use App\Models\TeamSaleArea;
use App\Models\User;
use App\Notifications\EmailNotification;
use App\Transformer\ClientTransformer;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;

class TicketController extends BaseController
{
    /**
     * Danh sách ticket
     */
    public function index(Request $request)
    {
        try {
            $fromDate = Carbon::now()->subDays(7);
            if ($request->time) {
                switch ($request->time) {
                    case '1MONTH':
                        $fromDate = Carbon::now()->subMonths(1);
                        break;
                    case '3MONTHS':
                        $fromDate = Carbon::now()->subMonths(3);
                        break;
                    case '1YEAR':
                        $fromDate = Carbon::now()->subYears(1);
                        break;
                    default:
                        $fromDate = Carbon::now()->subDays(7);
                        break;
                }
            }

            $rs = ProjectState::with([
                'tickets' => function ($query) use ($fromDate, $request) {
                    $query->where('created_at', '>=', $fromDate)
                        ->when($request->search, function ($q, $search) {
                            $q->where('title', 'LIKE', '%' . $search . '%')
                                ->orWhere('code', 'LIKE', '%' . $search . '%');
                        })
                        ->when($request->userIds, function ($q, $userIds) {
                            $q->whereIn('assign_to', $userIds);
                        })
                        ->when($request->priority, function ($q, $priority) {
                            $q->where('priority', $priority);
                        })
                        ->orderBy('ticket_number', 'DESC');
                },
                'tickets.assignTo.avatar',
            ])
                ->where('task_project_id', $request->projectId)
                ->orderBy('sort')
                ->get();

            return response()->json($rs);
        } catch (Exception $e) {
            Log::error('TicketController index: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    /**
     * Lấy trạng thái của ticket
     */
    public function getStatus(Request $request)
    {
        $project = Project::with('projectStates')
            ->where('id', $request->projectId)
            ->first();
        if (!$project) {
            return response()->json(['message' => 'Dữ liệu không tồn tại!'], 400);
        }

        $projectStates = $project->projectStates ?: collect([]);
        $rs = [];
        foreach ($projectStates as $state) {
            $rs[] = [
                'id' => $state->id,
                'title' => $state->state_name,
            ];
        }
        return response()->json($rs);
    }

    /**
     * Lấy loại của ticket
     */
    public function getType()
    {
        return response()->json(array_map(function ($key, $val) {
            return [
                'id' => $key,
                'title' => $val,
            ];
        }, array_keys(Ticket::TICKET_TYPE), Ticket::TICKET_TYPE));
    }

    /**
     * Lấy độ khó của ticket
     */
    public function getTicketLevel()
    {
        return response()->json(array_map(function ($key, $val) {
            return [
                'id' => $key,
                'title' => $val,
            ];
        }, array_keys(Ticket::TICKET_LEVEL), Ticket::TICKET_LEVEL));
    }

    /**
     * Lấy đánh giá của leader
     */
    public function getLeaderReview()
    {
        return response()->json(array_map(function ($key, $val) {
            return [
                'id' => $key,
                'title' => $val,
            ];
        }, array_keys(Ticket::LEADER_REVIEW), Ticket::LEADER_REVIEW));
    }

    /**
     * Lấy mức ưu tiên của ticket
     */
    public function getPriority()
    {
        return response()->json(array_map(function ($key, $val) {
            return [
                'id' => $key,
                'title' => $val,
            ];
        }, array_keys(Ticket::TICKET_PRIORITY), Ticket::TICKET_PRIORITY));
    }

    /**
     * Thêm mới ticket
     */
    public function store(Request $request)
    {
        $request->validate([
            'task_project_id' => 'required|uuid',
            'title' => 'required',
            'status' => 'required',
            'type' => ['required', Rule::in(array_keys(Ticket::TICKET_TYPE))],
            'priority' => ['required', Rule::in(array_keys(Ticket::TICKET_PRIORITY))],
            'start_date' => 'required',
            'end_date' => 'required|date_format:Y-m-d|after_or_equal:start_date'
        ], [], [
            'project_id' => 'Dự án',
            'title' => 'Tên Ticket',
            'status' => 'Trạng thái',
            'type' => 'Loại Ticket',
            'priority' => 'Độ ưu tiên',
            'start_date' => 'Thời gian dự kiến bắt đầu',
            'end_date' => 'Thời gian dự kiến kết thúc'
        ]);

        DB::beginTransaction();
        try {
            $allowExtensions = ['jpg', 'jpeg', 'png', 'svg', 'pdf', 'doc', 'docx', 'xls', 'xlsx'];
            if ($request->hasFile('attachments')) {
                foreach ($request->file('attachments') as $file) {
                    if (!in_array($file->extension(), $allowExtensions)) {
                        return response()->json(['message' => "Chỉ được chọn các file:  ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx', 'xls', 'xlsx']"], 400);
                    }
                }
            }

            $project = Project::with('leader', 'projectResources')->find($request->task_project_id);
            if (!$project) {
                return response()->json(['message' => 'Dữ liệu không tồn tại!'], 400);
            }

            $resources = $project->projectResources->pluck('member_id')->toArray();
            if (!$resources) {
                return response()->json(['message' => 'Dữ liệu không tồn tại!'], 400);
            }

            if ($request->assign_to && !in_array($request->assign_to, $resources)) {
                return response()->json([], 403);
            }

            $model = new Ticket();
            if ($request->id) {
                $model = Ticket::find($request->id);
                if (!$model) {
                    return response()->json(['message' => 'Dữ liệu không tồn tại'], 400);
                }

                $max = $model->ticket_number;
            } else {
                $max = Ticket::where('task_project_id', $project->id)->max('ticket_number');
                $max = $max > 0 ? ($max + 1) : 1;
            }

            $model->task_project_id = $project->id;
            $model->parent_id = $request->parent_id;
            $model->ticket_number = $max;
            $model->title = $request->title;
            $model->reporter_id = auth()->user()->id;
            $model->client_id = $request->client_id;
            $model->status = $request->status;
            $model->start_date = $request->start_date;
            $model->end_date = $request->end_date;
            $model->estimate_time = $request->estimate_time;
            $model->description = $request->description;
            $model->type = $request->type;
            $model->assign_to = $request->assign_to;
            $model->priority = $request->priority;
            $model->difficult_level = $request->difficult_level;
            $model->source = $request->source ?: Ticket::SOURCE_SYSTEM;
            $model->phone_number = $request->phone_number;
            $model->code = $project->code . '-' . $max;
            $model->kpi_id = $request->kpi_id;
            $model->save();

            # kiểm tra đẩy file đính kèm nếu có
            if ($request->hasFile('attachments')) {
                $model->storeAttachmentFiles($request->file('attachments'));
            }

            DB::commit();

            // # bắn notify cho người đc assign
            $assignUser = $model->assignTo;
            if ($assignUser) {
                $message = [
                    'subject' => '[' . env('APP_NAME') . '] - ' . $model->title,
                    'greeting' => 'Xin chào, ' . $assignUser->name,
                    'body' => 'Bạn vừa được gán ticket: ' . $model->title,
                    'actionTxt' => 'Xem chi tiết',
                    'actionUrl' => $this->generateTicketUrl($project->id, $model->id),
                ];

                $assignUser->notify(new EmailNotification($message));
            }

            return response()->json($model);
        } catch (Exception $e) {
            DB::rollback();
            Log::error('TicketController store: ' . $e->getMessage());
            throw $e;
            return response()->json([], 500);
        }
    }

    private function generateTicketUrl($projectId, $ticketId)
    {
        return env('APP_URL') . '/tasks-management/projects/' . $projectId . '/tickets/' . $ticketId;
    }

    public function updateStatus(Request $request)
    {
        try {
            $model = Ticket::find($request->id);
            if (!$model) {
                return response()->json(['message' => 'Dữ liệu không tồn tại!'], 400);
            }

            $model->status = $request->status;
            $model->save();

            return response()->json(['Xử lý thành công!']);
        } catch (Exception $e) {
            Log::error("TicketController updateStatus: " . $e->getMessage());

            return response()->json([], 500);
        }
    }

    /** logWork
     * @param uuid task_ticket_id
     * @param double logwork_amount
     * @param Datetime logwork_time
     */
    public function logWork(Request $request)
    {
        $request->validate([
            'logwork_id' => 'nullable|uuid',
            'logwork_amount' => 'required|numeric|min:0.5',
            'logwork_time' => 'required|date_format:Y-m-d',
            'note' => 'nullable|max:500'
        ], [], [
            'task_ticket_id' => 'Nhiệm vụ',
            'logwork_amount' => 'Thời gian làm việc',
            'logwork_time' => 'Ngày logwork'
        ]);

        try {
            $ticket = Ticket::find($request->id);
            if (!$ticket) {
                return response()->json(['message' => 'Không tìm thấy dữ liệu'], 404);
            }

            $model = new LogWork();
            if ($request->logwork_id) {
                $model = LogWork::find($request->logwork_id);
                if (!$model) {
                    return response()->json(['message' => 'Không tìm thấy dữ liệu'], 404);
                }
            }
            $model->task_ticket_id = $ticket->id;
            $model->logwork_amount = $request->logwork_amount;
            $model->logwork_time = $request->logwork_time;
            $model->note = $request->note;
            $model->created_by = auth()->user()->id;
            $model->save();
            $model->owner->avatar;
            return response()->json(['message' => 'Xử lý thành công', 'logWork' => $model]);
        } catch (Exception $e) {
            Log::error("TicketController logWork: " . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function removeWorkLog(Request $request)
    {
        try {
            $model = LogWork::where('id', $request->id)
                ->where('task_ticket_id', $request->ticketId)
                ->where('created_by', auth()->user()->id)
                ->first();
            if (!$model) {
                return response()->json(['message' => 'Không tìm thấy dữ liệu'], 404);
            }

            $model->delete();
            return response()->json(['message' => 'Xử lý thành công']);
        } catch (Exception $e) {
            Log::error("TicketController removeWorkLog: " . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function getClient(Request $request)
    {
        try {
            $query = Client::with('province:province_id,name', 'district:district_id,name')
                ->select('id', 'code', 'province_id', 'district_id', 'name', 'type', 'phone_number');
            if (!empty($request->keyword)) {
                $query->searchTextLike($request->keyword);
            }

            $districtIds = [];
            $provinceIds = [];
            if ($request->district_id) {
                $districtIds = [$request->district_id];
            } elseif ($request->user()->user && $request->user()->user->position && $request->user()->user->position->department_id == Department::SALE_DEPARTMENT) {
                $area = TeamSaleArea::getProvincesAndDistrictsBySaleArea(auth()->user());
                $provinceIds = $area['provinceIds'];
                $districtIds = $area['districtIds'];

                if (
                    $request->province_id && !in_array($request->province_id, $provinceIds)
                    || $request->district_id && !in_array($request->district_id, $districtIds)
                ) {
                    return response()->json(['message' => 'Bạn không có quyền truy cập thị trường này'], 403);
                }
            }

            if ($districtIds) {
                if ($provinceIds) {
                    $query->where(function ($subQuery) use ($districtIds, $provinceIds) {
                        $subQuery->whereIn('district_id', $districtIds)
                            ->orWhere(function ($subQuery) use ($provinceIds) {
                                $subQuery->whereNull('district_id')
                                    ->whereIn('province_id', $provinceIds);
                            });
                    });
                } else {
                    $query->whereIn('district_id', $districtIds);
                }
            } elseif ($request->province_id) {
                $query->where('province_id', $request->province_id);
            }

            $query->when($request->type, function ($subQuery, $type) {
                return $subQuery->where('type', $type);
            });

            $rs = $query->orderBy('created_at', 'DESC')->paginate(20)->through(function ($item) {
                return (new ClientTransformer)->transform($item);
            });
            return response()->json(['data' => $rs]);
        } catch (Exception $e) {
            Log::error($e);
            return response()->json([], 500);
        }
    }

    /**
     * Thông tin ticket
     */
    public function show(Request $request)
    {
        try {
            $data = Ticket::where('id', $request->id)->with(
                'reporter',
                'client',
                'comments.user.avatar',
                'files',
                'workLogs.owner.avatar'
            )->first();

            return response()->json($data);
        } catch (\Exception $e) {
            Log::error('TicketController show: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    /**
     * Xoá ticket
     */
    public function destroy(Request $request)
    {
        try {
            $ticket = Ticket::find($request->id);
            if (!$ticket) {
                return response()->json(['message' => 'Dữ liệu không tồn tại'], 400);
            }

            $ticket->delete();

            return response()->json(['data' => $ticket], 200);
        } catch (\Exception $e) {
            Log::error('TicketController destroy: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    /**
     * Thêm comment cho tictet
     */
    public function addComment(Request $request)
    {
        $user = auth()->user();
        $data = [
            'ticket_id' => $request->id,
            'user_id' => $user->id,
            'content' => $request->comment,
        ];
        TicketComment::create($data);
        return Response::formatResponse(config('apicode.SUCCESS'), $data);
    }

    /**
     * Xoá file đính kèm
     */
    public function destroyFile(Request $request)
    {
        try {
            ModelsFile::where('id', $request->id)->delete();
            unlink($request->path);
            return response()->json(['message' => 'Success'], 200);
        } catch (\Exception $e) {
            return $e;
        }
    }
}
