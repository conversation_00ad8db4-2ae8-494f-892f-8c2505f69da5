<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\KPI;
use App\Models\KPIHistory;
use App\Models\KPISummary;
use App\Models\KPISummaryHistory;
use App\Models\Response;
use App\Models\SystemConfig;
use App\Models\UserWorking;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class KPIController extends Controller
{
    public function index(Request $request)
    {
        $validate = Validator::make($request->all(), [
            'month' => 'required|date_format:m-Y'
        ], [], ['month' => 'Tháng']);

        if ($validate->fails()) {
            return response()->json(['message' => $validate->errors()->first()], 400);
        }
        $department_id = $request->department_id;
        $rank_code = $request->rank_code;
        $date = explode('-', $request->month);
        $month = $date[0];
        $year =  $date[1];
        $query = KPISummary::with(['kpi', 'owner.avatar', 'owner.user', 'department', 'position', 'owner.user.userLeader.user.avatar'])
            ->whereMonth('to_date', $month)
            ->whereYear('to_date', $year);
        $count_staff = UserWorking::where('department_id', $department_id)->active()->count();
        $count_all_kpi = with(clone $query)->where('department_id', $department_id)->count();

        KPISummary::getConditionList($request, $query, $department_id, $rank_code);
        $dt = $query
            ->orderBy('to_date', 'DESC')
            ->orderBy('user_id')->paginate(20);

        $warning_notify = null;
        if ($count_staff != $count_all_kpi) {
            $warning_notify = 'Chưa đủ thành viên trong phòng ban đăng ký KPI';
        }
        $data = [
            'data' => $dt,
            'time_config' =>  SystemConfig::getKpiAssessmentTime(),
            'warning_notify' => $warning_notify,
        ];
        return Response::formatResponse(config('apicode.SUCCESS'), $data);
    }

    public function details(Request $request)
    {
        $data = KPISummary::with('owner.user', 'kpi')
            ->where('id', $request->kpiSummaryId)
            ->first();
        if (!$data) {
            return response()->json(['message' => 'Không tìm thấy dữ liệu'], 404);
        }

        if (
            $data->user_id == auth()->user()->id
            || in_array($data->owner->user->dependent_position_id, KPISummary::getAllPosition())
            || auth()->user()->hasAnyPermission(['approve_kpi', 'kpi_list_management', 'approve_kpi_leader'])
        ) {
            $config = SystemConfig::getKpiAssessmentTime();

            $isAllowEdit = $this->isAllowEdit($data, $config)->status;

            $isAllowReopenByLeader = $this->isAllowReopenByLeader($data, $config)->status;

            $isAllowApproveByLeader = $this->isAllowApproveByLeader($data, $config)->status;

            return response()->json([
                'summaryData' => $data,
                'isAllowEdit' =>  $isAllowEdit,
                'isAllowReopenByLeader' =>  $isAllowReopenByLeader,
                'isAllowApproveByLeader' =>  $isAllowApproveByLeader
            ]);
        } else {
            return response()->json(['message' => 'Bạn không có quyền xem KPI của nhân viên này'], 403);
        }
    }

    public function store(Request $request)
    {
        $request->validate([
            'kpi_summary' => 'required',
            'kpi_summary.from_date' => 'required|date_format:Y-m-d',
            'kpi_summary.to_date' => 'required|date_format:Y-m-d',
            'kpi' => 'required|array',
            'kpi.*.group_criteria' => 'required|numeric',
            'kpi.*.criteria_name' => 'required|max:1000',
            'kpi.*.proportion' => 'required|numeric',
        ], [], [
            'kpi_summary' => 'Thông tin dăng ký KPI',
            'kpi_summary.from_date' => 'Thời gian',
            'kpi_summary.to_date' => 'Thời gian',
            'kpi' => 'Tiêu chí KPI',
            'kpi.*.group_criteria' => 'Nhóm tiêu chí',
            'kpi.*.criteria_name' => 'Tên tiêu chí',
            'kpi.*.proportion' => 'Tỷ trọng',
        ]);
        try {
            $kpi_summary = $request->kpi_summary;
            $kpi_data = KPISummary::where('user_id', auth()->user()->id)
                ->whereMonth('to_date', Carbon::parse($kpi_summary['to_date'])->format('m'))
                ->whereYear('to_date', Carbon::parse($kpi_summary['to_date'])->format('Y'))
                ->first();
            if ($kpi_data) {
                return response()->json(['message' => 'Bạn đã đăng ký KPI tháng này'], 404);
            }

            $config = SystemConfig::getKpiAssessmentTime();
            if (Carbon::now()->gt(Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-') . $config[SystemConfig::EMPLOYEE_REGISTER_KPI] . ' 23:59:59'))) {
                return response()->json(['message' => 'Quá thời gian đăng ký KPI'], 404);
            }
            DB::beginTransaction();

            $kpi = $request->kpi;
            // store to summary
            $kpiSummary = new KPISummary();
            $kpiSummary->user_id = auth()->user()->id;
            $kpiSummary->department_id = auth()->user()->user->department_id;
            $kpiSummary->position_id = auth()->user()->user->position_id;
            $kpiSummary->level_position = auth()->user()->user->position->is_leader ? KPISummary::LEADER_POSITION : KPISummary::STAFF_POSITION;
            $kpiSummary->time_range = KPISummary::MONTH;
            $kpiSummary->from_date = $kpi_summary['from_date'];
            $kpiSummary->to_date = $kpi_summary['to_date'];
            $kpiSummary->status = KPISummary::STAFF_REGISTER_KPI;
            $kpiSummary->save();
            // store to kpi history
            $kpiSumHistory = new KPISummaryHistory();
            $kpiSumHistory->kpi_summary_id = $kpiSummary->id;
            $kpiSumHistory->user_id = $kpiSummary->user_id;
            $kpiSumHistory->department_id = $kpiSummary->department_id;
            $kpiSumHistory->position_id = $kpiSummary->position_id;
            $kpiSumHistory->level_position = $kpiSummary->level_position;
            $kpiSumHistory->time_range = $kpiSummary->time_range;
            $kpiSumHistory->from_date = $kpiSummary->from_date;
            $kpiSumHistory->to_date = $kpiSummary->to_date;
            $kpiSumHistory->status = $kpiSummary->status;
            $kpiSumHistory->save();
            //loop kpi
            $kpi_array = [];
            $kpi_history_array = [];
            foreach ($kpi as $e) {
                $sub_kpi = [
                    'kpi_summary_id' => $kpiSummary->id,
                    'group_criteria' => $e['group_criteria'],
                    'criteria_name' => $e['criteria_name'],
                    'proportion' => $e['proportion'],
                    'target_content' => $e['target_content']
                ];
                array_push($kpi_array, $sub_kpi);
                array_push($kpi_history_array, $sub_kpi);
            }
            KPI::insert($kpi_array);
            KPIHistory::insert($kpi_history_array);
            DB::commit();
            return response()->json(['message' => 'Xử lý thành công']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('KPIController store: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function update(Request $request)
    {
        $request->validate([
            'kpi_summary' => 'required',
            'kpi_summary.from_date' => 'required|date_format:Y-m-d',
            'kpi_summary.to_date' => 'required|date_format:Y-m-d',
            'kpi' => 'required|array',
            'kpi.*.group_criteria' => 'required|numeric',
            'kpi.*.criteria_name' => 'required|max:1000',
            'kpi.*.proportion' => 'required|numeric',
        ], [], [
            'kpi_summary' => 'Thông tin dăng ký KPI',
            'kpi_summary.from_date' => 'Ngày bắt đầu',
            'kpi_summary.to_date' => 'Ngày kết thúc',
            'kpi' => 'Tiêu chí KPI',
            'kpi.*.group_criteria' => 'Nhóm tiêu chí',
            'kpi.*.criteria_name' => 'Tên tiêu chí',
            'kpi.*.proportion' => 'Tỷ trọng',
        ]);

        try {
            $model = KPISummary::where('id', $request->kpiSummaryId)
                ->where('user_id', auth()->user()->id)
                ->first();

            $checkPermission = $this->isAllowEdit($model);
            if (!$checkPermission->status) {
                return response()->json(['message' => $checkPermission->msg], $checkPermission->code);
            }

            DB::beginTransaction();
            $kpi_summary = $request->kpi_summary;
            $kpi = $request->kpi;

            $model->time_range = $kpi_summary['time_range'];
            $model->from_date = $kpi_summary['from_date'];
            $model->to_date = $kpi_summary['to_date'];
            $model->save();

            foreach ($kpi as $e) {
                $sub_kpi = [
                    'group_criteria' => $e['group_criteria'],
                    'criteria_name' =>  $e['criteria_name'],
                    'proportion' => $e['proportion'],
                    'target_content' => $e['target_content']
                ];
                if (isset($e['id'])) {
                    KPI::where('id', $e['id'])->update($sub_kpi);
                } else {
                    $sub_kpi['kpi_summary_id'] = $model->id;
                    KPI::create($sub_kpi);
                }
            }
            DB::commit();
            return response()->json(['message' => 'Xử lý thành công']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("KPIController update: " . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function leaderConfirmKPI(Request $request)
    {
        try {
            $model = KPISummary::find($request->kpiSummaryId);
            $checkPermission = $this->isAllowApproveByLeader($model);
            if (!$checkPermission->status) {
                return response()->json(['message' => $checkPermission->msg], $checkPermission->code);
            }
            DB::beginTransaction();
            $model->status = KPISummary::LEADER_CONFIRM_KPI;
            $model->leader_confirm_by = auth()->user()->id;
            $model->save();
            $this->createKPIHistory($model);
            DB::commit();
            return response()->json(['message' => 'Xử lý thành công']);
        } catch (Exception $e) {
            DB::rollBack();
            Log::error("KPIController leaderConfirmKPI: " . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function leaderReopenKPI(Request $request, $id)
    {
        try {
            $model = KPISummary::find($request->kpiSummaryId);
            $checkPermission = $this->isAllowReopenByLeader($model);
            if (!$checkPermission->status) {
                return response()->json(['message' => $checkPermission->msg], $checkPermission->code);
            }
            DB::beginTransaction();
            $model->status = KPISummary::LEADER_REOPEN_KPI;
            $model->leader_confirm_by = auth()->user()->id;
            $model->save();
            $this->createKPIHistory($model);
            DB::commit();
            return response()->json(['message' => 'Xử lý thành công']);
        } catch (Exception $e) {
            DB::rollBack();
            Log::error("KPIController leaderConfirmKPI: " . $e->getMessage());
            return response()->json([], 500);
        }
    }

    private function createKPIHistory($kpi_summary)
    {
        KPISummaryHistory::create([
            'kpi_summary_id' => $kpi_summary->id,
            'user_id' => $kpi_summary->user_id,
            'department_id' => $kpi_summary->department_id,
            'position_id' => $kpi_summary->position_id,
            'time_range' => $kpi_summary->time_range,
            'level_position' => $kpi_summary->level_position,
            'from_date' => $kpi_summary->from_date,
            'to_date' => $kpi_summary->to_date,
            'status' => $kpi_summary->status,
            'leader_confirm_by' => $kpi_summary->leader_confirm_by
        ]);
    }

    public function assignKPI($id)
    {
        $kpi_summary = KPISummary::where('user_id', $id)->get();
        $kpi_summary_ids = [];
        foreach ($kpi_summary as $e) {
            array_push($kpi_summary_ids, $e->id);
        }
        $kpi = KPI::whereIn('kpi_summary_id', $kpi_summary_ids)->where('group_criteria', true)->get();
        return $kpi;
    }

    private function isAllowEdit($data, $config = null)
    {
        if (!$data) {
            return (object) [
                'status' => false,
                'code' => 404,
                'msg' => 'Không tìm thấy dữ liệu',
            ];
        }

        $config = $config ?: SystemConfig::getKpiAssessmentTime();
        $status = [
            KPISummary::STAFF_REGISTER_KPI,
            KPISummary::LEADER_REOPEN_KPI,
        ];
        if (
            !$config
            || !in_array($data->status, $status)
            || $data->user_id != auth()->user()->id
            || Carbon::now()->gt(Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-') . $config[SystemConfig::EMPLOYEE_REGISTER_KPI] . ' 23:59:59'))
        ) {
            return (object) [
                'status' => false,
                'code' => 404,
                'msg' => ($data->user_id != auth()->user()->id || !in_array($data->status, $status)) ? 'Bạn không có quyền' : 'Đã quá thời gian cho phép',
            ];
        }

        return (object) [
            'status' => true,
            'code' => 200,
            'msg' => 'Allowed'
        ];
    }

    private function isAllowApproveByLeader($data, $config = null)
    {
        if (!$data) {
            return (object) [
                'status' => false,
                'code' => 404,
                'msg' => 'Không tìm thấy dữ liệu',
            ];
        }

        $config = $config ?: SystemConfig::getKpiAssessmentTime();
        $leaderPositions = UserWorking::allActive()
            ->where('user_id', $data->user_id)
            ->pluck('dependent_position_id')
            ->toArray();

        $status = [
            KPISummary::STAFF_REGISTER_KPI,
            KPISummary::LEADER_REOPEN_KPI,
        ];


        if (
            !$config || (auth()->user()->id == $data->user_id)
            || !in_array($data->status, $status)
            || Carbon::now()->gt(Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-') . $config[SystemConfig::EMPLOYEE_REGISTER_KPI] . ' 23:59:59'))
        ) {
            return (object) [
                'status' => false,
                'code' => 404,
                'msg' => Carbon::now()->gt(Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-') . $config[SystemConfig::EMPLOYEE_REGISTER_KPI] . ' 23:59:59')) ? 'Đã quá thời gian cho phép' : 'Bạn không có quyền',
            ];
        }

        $position_id = [];
        foreach (auth()->user()->user_workings as $user_working) {
            array_push($position_id, $user_working->position_id);
        }
        $count_position_ids = count(array_intersect($position_id, $leaderPositions));
        if ($count_position_ids == 0) {
            return (object) [
                'status' => false,
                'code' => 404,
                'msg' => 'Not Allowed'
            ];
        }

        return (object) [
            'status' => true,
            'code' => 200,
            'msg' => 'Allowed'
        ];
    }

    private function isAllowReopenByLeader($data, $config = null)
    {
        if (!$data) {
            return (object) [
                'status' => false,
                'code' => 404,
                'msg' => 'Không tìm thấy dữ liệu',
            ];
        }

        $config = $config ?: SystemConfig::getKpiAssessmentTime();
        $leaderPositions = UserWorking::allActive()
            ->where('user_id', $data->user_id)
            ->pluck('dependent_position_id')
            ->toArray();
        if (
            !$config
            || (auth()->user()->id == $data->user_id)
            || $data->status != KPISummary::LEADER_CONFIRM_KPI
            || Carbon::now()->gt(Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-') . $config[SystemConfig::EMPLOYEE_REGISTER_KPI] . ' 23:59:59'))
        ) {
            return (object) [
                'status' => false,
                'code' => 404,
                'msg' => Carbon::now()->gt(Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-') . $config[SystemConfig::EMPLOYEE_REGISTER_KPI] . ' 23:59:59')) ? 'Đã quá thời gian cho phép' : 'Bạn không có quyền',
            ];
        }

        $position_id = [];
        foreach (auth()->user()->user_workings as $user_working) {
            array_push($position_id, $user_working->position_id);
        }
        $count_position_ids = count(array_intersect($position_id, $leaderPositions));
        if ($count_position_ids == 0) {
            return (object) [
                'status' => false,
                'code' => 404,
                'msg' => 'Not Allowed'
            ];
        }

        return (object) [
            'status' => true,
            'code' => 200,
            'msg' => 'Allowed'
        ];
    }
}
