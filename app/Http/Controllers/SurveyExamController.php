<?php

namespace App\Http\Controllers;

use App\Models\SurveyExam;
use App\Models\SurveyQuestionForExam;
use App\Models\SurveyUserForExamHistory;
use App\Models\SurveyUserForExamSummary;
use App\Models\UserWorking;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use App\Events\NotificationToUserJoinSurveyEvent;

class SurveyExamController extends Controller
{
    public function index(Request $request)
    {
        try {
            $rs = SurveyExam::when($request->name, function ($query, $name) {
                $query->where('name', $name);
            })
                ->orderBy('created_at', 'DESC')
                ->paginate(20);

            return response()->json(['data' => $rs]);
        } catch (Exception $e) {
            Log::error('SurveyExamController index: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    /**
     * Lưu thông tin bài khảo sát
     */
    public function store(Request $request)
    {
        $rules = $request->id
            ? [
                'name' => ['required', 'max:255', 'unique:survey_exams,name,' . $request->id],
                'start_time' => 'required|date_format:Y-m-d H:i|after:' . Carbon::now(),
                'end_time' => 'required|date_format:Y-m-d H:i|lte:start_time',
                'questions' => 'array',
                'users' => 'array'
            ]
            : [
                'name' => ['required', 'max:255', 'unique:survey_exams'],
                'start_time' => 'date_format:Y-m-d H:i|after:' . Carbon::now(),
                'end_time' => 'required|date_format:Y-m-d H:i|lte:start_time',
                'questions' => 'array',
                'users' => 'array'
            ];

        $request->validate($rules, [], [
            'name' => 'Tiêu đề bài kiểm tra',
            'start_time' => 'Thời gian bắt đầu',
            'end_time' => 'Thời gian kết thúc',
            'questions' => 'Câu hỏi',
            'users' => 'Người tham gia'
        ]);

        DB::beginTransaction();
        try {
            $model = new SurveyExam();
            if ($request->id) {
                $model = SurveyExam::where('id', $request->id)
                    ->where('start_time', '>', Carbon::now())
                    ->first();
                if (!$model) {
                    return response()->json(['message' => 'Dữ liệu không tồn tại'], 400);
                }
            }

            $checkExistDoing = SurveyUserForExamSummary::where('is_completed', false)
                ->when($request->id, function ($query, $examId) {
                    return $query->where('exam_id', '<>', $examId);
                })
                ->where('end_time', '>=', $request->start_time)
                ->whereIn('user_id', $request->users)
                ->count();
            if ($checkExistDoing > 0) {
                return response()->json(['message' => 'Có nhân viên đang tham gia cuộc khảo sát khác!'], 400);
            }

            $model->name = trim($request->name);
            $model->start_time = $request->start_time;
            $model->end_time = $request->end_time;
            $model->number_of_question = count($request->questions);
            $model->number_of_survey = count($request->users);
            $model->save();

            if ($request->id) {
                SurveyQuestionForExam::where('exam_id', $request->id)->delete();
                SurveyUserForExamSummary::where('exam_id', $request->id)->delete();
            }

            $questions = [];
            foreach ($request->questions as $sort => $question) {
                $questions[] = [
                    'id' => Str::uuid()->toString(),
                    'exam_id' => $model->id,
                    'question_id' => $question,
                    'sort' => $sort,
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now()
                ];
            }

            DB::table('survey_question_for_exams')->insert($questions);

            $summary = [];
            foreach ($request->users as $userId) {
                $summary[] = [
                    'id' => Str::uuid()->toString(),
                    'user_id' => $userId,
                    'exam_id' => $model->id,
                    'total_question' => $model->number_of_question,
                    'start_time' => $model->start_time,
                    'end_time' => $model->end_time,
                    'is_completed' => false,
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now()
                ];
            }
            DB::table('survey_user_for_exam_summary')->insert($summary);

            DB::commit();
            // send notification via onesignal
            event(new NotificationToUserJoinSurveyEvent($request->users, $model->name, $model->start_time));

            return response()->json(['message' => $request->id ? 'Cập nhật câu hỏi thành công!' : 'Thêm câu hỏi thành công!']);
        } catch (Exception $e) {
            DB::rollBack();
            Log::error('SurveyExamController store: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    /**
     * Lấy thông tin bài khảo sát để chỉnh sửa
     */
    public function details(Request $request)
    {
        try {
            $model = SurveyExam::with(['questions', 'summary.user'])
                ->where('id', $request->id)
                ->first();
            if (!$model) {
                return response()->json(['message' => 'Dữ liệu không tồn tại'], 404);
            }

            return response()->json($model);
        } catch (Exception $e) {
            Log::error('SurveyExamController details: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    /**
     * Xoá bài khảo sát
     */
    public function delete(Request $request)
    {
        DB::beginTransaction();
        try {
            $model = SurveyExam::find($request->id);
            if (!$model) {
                return response()->json(['message' => 'Dữ liệu không tồn tại'], 404);
            }

            $model->questions()->delete();
            $model->summary()->delete();
            $model->history()->delete();
            $model->delete();
            DB::commit();
            return response()->json(['message' => 'Xóa bài khảo sát thành công!']);
        } catch (Exception $e) {
            DB::rollBack();
            Log::error('SurveyExamController delete: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    /**
     * Lấy danh sách user có thể tham gia khảo sát (Lúc tạo bài khảo sát)
     */
    public function getParticipants(Request $request)
    {
        try {
            $usersDoing = SurveyUserForExamSummary::where('is_completed', false)
                ->when($request->startTime, function ($query, $startTime) {
                    return $query->where('end_time', '>=', $startTime);
                })
                ->when($request->examId, function ($query, $examId) {
                    return $query->where('exam_id', '!=', $examId);
                })
                ->pluck('user_id')
                ->toArray();
            $users = UserWorking::with('user')
                ->active()
                ->notAdmin()
                ->when($usersDoing, function ($query, $usersDoing) {
                    return $query->whereNotIn('user_id', $usersDoing);
                })
                ->get();
            $rs = [];
            foreach ($users as $user) {
                $rs[] = [
                    'id' => $user->user->id,
                    'name' => $user->user->name,
                ];
            }
            return response()->json($rs);
        } catch (Exception $e) {
            Log::error('SurveyExamController getParticipants: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    /**
     * Kiểm tra user có bài khảo sát nào cần thực hiện không
     */
    public function checkDoExam(Request $request)
    {
        $check = SurveyUserForExamSummary::with('exam')
            ->where('is_completed', false)
            ->where('start_time', '<=', Carbon::now())
            ->where('end_time', '>', Carbon::now())
            ->where('user_id', $request->user()->id)
            ->first();
        if ($check) {
            return response()->json([
                'id' => $check->exam->id,
                'name' => $check->exam->name
            ]);
        }

        return response()->json(false);
    }

    /**
     * Thí sinh lấy thông tin bài khảo sát để làm
     */
    public function getInfo2DoExam(Request $request)
    {
        try {
            if (!$request->user()->can('doSurvey', SurveyExam::class)) {
                return response()->json([], 403);
            }

            $model = SurveyExam::with(['questions.answers'])
                ->where('id', $request->id)
                ->first();

            if (!$model) {
                return response()->json(['message' => 'Dữ liệu không tồn tại.'], 404);
            }

            $questions = [];
            foreach ($model->questions as $question) {
                $questions[] = [
                    'id' => $question->id,
                    'content' => $question->content,
                    'answers' => $question->answers
                ];
            }

            return response()->json([
                'id' => $model->id,
                'name' => $model->name,
                'duration' => (new Carbon($model->end_time))->diffInMinutes((new Carbon($model->start_time))),
                'startTime' => $model->start_time,
                'endTime' => $model->end_time,
                'totalQuestion' => $model->number_of_question,
                'questions' => $questions

            ]);
        } catch (Exception $e) {
            Log::error('SurveyExamController getInfo2DoExam: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    /**
     * Lưu kết quả bài làm của thí sinh
     */
    public function doExam(Request $request)
    {
        $request->validate([
            'result' => 'required|array',
            'result.*.questionId' => 'required|uuid',
            'result.*.answerId' => 'nullable|uuid',
        ], [], [
            'result' => 'Kết quả bài làm',
            'result.*.questionId' => 'Mã câu hỏi',
            'result.*.answerId' => 'Mã câu trả lời'
        ]);

        DB::beginTransaction();
        try {
            $model = SurveyExam::with(['questions.answers'])
                ->where('id', $request->id)
                ->first();

            if (!$model) {
                return response()->json(['message' => 'Dữ liệu không tồn tại.'], 404);
            }

            $history = [];
            $answersCorrect = 0;
            $userId = $request->user()->id;
            foreach ($request->result as $item) {
                $originQuestion = $model->questions->find($item['questionId']);
                if ($originQuestion) {
                    $historyItem = [
                        'id' => Str::uuid()->toString(),
                        'user_id' => $userId,
                        'exam_id' => $model->id,
                        'question_id' => $originQuestion->id,
                        'answer_id' => null,
                        'is_correct' => false,
                        'created_at' => Carbon::now(),
                        'updated_at' => Carbon::now()
                    ];
                    $originAnswer = $originQuestion->answers->find($item['answerId']);
                    if ($originAnswer) {
                        $correct = $originAnswer->is_correct;
                        $answersCorrect += $correct ? 1 : 0;
                        $historyItem['answer_id'] = $originAnswer->id;
                        $historyItem['is_correct'] = $correct;
                    }
                    $history[] = $historyItem;
                }
            }

            $model->number_of_participant += 1;
            $model->number_of_win += $answersCorrect == $model->number_of_question ? 1 : 0;
            $model->save();

            SurveyUserForExamSummary::where('exam_id', $request->id)
                ->where('user_id', $request->user()->id)
                ->update([
                    'total_correct' => $answersCorrect,
                    'total_incorrect' => $model->number_of_question - $answersCorrect,
                    'is_completed' => true
                ]);

            DB::table('survey_user_for_exam_history')->insert($history);
            DB::commit();

            return response()->json(['message' => 'Xử lý thành công!']);
        } catch (Exception $e) {
            DB::rollback();
            Log::error('SurveyExamController doExam: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    /**
     * Xem lịch sử khảo sát (summary)
     */
    public function viewHistory(Request $request)
    {
        try {
            $query = SurveyUserForExamSummary::with(['user', 'exam.questions.answers'])
                ->when($request->name, function ($subQuery, $name) {
                    return $subQuery->whereHas('exam', function ($q) use ($name) {
                        return $q->where('name', 'LIKE', '%' . $name . '%');
                    });
                });

            if ($request->user()->hasPermissionTo('survey_exam_management', 'api')) {
                $query->when($request->userId, function ($subQuery, $userId) {
                    return $subQuery->whereIn('user_id', explode(',', $userId));
                });
            } else {
                $query->where('user_id', $request->user()->id);
            }
            $rs = $query->orderBy('created_at', 'DESC')
                ->paginate(20);

            return response()->json(['data' => $rs]);
        } catch (Exception $e) {
            Log::error('SurveyExamController viewHistory: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    /**
     * Xem chi tiết lịch sử 1 bài khảo sát
     */
    public function detailsHistory(Request $request)
    {
        try {
            $userId = $request->user()->id;
            if ($request->user()->hasPermissionTo('survey_exam_management', 'api')) {
                $userId = $request->userId;
            }

            if (!$userId) {
                return response()->json(['message' => 'Dữ liệu không tồn tại', 404]);
            }

            $rs = SurveyUserForExamHistory::with(['exam', 'question.answers'])
                ->where('exam_id', $request->examId)
                ->where('user_id', $userId)
                ->orderBy('created_at', 'ASC')
                ->get();

            return response()->json($rs);
        } catch (Exception $e) {
            Log::error('SurveyExamController detailsHistory: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }
}
