<?php

namespace App\Http\Controllers;

use App\Models\Response;
use Illuminate\Http\Request;
use Illuminate\Notifications\DatabaseNotification as Notification;
use App\Events\MarkAsReadNotificationEvent;
use App\Transformer\APIJsonResponse;
use App\Transformer\NotificationTransformer;
use Exception;
use PhpParser\Node\Stmt\TryCatch;

class NotificationController extends BaseController
{
    public function index(Request $request)
    {
        $user = auth()->user();
        $data = $user->notifications()->paginate(20);
        return Response::formatResponse(config('apicode.SUCCESS'), $data);
    }

    /**
     * Danh sách ticket của user đang đăng nhập
     */
    function list(Request $request) {
        $user = auth()->user();
        $datas = [];
        foreach ($user->unreadNotifications as $notification) {
            $datas[] = $notification;
        }
        return Response::formatResponse(config('apicode.SUCCESS'), $datas);
    }

    /**
     * Update read_at Notification
     */
    public function markAsRead(Request $request)
    {
        try {
            $id = $request->id;
            $query = Notification::find($id);
            $query->markAsRead();

            // bắn event để xoá đi thông báo này bên local client
            $user = auth()->user();
            $message = [
                'notification_id' => $id,
                'option' => 'remove',
            ];
            event(new MarkAsReadNotificationEvent($user, $message));

            return Response::formatResponse(config('apicode.SUCCESS'), $query);
        } catch (\Exception $e) {
            return Response::formatResponse(config('apicode.INTERNAL_SERVER_ERROR'), $e->getMessage());
        }
    }

    /**
     * Update read all  Notification
     */
    public function markAsReadAll(Request $request)
    {
        try {
            $user = auth()->user();
            $user->unreadNotifications->markAsRead();

            return Response::formatResponse(config('apicode.SUCCESS'), []);
        } catch (\Exception $e) {
            return Response::formatResponse(config('apicode.INTERNAL_SERVER_ERROR'), $e->getMessage());
        }
    }

    function listNotificationMobile() {
        try {
            $user = auth()->user();
            foreach ($user->unreadNotifications as $n) {
                $data[] = $n;
            }
            $datas = (new NotificationTransformer)->transforms($data);
            $rs = (new APIJsonResponse)->responseSuccess($datas);
        } catch (Exception $e) {
            $rs = (new APIJsonResponse)->responseError();
        }
        
        return $rs;
    }

    public function listAllMobile(Request $request)
    {
        try {
            $pre_page = $request['pre_page'] ?? 20;
            $user = auth()->user();
            $query = $user->notifications()->paginate();
            $datas = (new NotificationTransformer)->transforms($query->items());
        
            $option = ['current_page' => $query->currentPage(),
                        'per_page' =>$pre_page,
                        'total' => $query->total(),
                        'badge' => $user->unreadNotifications()->count()
                    ];
            $rs = (new APIJsonResponse)->responseSuccess($datas, null, $option);
        } catch (Exception $e) {
            $rs = (new APIJsonResponse)->responseError();
        }
        return $rs;
    }

    public function markAsReadMobile(Request $request)
    {
        try {
            $id = $request->id;
            $query = Notification::find($id);
            $rs = (new APIJsonResponse)->responseSuccess($query->markAsRead());
        } catch (Exception $e) {
            $rs = (new APIJsonResponse)->responseError();
        }
        
        return $rs;
    }

    public function markAsReadAllMobile(Request $request)
    {
        try {
            $user = auth()->user();
            $rs = (new APIJsonResponse)->responseSuccess( $user->unreadNotifications->markAsRead());
        } catch (Exception $e) {
            $rs = (new APIJsonResponse)->responseError();
        }
        
        return $rs;
    }

    public function markUnReadMobile(Request $request)
    {
        try {
            $id = $request->id;
            $query = Notification::find($id);
            $rs = (new APIJsonResponse)->responseSuccess($query->markAsUnread());
        } catch (Exception $e) {
            $rs = (new APIJsonResponse)->responseError();
        }
        
        return $rs;
    }

    function notiDetailMobile(Request $request) {
        try {
            $id = $request->id;
            $notiDetail = Notification::whereId($id)->first();
            $datas = (new NotificationTransformer)->transform($notiDetail);
            $rs = (new APIJsonResponse)->responseSuccess($datas);
        } catch (Exception $e) {
            $rs = (new APIJsonResponse)->responseError();
        }
        
        return $rs;
    }
}
