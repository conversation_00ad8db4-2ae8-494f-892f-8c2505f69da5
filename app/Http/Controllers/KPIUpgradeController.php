<?php

namespace App\Http\Controllers;

use App\Models\KPISummaryUpgrade;
use App\Models\KPISummaryUpgradeHistory;
use App\Models\KPIUpgrade;
use App\Models\KPIUpgradeHistory;
use App\Models\Response;
use App\Models\SystemConfig;
use App\Models\UserWorking;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class KPIUpgradeController extends Controller
{
    public function index(Request $request)
    {
        $validate = Validator::make($request->all(), [
            'month' => 'required|date_format:m-Y',
        ], [], ['month' => 'Tháng']);

        if ($validate->fails()) {
            return response()->json(['message' => $validate->errors()->first()], 400);
        }
        $department_id = $request->department_id;
        $rank_code = $request->rank_code;
        $date = explode('-', $request->month);
        $month = $date[0];
        $year = $date[1];
        $query = KPISummaryUpgrade::with(['kpi', 'owner.avatar', 'owner.user', 'department', 'position', 'owner.user.userLeader.user.avatar'])
            ->whereMonth('to_date', $month)
            ->whereYear('to_date', $year);
        $count_staff = UserWorking::where('department_id', $department_id)->active()->count();
        $count_all_kpi = with(clone $query)->where('department_id', $department_id)->count();

        KPISummaryUpgrade::getConditionList($request, $query, $department_id, $rank_code);
        $dt = $query
            ->orderBy('to_date', 'DESC')
            ->orderBy('user_id')->paginate(20);

        $warning_notify = null;
        if ($count_staff != $count_all_kpi) {
            $warning_notify = 'Chưa đủ thành viên trong phòng ban đăng ký KPI';
        }
        $data = [
            'data' => $dt,
            'time_config' => SystemConfig::getKpiAssessmentTime(),
            'warning_notify' => $warning_notify,
        ];
        return Response::formatResponse(config('apicode.SUCCESS'), $data);
    }

    public function detail(Request $request)
    {
        $data = KPISummaryUpgrade::with('owner.user', 'kpi')
            ->where('id', $request->kpiSummaryId)
            ->first();
        if (!$data) {
            return response()->json(['message' => 'Không tìm thấy dữ liệu'], 404);
        }

        if (
            $data->user_id == auth()->user()->id
            || in_array($data->owner->user->dependent_position_id, KPISummaryUpgrade::getAllPosition())
            || auth()->user()->hasAnyPermission(['approve_kpi', 'kpi_list_management', 'approve_kpi_leader'])
        ) {
            $config = SystemConfig::getKpiAssessmentTime();

            $isAllowEdit = $this->isAllowEdit($data, $config)->status;

            $isAllowReopenByLeader = $this->isAllowReopenByLeader($data, $config)->status;

            $isAllowApproveByLeader = $this->isAllowApproveByLeader($data, $config)->status;

            return response()->json([
                'summaryData' => $data,
                'isAllowEdit' => $isAllowEdit,
                'isAllowReopenByLeader' => $isAllowReopenByLeader,
                'isAllowApproveByLeader' => $isAllowApproveByLeader,
            ]);
        } else {
            return response()->json(['message' => 'Bạn không có quyền xem KPI của nhân viên này'], 403);
        }
    }

    public function store(Request $request)
    {
        $request->validate([
            'kpi_summary' => 'required',
            'kpi_summary.from_date' => 'required|date_format:Y-m-d',
            'kpi_summary.to_date' => 'required|date_format:Y-m-d',
            'kpi' => 'required|array',
            'kpi.*.criteria_name' => 'required|max:1000',
            'kpi.*.proportion' => 'required|integer',
        ], [], [
            'kpi_summary' => 'Thông tin dăng ký KPI',
            'kpi_summary.from_date' => 'Thời gian',
            'kpi_summary.to_date' => 'Thời gian',
            'kpi' => 'Tiêu chí KPI',
            'kpi.*.criteria_name' => 'Tên tiêu chí',
            'kpi.*.proportion' => 'Tỷ trọng',
        ]);
        try {
            $kpi_summary = $request->kpi_summary;
            $kpi_data = KPISummaryUpgrade::where('user_id', auth()->user()->id)
                ->whereMonth('to_date', Carbon::parse($kpi_summary['to_date'])->format('m'))
                ->whereYear('to_date', Carbon::parse($kpi_summary['to_date'])->format('Y'))
                ->first();
            if ($kpi_data) {
                return response()->json(['message' => 'Bạn đã đăng ký KPI tháng này'], 404);
            }

            $config = SystemConfig::getKpiAssessmentTime();
            if (Carbon::now()->gt(Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-') . $config[SystemConfig::EMPLOYEE_REGISTER_KPI] . ' 23:59:59'))) {
                return response()->json(['message' => 'Quá thời gian đăng ký KPI'], 404);
            }
            DB::beginTransaction();

            // store to summary
            $kpiSummary = new KPISummaryUpgrade();
            $kpiSummary->user_id = auth()->user()->id;
            $kpiSummary->department_id = auth()->user()->user->department_id;
            $kpiSummary->position_id = auth()->user()->user->position_id;
            $kpiSummary->level_position = auth()->user()->user->position->is_leader ? KPISummaryUpgrade::LEADER_POSITION : KPISummaryUpgrade::STAFF_POSITION;
            $kpiSummary->from_date = $kpi_summary['from_date'];
            $kpiSummary->to_date = $kpi_summary['to_date'];
            $kpiSummary->status = KPISummaryUpgrade::STAFF_REGISTER_KPI;
            $kpiSummary->save();

            // store to kpi history
            $kpiSumHistory = new KPISummaryUpgradeHistory();
            $kpiSumHistory->kpi_summary_id = $kpiSummary->id;
            $kpiSumHistory->user_id = $kpiSummary->user_id;
            $kpiSumHistory->department_id = $kpiSummary->department_id;
            $kpiSumHistory->position_id = $kpiSummary->position_id;
            $kpiSumHistory->level_position = $kpiSummary->level_position;
            $kpiSumHistory->from_date = $kpiSummary->from_date;
            $kpiSumHistory->to_date = $kpiSummary->to_date;
            $kpiSumHistory->status = $kpiSummary->status;
            $kpiSumHistory->save();

            //loop kpi
            $sum_proportion = 0;
            foreach ($request->kpi as $e) {
                $sum_proportion += $e['proportion'];

                $kpi = new KPIUpgrade();
                $kpi->kpi_summary_id = $kpiSummary->id;
                $kpi->criteria_name = $e['criteria_name'];
                $kpi->proportion = $e['proportion'];
                $kpi->target_content = $e['target_content'];
                $kpi->save();

                //history kpi
                $kpi_histories = new KPIUpgradeHistory();
                $kpi_histories->kpi_summary_id = $kpiSummary->id;
                $kpi_histories->criteria_name = $e['criteria_name'];
                $kpi_histories->proportion = $e['proportion'];
                $kpi_histories->target_content = $e['target_content'];
                $kpi_histories->save();
            }
            if ($sum_proportion != 100) {
                return response()->json(['message' => 'Bạn đăng ký chưa đúng % tỷ trọng KPI'], 404);
            }
            DB::commit();
            return response()->json(['message' => 'Xử lý thành công']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('KPIController store: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function update(Request $request)
    {
        $request->validate([
            'kpi_summary.from_date' => 'required|date_format:Y-m-d',
            'kpi_summary.to_date' => 'required|date_format:Y-m-d',
            'kpi' => 'required|array',
            'kpi.*.criteria_name' => 'required|max:1000',
            'kpi.*.proportion' => 'required|integer',
        ], [], [
            'kpi_summary.from_date' => 'Ngày bắt đầu',
            'kpi_summary.to_date' => 'Ngày kết thúc',
            'kpi' => 'Tiêu chí KPI',
            'kpi.*.criteria_name' => 'Tên tiêu chí',
            'kpi.*.proportion' => 'Tỷ trọng',
        ]);

        try {
            $model = KPISummaryUpgrade::where('id', $request->kpiSummaryId)
                ->where('user_id', auth()->user()->id)
                ->first();

            $checkPermission = $this->isAllowEdit($model);
            if (!$checkPermission->status) {
                return response()->json(['message' => $checkPermission->msg], $checkPermission->code);
            }

            DB::beginTransaction();
            $kpi_summary = $request->kpi_summary;
            $kpi = $request->kpi;
            $model->from_date = $kpi_summary['from_date'];
            $model->to_date = $kpi_summary['to_date'];
            $model->save();

            # xoá hết các dòng KPI cũ
            KPIUpgrade::where('kpi_summary_id', $model->id)->delete();
            KPIUpgradeHistory::where('kpi_summary_id', $model->id)->delete();

            $sum_proportion = 0;
            foreach ($kpi as $e) {

                $sum_proportion += $e['proportion'];

                $tmpKpi = new KPIUpgrade();
                $tmpKpi->kpi_summary_id = $model->id;
                $tmpKpi->criteria_name = $e['criteria_name'];
                $tmpKpi->proportion = $e['proportion'];
                $tmpKpi->target_content = $e['target_content'];
                $tmpKpi->save();

                $kpiHistory = new KPIUpgradeHistory();
                $kpiHistory->kpi_summary_id = $model->id;
                $kpiHistory->criteria_name = $e['criteria_name'];
                $kpiHistory->proportion = $e['proportion'];
                $kpiHistory->target_content = $e['target_content'];
                $kpiHistory->save();
            }
            if ($sum_proportion != 100) {
                return response()->json(['message' => 'Bạn đăng ký chưa đúng % tỷ trọng KPI'], 404);
            }
            DB::commit();
            return response()->json(['message' => 'Xử lý thành công']);
        } catch (\Exception $e) {
            DB::rollBack();
            dd($e);
            Log::error("KPIController update: " . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function leaderConfirmKPI(Request $request)
    {
        try {
            $model = KPISummaryUpgrade::find($request->kpiSummaryId);
            $checkPermission = $this->isAllowApproveByLeader($model);
            if (!$checkPermission->status) {
                return response()->json(['message' => $checkPermission->msg], $checkPermission->code);
            }
            DB::beginTransaction();
            $model->status = KPISummaryUpgrade::LEADER_CONFIRM_KPI;
            $model->leader_confirm_by = auth()->user()->id;
            $model->save();
            // $this->createKPIHistory($model);
            DB::commit();
            return response()->json(['message' => 'Xử lý thành công']);
        } catch (Exception $e) {
            DB::rollBack();
            Log::error("KPIController leaderConfirmKPI: " . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function leaderReopenKPI(Request $request, $id)
    {
        try {
            $model = KPISummaryUpgrade::find($request->kpiSummaryId);
            $checkPermission = $this->isAllowReopenByLeader($model);
            if (!$checkPermission->status) {
                return response()->json(['message' => $checkPermission->msg], $checkPermission->code);
            }
            DB::beginTransaction();
            $model->status = KPISummaryUpgrade::LEADER_REOPEN_KPI;
            $model->leader_confirm_by = auth()->user()->id;
            $model->save();
            // $this->createKPIHistory($model);
            DB::commit();
            return response()->json(['message' => 'Xử lý thành công']);
        } catch (Exception $e) {
            DB::rollBack();
            Log::error("KPIController leaderConfirmKPI: " . $e->getMessage());
            return response()->json([], 500);
        }
    }

    private function createKPIHistory($kpi_summary)
    {
        KPISummaryUpgradeHistory::create([
            'kpi_summary_id' => $kpi_summary->id,
            'user_id' => $kpi_summary->user_id,
            'department_id' => $kpi_summary->department_id,
            'position_id' => $kpi_summary->position_id,
            'time_range' => $kpi_summary->time_range,
            'level_position' => $kpi_summary->level_position,
            'from_date' => $kpi_summary->from_date,
            'to_date' => $kpi_summary->to_date,
            'status' => $kpi_summary->status,
            'leader_confirm_by' => $kpi_summary->leader_confirm_by,
        ]);
    }

    public function assignKPI($id)
    {
        $kpi_summary = KPISummaryUpgrade::where('user_id', $id)->get();
        $kpi_summary_ids = [];
        foreach ($kpi_summary as $e) {
            array_push($kpi_summary_ids, $e->id);
        }
        $kpi = KPIUpgrade::whereIn('kpi_summary_id', $kpi_summary_ids)->where('group_criteria', true)->get();
        return $kpi;
    }

    private function isAllowEdit($data, $config = null)
    {
        if (!$data) {
            return (object) [
                'status' => false,
                'code' => 404,
                'msg' => 'Không tìm thấy dữ liệu',
            ];
        }

        $config = $config ?: SystemConfig::getKpiAssessmentTime();
        $status = [
            KPISummaryUpgrade::STAFF_REGISTER_KPI,
            KPISummaryUpgrade::LEADER_REOPEN_KPI,
        ];
        if (
            !$config
            || !in_array($data->status, $status)
            || $data->user_id != auth()->user()->id
            || Carbon::now()->gt(Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-') . $config[SystemConfig::EMPLOYEE_REGISTER_KPI] . ' 23:59:59'))
        ) {
            return (object) [
                'status' => false,
                'code' => 404,
                'msg' => ($data->user_id != auth()->user()->id || !in_array($data->status, $status)) ? 'Bạn không có quyền' : 'Đã quá thời gian cho phép',
            ];
        }

        return (object) [
            'status' => true,
            'code' => 200,
            'msg' => 'Allowed',
        ];
    }

    private function isAllowApproveByLeader($data, $config = null)
    {
        if (!$data) {
            return (object) [
                'status' => false,
                'code' => 404,
                'msg' => 'Không tìm thấy dữ liệu',
            ];
        }

        $config = $config ?: SystemConfig::getKpiAssessmentTime();
        $leaderPositions = UserWorking::allActive()
            ->where('user_id', $data->user_id)
            ->pluck('dependent_position_id')
            ->toArray();

        $status = [
            KPISummaryUpgrade::STAFF_REGISTER_KPI,
            KPISummaryUpgrade::LEADER_REOPEN_KPI,
        ];

        if (
            !$config || (auth()->user()->id == $data->user_id)
            || !in_array($data->status, $status)
            || Carbon::now()->gt(Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-') . $config[SystemConfig::EMPLOYEE_REGISTER_KPI] . ' 23:59:59'))
        ) {
            return (object) [
                'status' => false,
                'code' => 404,
                'msg' => Carbon::now()->gt(Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-') . $config[SystemConfig::EMPLOYEE_REGISTER_KPI] . ' 23:59:59')) ? 'Đã quá thời gian cho phép' : 'Bạn không có quyền',
            ];
        }

        $position_id = [];
        foreach (auth()->user()->user_workings as $user_working) {
            array_push($position_id, $user_working->position_id);
        }
        $count_position_ids = count(array_intersect($position_id, $leaderPositions));
        if ($count_position_ids == 0) {
            return (object) [
                'status' => false,
                'code' => 404,
                'msg' => 'Not Allowed',
            ];
        }

        return (object) [
            'status' => true,
            'code' => 200,
            'msg' => 'Allowed',
        ];
    }

    private function isAllowReopenByLeader($data, $config = null)
    {
        if (!$data) {
            return (object) [
                'status' => false,
                'code' => 404,
                'msg' => 'Không tìm thấy dữ liệu',
            ];
        }

        $config = $config ?: SystemConfig::getKpiAssessmentTime();
        $leaderPositions = UserWorking::allActive()
            ->where('user_id', $data->user_id)
            ->pluck('dependent_position_id')
            ->toArray();
        if (
            !$config
            || (auth()->user()->id == $data->user_id)
            || $data->status != KPISummaryUpgrade::LEADER_CONFIRM_KPI
            || Carbon::now()->gt(Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-') . $config[SystemConfig::EMPLOYEE_REGISTER_KPI] . ' 23:59:59'))
        ) {
            return (object) [
                'status' => false,
                'code' => 404,
                'msg' => Carbon::now()->gt(Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-') . $config[SystemConfig::EMPLOYEE_REGISTER_KPI] . ' 23:59:59')) ? 'Đã quá thời gian cho phép' : 'Bạn không có quyền',
            ];
        }

        $position_id = [];
        foreach (auth()->user()->user_workings as $user_working) {
            array_push($position_id, $user_working->position_id);
        }
        $count_position_ids = count(array_intersect($position_id, $leaderPositions));
        if ($count_position_ids == 0) {
            return (object) [
                'status' => false,
                'code' => 404,
                'msg' => 'Not Allowed',
            ];
        }

        return (object) [
            'status' => true,
            'code' => 200,
            'msg' => 'Allowed',
        ];
    }

    public function indexMobile(Request $request)
    {
        $validate = Validator::make($request->all(), [
            'month' => 'required|date_format:m-Y',
        ], [], ['month' => 'Tháng']);

        if ($validate->fails()) {
            return response()->json(['message' => $validate->errors()->first()], 400);
        }
        $department_id = $request->department_id;
        $rank_code = $request->rank_code;
        $date = explode('-', $request->month);
        $month = $date[0];
        $year = $date[1];
        $query = KPISummaryUpgrade::select('id', 'user_id', 'department_id', 'from_date', 'to_date', 'status')->with(['owner', 'department'])
            ->whereMonth('to_date', $month)
            ->whereYear('to_date', $year);

        KPISummaryUpgrade::getConditionList($request, $query, $department_id, $rank_code);
        $dt = $query
            ->orderBy('to_date', 'DESC')
            ->orderBy('user_id')->paginate(20);

        return [
            'data' => $dt->items(),
            'current_page' => $dt->currentPage(),
            'per_page' => 20,
            'total' => $dt->total(),
            'time_config' => SystemConfig::getKpiAssessmentTime(),
            'register_status' => KPISummaryUpgrade::REGISTERED_STATUS,
        ];
    }

    public function detailMobile(Request $request)
    {
        $data = KPISummaryUpgrade::select('id', 'from_date', 'to_date', 'status', 'user_id')->with(['owner', 'kpi' => function ($qr) {
            $qr->select('id', 'kpi_summary_id', 'criteria_name', 'proportion', 'target_content');
        }])
            ->where('id', $request->kpiSummaryId)
            ->first();
        if (!$data) {
            return response()->json(['message' => 'Không tìm thấy dữ liệu'], 404);
        }

        if (
            $data->user_id == auth()->user()->id
            || in_array($data->owner->user->dependent_position_id, KPISummaryUpgrade::getAllPosition())
            || auth()->user()->hasAnyPermission(['approve_kpi', 'kpi_list_management', 'approve_kpi_leader'])
        ) {
            $config = SystemConfig::getKpiAssessmentTime();
            $isAllowEdit = $this->isAllowEdit($data, $config)->status;
            $isAllowReopenByLeader = $this->isAllowReopenByLeader($data, $config)->status;
            $isAllowApproveByLeader = $this->isAllowApproveByLeader($data, $config)->status;

            $permission = [
                'isAllowEdit' => $isAllowEdit,
                'isAllowReopenByLeader' => $isAllowReopenByLeader,
                'isAllowApproveByLeader' => $isAllowApproveByLeader,
            ];

            $data['permissionView'] = $permission;

            return $data = [
                'summaryData' => $data,
            ];
        } else {
            return response()->json(['message' => 'Bạn không có quyền xem KPI của nhân viên này'], 403);
        }
    }

    public function storeMobile(Request $request)
    {
        $request->validate([
            'kpi_summary' => 'required',
            'kpi_summary.from_date' => 'required|date_format:Y-m-d',
            'kpi_summary.to_date' => 'required|date_format:Y-m-d',
            'kpi' => 'required|array',
            'kpi.*.criteria_name' => 'required|max:1000',
            'kpi.*.proportion' => 'required|numeric',
        ], [], [
            'kpi_summary' => 'Thông tin dăng ký KPI',
            'kpi_summary.from_date' => 'Thời gian',
            'kpi_summary.to_date' => 'Thời gian',
            'kpi' => 'Tiêu chí KPI',
            'kpi.*.criteria_name' => 'Tên tiêu chí',
            'kpi.*.proportion' => 'Tỷ trọng',
        ]);
        try {
            $kpi_summary = $request->kpi_summary;
            $kpi_data = KPISummaryUpgrade::where('user_id', auth()->user()->id)
                ->whereMonth('to_date', Carbon::parse($kpi_summary['to_date'])->format('m'))
                ->whereYear('to_date', Carbon::parse($kpi_summary['to_date'])->format('Y'))
                ->first();
            if ($kpi_data) {
                return response()->json(['message' => 'Bạn đã đăng ký KPI tháng này'], 404);
            }

            $config = SystemConfig::getKpiAssessmentTime();
            if (Carbon::now()->gt(Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-') . $config[SystemConfig::EMPLOYEE_REGISTER_KPI] . ' 23:59:59'))) {
                return response()->json(['message' => 'Quá thời gian đăng ký KPI'], 404);
            }
            DB::beginTransaction();

            // store to summary
            $kpiSummary = new KPISummaryUpgrade();
            $kpiSummary->user_id = auth()->user()->id;
            $kpiSummary->department_id = auth()->user()->user->department_id;
            $kpiSummary->position_id = auth()->user()->user->position_id;
            $kpiSummary->level_position = auth()->user()->user->position->is_leader ? KPISummaryUpgrade::LEADER_POSITION : KPISummaryUpgrade::STAFF_POSITION;
            $kpiSummary->from_date = $kpi_summary['from_date'];
            $kpiSummary->to_date = $kpi_summary['to_date'];
            $kpiSummary->status = KPISummaryUpgrade::STAFF_REGISTER_KPI;
            $kpiSummary->save();

            // store to kpi history
            $kpiSumHistory = new KPISummaryUpgradeHistory();
            $kpiSumHistory->kpi_summary_id = $kpiSummary->id;
            $kpiSumHistory->user_id = $kpiSummary->user_id;
            $kpiSumHistory->department_id = $kpiSummary->department_id;
            $kpiSumHistory->position_id = $kpiSummary->position_id;
            $kpiSumHistory->level_position = $kpiSummary->level_position;
            $kpiSumHistory->from_date = $kpiSummary->from_date;
            $kpiSumHistory->to_date = $kpiSummary->to_date;
            $kpiSumHistory->status = $kpiSummary->status;
            $kpiSumHistory->save();

            // loop kpi
            $sum_proportion = 0;
            foreach ($request->kpi as $e) {
                $sum_proportion += $e['proportion'];

                $kpi = new KPIUpgrade();
                $kpi->kpi_summary_id = $kpiSummary->id;
                $kpi->criteria_name = $e['criteria_name'];
                $kpi->proportion = $e['proportion'];
                $kpi->target_content = $e['target_content'];
                $kpi->save();

                // history kpi
                $kpi_histories = new KPIUpgradeHistory();
                $kpi_histories->kpi_summary_id = $kpiSummary->id;
                $kpi_histories->criteria_name = $e['criteria_name'];
                $kpi_histories->proportion = $e['proportion'];
                $kpi_histories->target_content = $e['target_content'];
                $kpi_histories->save();
            }
            if ($sum_proportion != 100) {
                return response()->json(['message' => 'Bạn đăng ký chưa đúng % tỷ trọng KPI'], 404);
            }
            DB::commit();
            return response()->json(['message' => 'Xử lý thành công']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('KPIController storeMobile: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function updateMobile(Request $request)
    {
        try {
            $model = KPISummaryUpgrade::where('id', $request->kpiSummaryId)
                ->where('user_id', auth()->user()->id)
                ->first();

            $checkPermission = $this->isAllowEdit($model);
            if (!$checkPermission->status) {
                return response()->json(['message' => $checkPermission->msg], $checkPermission->code);
            }

            DB::beginTransaction();
            $kpi_summary = $request->kpi_summary;
            $kpi = $request->kpi;
            $model->from_date = $kpi_summary['from_date'];
            $model->to_date = $kpi_summary['to_date'];
            $model->save();

            # xoá hết các dòng KPI cũ
            KPIUpgrade::where('kpi_summary_id', $model->id)->delete();
            KPIUpgradeHistory::where('kpi_summary_id', $model->id)->delete();

            $sum_proportion = 0;
            foreach ($kpi as $e) {

                $sum_proportion += $e['proportion'];

                $tmpKpi = new KPIUpgrade();
                $tmpKpi->kpi_summary_id = $model->id;
                $tmpKpi->criteria_name = $e['criteria_name'];
                $tmpKpi->proportion = $e['proportion'];
                $tmpKpi->target_content = $e['target_content'];
                $tmpKpi->save();

                $kpiHistory = new KPIUpgradeHistory();
                $kpiHistory->kpi_summary_id = $model->id;
                $kpiHistory->criteria_name = $e['criteria_name'];
                $kpiHistory->proportion = $e['proportion'];
                $kpiHistory->target_content = $e['target_content'];
                $kpiHistory->save();
            }
            if ($sum_proportion != 100) {
                return response()->json(['message' => 'Bạn đăng ký chưa đúng % tỷ trọng KPI'], 404);
            }
            DB::commit();
            return response()->json(['message' => 'Xử lý thành công']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("KPIController updateMobile: " . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function leaderConfirmKPIMobile(Request $request)
    {
        try {
            $model = KPISummaryUpgrade::find($request->kpiSummaryId);
            $checkPermission = $this->isAllowApproveByLeader($model);
            if (!$checkPermission->status) {
                return response()->json(['message' => $checkPermission->msg], $checkPermission->code);
            }
            DB::beginTransaction();
            $model->status = KPISummaryUpgrade::LEADER_CONFIRM_KPI;
            $model->leader_confirm_by = auth()->user()->id;
            $model->save();
            // $this->createKPIHistory($model);
            DB::commit();
            return response()->json(['message' => 'Xử lý thành công']);
        } catch (Exception $e) {
            DB::rollBack();
            Log::error("KPIController leaderConfirmKPI: " . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function leaderReopenKPIMobile(Request $request)
    {
        try {
            $model = KPISummaryUpgrade::find($request->kpiSummaryId);
            $checkPermission = $this->isAllowReopenByLeader($model);
            if (!$checkPermission->status) {
                return response()->json(['message' => $checkPermission->msg], $checkPermission->code);
            }
            DB::beginTransaction();
            $model->status = KPISummaryUpgrade::LEADER_REOPEN_KPI;
            $model->leader_confirm_by = auth()->user()->id;
            $model->save();
            // $this->createKPIHistory($model);
            DB::commit();
            return response()->json(['message' => 'Xử lý thành công']);
        } catch (Exception $e) {
            DB::rollBack();
            Log::error("KPIController leaderConfirmKPI: " . $e->getMessage());
            return response()->json([], 500);
        }
    }
}
