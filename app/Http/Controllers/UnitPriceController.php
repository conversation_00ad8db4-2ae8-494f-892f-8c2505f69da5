<?php

namespace App\Http\Controllers;

use App\Models\UnitPrice;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class UnitPriceController extends Controller
{
    public function index(Request $request)
    {
        $data = UnitPrice::select("*", DB::raw("CONCAT(m_unit_prices.price,m_unit_prices.unit,'/',m_unit_prices.frequency,m_unit_prices.type) AS unit_price"))->get();
        return $data;
    }
}
