<?php

namespace App\Http\Controllers;

use App\Jobs\SendEmailInfoNewStaff;
use App\Models\UserWorking;
use Carbon\Carbon;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use App\Models\ConfigEmail;
use App\Models\Response;
use Illuminate\Http\Request;
use Exception;
use Illuminate\Support\Facades\Log;

class HrInfoController extends BaseController
{
    public static function infoNewStaff()
    {
        try {
            $users = UserWorking::active()->with('user')->get();
            $birthday_persons = UserWorking::active()->whereHas('user', function ($query) {
                $query->whereMonth('birth_of_date', Carbon::now()->format('m'));
            })->with('user', 'department', 'position')->get();
            foreach ($users as $e) {
                $content = (object) [];
                $content->receiver = env('APP_ENV') == 'production' ? $e->email : '<EMAIL>';
                $content->receiver_name = $e->name;
                $content->subject = '[VIETEC-QLDN]-CHÀO ĐÓN NHÂN SỰ MỚI';
                $content->message = 'Văn phòng xin giới thiệu nhân sự mới';
                $content->birthday_persons = $birthday_persons;
                $content->cc = [];
                $emailJob = (new SendEmailInfoNewStaff($content))->onQueue('email');
                dispatch($emailJob);
            }
            return Response::formatResponse(config('apicode.SUCCESS'), 1);
        } catch (\Exception $e) {
            return Response::formatResponse(config('apicode.INTERNAL_SERVER_ERROR'), $e->getMessage());
        }
    }

    public function configHappyBirthday(Request $request)
    {
        $request->validate([
            'content' => 'required',
        ], [], [
            'content' => 'Lời chúc'
        ]);
        try {
            DB::beginTransaction();
            if ($request['file']) {
                $image_64 = $request['file']['dataUrl'];

                $extension = explode('/', explode(':', substr($image_64, 0, strpos($image_64, ';')))[1])[1];
                $replace = substr($image_64, 0, strpos($image_64, ',') + 1);
                $image = str_replace($replace, '', $image_64);
                $image = str_replace(' ', '+', $image);
                $imageName = Str::random(10) . '.' . $extension;
                if (!Storage::exists("app/public/background")) {
                    Storage::makeDirectory("app/public/background", 777, true);
                }

                Storage::disk('background')->put($imageName, base64_decode($image));
                $path = 'storage/background/' . $imageName;
            } else {
                $path = $request->path;
            }
            $data = [
                'content' => $request->content,
                'path' => $path
            ];
            if ($request['id']) {
                ConfigEmail::where('id', $request->id)->update($data);
            } else {
                ConfigEmail::create($data);
            }
            DB::commit();
            return response()->json(['message' => 'Xử lý thành công']);
        } catch (Exception $e) {
            Log::error('ERROR:' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function getConfigHappyBirthday()
    {
        $rs = ConfigEmail::first();
        return response()->json($rs);
    }
}
