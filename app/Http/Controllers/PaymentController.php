<?php

namespace App\Http\Controllers;

use App\Models\ContractPaymentHistory;
use Illuminate\Http\Request;
use App\Models\UserWorking;
use App\Models\Department;
use PhpOffice\PhpSpreadsheet\IOFactory;

class PaymentController extends Controller
{
    //list
    public function index(Request $request)
    {
        try {
            $query = ContractPaymentHistory::with('contracts','contracts.clients','contracts.provinceBusinessMarket','contracts.product');
            $this->getCondition($request, $query);
            $sum_rs = with(clone $query)->get();
            $data = $query->paginate(15);
            $sum_contract_value = $sum_rs->sum('current_contract_value');
            $sum_received_money = $sum_rs->sum('current_received_money');
            $data = [
                'data' => $data,
                'sum_contract_value' => $sum_contract_value,
                'sum_received_money' => $sum_received_money,
                'sum_debt' => $sum_contract_value -  $sum_received_money,
            ];
        return response()->json(['data' => $data]);
        } catch (Exception $e) {
            Log::error('ContractController index: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    private function getCondition($request, $query)
    {
        $query->whereHas('contracts', function ($subQuery) use ($request) {

            if (auth()->user()->user && auth()->user()->user->department_id == Department::SALE_DEPARTMENT) {
                if (!auth()->user()->hasAnyPermission(['contract_list_management', 'contract_help_upload'])) {
                    if (auth()->user()->user->position->is_leader == null) {
                        $dependent_position_id = auth()->user()->user->dependent_position_id;
                    } else {
                        $dependent_position_id = auth()->user()->user->position_id;
                    }
                    $leaderId = UserWorking::getAllDependentPositionIdByPosition($dependent_position_id, []);
                    $subQuery->whereIn('position_leader_id', $leaderId);
                }
            }

            $subQuery->when($request->month, function ($subQuery, $month) {
                return $subQuery->whereMonth('sale_date', $month);
            });
            $subQuery->whereHas('clients', function ($sq) use ($request) {
                $sq->when($request->province_id, function ($q, $province_id) {
                    $q->where('province_id', $province_id);
                })->when($request->district_id, function($q, $district_id){
                    $q->where('district_id', $district_id);
                })->when($request->client_code, function($q, $client_code){
                    $q->where('code', $client_code);
                })->when($request->school_name, function($q, $school_name){
                    $q->where('name', $school_name);
                });
            });
            $subQuery->whereHas('product',function ($sq) use ($request){
                $sq->when($request->product_id, function ($q, $product_id) {
                    $q->where('product_id', $product_id);
                });
            });
            $subQuery->when($request->month, function ($q, $month) {
                $q->whereMonth('sale_date', $month);
            });
            $subQuery->when($request->year, function ($q, $year) {
                $q->whereYear('sale_date', $year);
            });
            $subQuery->when($request->team, function ($q, $team) {
                $q->where('team', $team);
            });
            $subQuery->when($request->company, function ($q, $company) {
                $q->where('company', $company);
            });
        });
        $query->when($request->created_at, function ($subQuery, $created_at) {
            return $subQuery->whereDate('created_at', $created_at);
        });
    }

    public function exportPayment(Request $request)
    {
        $inputFileName = 'templates/payment_report.xlsx';
        $fileType = IOFactory::identify($inputFileName);
        $objReader = IOFactory::createReader($fileType);
        $objPHPExcel = $objReader->load($inputFileName);
        $sheet = $objPHPExcel->setActiveSheetIndex(0);
        $row = 3;
        $query = ContractPaymentHistory::with('contracts','contracts.clients','contracts.provinceBusinessMarket','contracts.product');
        $this->getCondition($request, $query);
        $contracts = $query->get();
        $contract_ids = [];
        foreach ($contracts as $k => $e) {
            array_push($contract_ids, $e->id);
            $sheet->setCellValue('A' . $row, $k + 1)->getStyle('A' . $row)->getAlignment()->setHorizontal('center');
            $sheet->setCellValue('B' . $row, $e->contracts->provinceBusinessMarket ? $e->contracts->provinceBusinessMarket->name : '');
            $sheet->setCellValue('C' . $row, $e->contracts->clients->code);
            $sheet->setCellValue('D' . $row, $e->contracts->clients->name);
            $sheet->setCellValue('E' . $row, $e->contracts->product->name);
            $sheet->setCellValue('F' . $row, $e->current_contract_value);
            $sheet->setCellValue('G' . $row, $e->payment_amount);
            $sheet->setCellValue('H' . $row, $e->current_received_money);
            $sheet->setCellValue('I' . $row, date('d-m-Y',strtotime($e->payment_date)));
            $sheet->setCellValue('J' . $row, date('d-m-Y',strtotime($e->sale_real_date)));
            $row++;
        }
        $objWriter = IOFactory::createWriter($objPHPExcel, $fileType);
        try {
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename="Report.xlsx"');
            header('Cache-Control: max-age=0');
            $objWriter->save("php://output");
        } catch (\Exception $exception) {
            DB::rollBack();
            throw $exception;
        }
    }
}
