<?php

namespace App\Http\Controllers;

use App\Jobs\SendWedShareEmail;

class MailController extends Controller
{
    public function test()
    {
        // $content = (object) [];
        // $content->subject = "[VIETEC-QLDN]- Thông báo <PERSON>ác nhận về thứ 4 chia sẻ!";
        // $content->receiver = "<EMAIL>";
        // $content->message = "Anh/chị đã nhận được đề nghị tham gia thứ 4 chia sẻ, vui lòng vào hệ thống để xác nhận hoặc từ chối lời đề nghị";
        // $emailJob = (new SendWedShareEmail($content))->onQueue('emails');
        // dispatch($emailJob);
        // return "Done!";
    }
}
