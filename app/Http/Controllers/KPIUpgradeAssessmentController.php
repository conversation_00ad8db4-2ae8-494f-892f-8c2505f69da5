<?php

namespace App\Http\Controllers;

use App\Jobs\QueueName;
use App\Jobs\StoreHistoryAssessmentKpiJob;
use Illuminate\Http\Request;
use Exception;
use Illuminate\Support\Facades\Log;
use App\Models\KPIUpgrade;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use App\Models\HistoryAssessmentKpi;
use App\Models\KPISummaryUpgrade;
use App\Models\Position;
use App\Models\SystemConfig;
use App\Models\User;
use App\Models\UserWorking;
use App\Notifications\EmailNotification;
use PhpOffice\PhpSpreadsheet\Style;
use App\Services\FirebaseNotification;
use App\Jobs\SendNotifyToAppJob;
use App\Transformer\APIJsonResponse;
use App\Transformer\KPIUpgradeAssessmentTransformer;

class KPIUpgradeAssessmentController extends Controller
{
    private $fromDate;
    private $toDate;

    public function __construct()
    {
        $rs = KPISummaryUpgrade::whereNotIn('status', KPISummaryUpgrade::STATUS_REGISTERED_KPI)
            ->orderBy('to_date', 'DESC')
            ->first();

        if ($rs) {
            $this->fromDate = $rs->from_date;
            $this->toDate = $rs->to_date;
        } else {
            $this->fromDate = date('Y-m-d');
            $this->toDate = date('Y-m-d');
        }
    }

    public function index(Request $request)
    {
        try {
            $query = KPISummaryUpgrade::with(['owner.avatar', 'owner.user', 'department', 'position', 'leader'])->whereHas('owner.user', function ($q) {
                $q->whereNull('end_date')
                    ->orWhere('end_date', '>=', date('Y-m-d'));
            });
            $department_id = $request->department_id;
            $rank_code = $request->rank_code;
            //query theo tung vi tri truyen vao
            KPISummaryUpgrade::getConditionList($request, $query, $department_id, $rank_code);
            $date = explode('-', $request->month);
            $year = $date[1] ?: Carbon::parse($this->toDate)->format('Y');
            $month = $date[0] ?: Carbon::parse($this->toDate)->format('m');
            $rs = $query->whereNotIn('status', [KPISummaryUpgrade::STAFF_REGISTER_KPI, KPISummaryUpgrade::LEADER_REOPEN_KPI])
                ->when($year, function ($q, $year) {
                    $q->whereYear('to_date', $year);
                })
                ->when($month, function ($q, $month) {
                    $q->whereMonth('to_date', $month);
                })
                ->orderBy('to_date', 'DESC')
                ->orderBy('user_id')
                ->paginate(20);

            return response()->json(['data' => $rs]);
        } catch (Exception $e) {
            Log::error("KpiAssessmentController index: " . $e->getMessage());
            return response()->json([], 500);
        }
    }

    //lấy chi tiết đăng ký kpi
    public function detail($kpiSummaryId)
    {
        try {
            $rs = KPISummaryUpgrade::with('owner.user', 'kpi')
                ->whereNotIn('status', KPISummaryUpgrade::STATUS_REGISTERED_KPI)
                ->where('id', $kpiSummaryId)
                ->first();

            if (!$rs) {
                return response()->json(['message' => 'Không tìm thấy dữ liệu'], 404);
            }

            if (
                $rs->user_id == auth()->user()->id
                || in_array($rs->owner->user->dependent_position_id, KPISummaryUpgrade::getAllPosition())
                || auth()->user()->hasAnyPermission(['leader_send_kpi_to_hr', 'approve_kpi', 'kpi_list_management'])
            ) {
                $config = SystemConfig::getKpiAssessmentTime();

                $isAllowStaffSelfAssessment = $this->isAllowStaffSelfAssessment($rs, $config)->status;

                $isAllowAssessmentLeader = $this->isAllowAssessmentLeader($rs, $config)->status;

                $isAllowReopenAssessmentLeader = $this->isAllowReopenAssessmentLeader($rs, $config)->status;

                return response()->json([
                    'criterias' => $rs,
                    'isAllowStaffSelfAssessment' => $isAllowStaffSelfAssessment,
                    'isAllowAssessmentLeader' => $isAllowAssessmentLeader,
                    'isAllowReopenAssessmentLeader' => $isAllowReopenAssessmentLeader,
                ]);
            } else {
                return response()->json([], 403);
            }
        } catch (Exception $e) {
            Log::error("KpiAssessmentController getCriteriaForAssessment: " . $e->getMessage());
            return response()->json([], 500);
        }
    }

    //Nhân viên tự đánh giá kpi
    public function saveStaffSelfAssessment(Request $request)
    {
        $request->validate([
            'kpiSummaryId' => 'required',
            'criteria' => 'required|array',
            'criteria.*.id' => 'required',
            'criteria.*.self_assessment_score' => 'required|integer',
            'criteria.*.self_assessment_result' => 'required',
        ], [], [
            'kpiSummaryId' => 'Thông tin đánh giá',
            'criteria' => 'Tiêu chí',
            'criteria.*.id' => 'Tiêu chí',
            'criteria.*.self_assessment_score' => 'Điểm tự đánh giá',
            'criteria.*.self_assessment_result' => 'Kết quả thực hiện',
        ]);

        try {

            $originalData = KPISummaryUpgrade::with('kpi')
                ->where('id', $request->kpiSummaryId)
                ->where('user_id', auth()->user()->id)
                ->first();

            $checkPermission = $this->isAllowStaffSelfAssessment($originalData);

            if (!$checkPermission->status) {
                return response()->json(['message' => $checkPermission->msg], $checkPermission->code);
            }

            $originalDataHistory = json_encode($originalData);
            DB::beginTransaction();

            $selfScore = 0;
            foreach ($request->criteria as $item) {
                $model = KPIUpgrade::where('kpi_summary_id', $originalData->id)
                    ->where('id', $item['id'])
                    ->first();
                if (!$model) continue;
                $model->self_assessment_score = $item['self_assessment_score'];
                $model->self_assessment_result = $item['self_assessment_result'];
                $model->save();

                $selfScore += $model->proportion * $item['self_assessment_score'] / 100;
            }

            $originalData->self_assessment_score = $selfScore;
            $originalData->status = KPISummaryUpgrade::STAFF_SELF_ASSESSMENT_KPI;
            $originalData->self_assessment_rank = KPISummaryUpgrade::getRank($selfScore);
            $originalData->save();

            DB::commit();

            /**
             * save history
             * */
            $working = auth()->user()->user;
            $newData = json_encode($originalData);
            $jobData = [
                'user_id' => auth()->user()->id,
                'department_id' => $working->department_id,
                'owner' => auth()->user()->id,
                'position_of_owner' => $working->position_id,
                'action_name' => HistoryAssessmentKpi::STAFF_SELF_ASSESSMENT_KPI,
                'original_data' => $originalDataHistory,
                'new_data' => $newData
            ];

            $job = (new StoreHistoryAssessmentKpiJob($jobData))->onQueue(QueueName::STORE_HISTORY_ASSESSMENT_KPI);
            dispatch($job);


            //send email thông báo cho CBLĐ


            $manager = UserWorking::with('user')
                ->allActive()
                ->where('position_id', $request->user()->user->leader->id)
                ->first();
            $user_default = User::where('email', '<EMAIL>')->first();

            if ($manager) {
                $notifier = env('APP_ENV') == 'production' ? $manager->user : $user_default;
                $message = [
                    'subject' => '[' . env('APP_NAME') . '][' . auth()->user()->name . '] - Kết quả tự đánh giá KPI tháng ' . Carbon::parse($originalData->to_date)->format('m/Y'),
                    'greeting' => 'Xin chào, ' . $notifier->name,
                    'body' => 'Bạn vừa nhận được kết quả tự đánh giá KPI của nhân viên: ' . auth()->user()->name,
                    'actionTxt' => 'Xem chi tiết',
                    'actionUrl' => env('APP_URL') . '/upgrade-assessment-kpi/' . $originalData->id,
                ];

                $notifier->notify(new EmailNotification($message));
            }

            return response()->json(['message' => 'Kết quả tự đánh giá của bạn đã được gửi tới CBLĐ']);
        } catch (Exception $e) {
            DB::rollBack();
            Log::error("KpiAssessmentController saveStaffSelfAssessment: " . $e->getMessage());
            return response()->json([], 500);
        }
    }

    // CBLĐ đánh giá nhân viên
    public function saveAssessmentLeader(Request $request)
    {
        $request->validate([
            'kpiSummaryId' => 'required',
            'userId' => 'required',
            'criteria' => 'required|array',
            'criteria.*.id' => 'required',
            'criteria.*.leader_assessment_score' => 'required|integer',
            'criteria.*.leader_assessment_result' => 'required',
        ], [], [
            'kpiSummaryId' => 'Thông tin đánh giá',
            'userId' => 'CBNV',
            'criteria' => 'Tiêu chí',
            'criteria.*.id' => 'Tiêu chí',
            'criteria.*.leader_assessment_score' => 'Điểm CBLĐ đánh giá',
            'criteria.*.leader_assessment_result' => 'KQ thực hiện CBLĐ đánh giá',
        ]);

        try {
            $status = [
                KPISummaryUpgrade::STAFF_SELF_ASSESSMENT_KPI,
                KPISummaryUpgrade::LEADER_REOPEN_SELF_ASSESSMENT_KPI,
            ];

            $originalData = KPISummaryUpgrade::with('owner', 'kpi')
                ->whereIn('status', $status)
                ->where('id', $request->kpiSummaryId)
                ->where('user_id', $request->userId)
                ->first();

            $checkPermission = $this->isAllowAssessmentLeader($originalData);
            if (!$checkPermission->status) {
                return response()->json(['message' => $checkPermission->msg], $checkPermission->code);
            }

            $originalDataHistory = json_encode($originalData);
            DB::beginTransaction();
            $leaderScore = 0;
            foreach ($request->criteria as $item) {

                $model = KPIUpgrade::where('kpi_summary_id', $originalData->id)
                    ->where('id', $item['id'])
                    ->first();
                if (!$model) continue;
                $model->leader_assessment_score = $item['leader_assessment_score'];
                $model->leader_assessment_result = $item['leader_assessment_result'];
                $model->result_final = $item['result_final'];
                $model->leader_note = $item['leader_note'];
                $model->save();

                $leaderScore += $model->proportion * $item['leader_assessment_score'] / 100;
            }

            $originalData->leader_assessment_score = $leaderScore;
            $originalData->status = KPISummaryUpgrade::LEADER_ASSESSMENT_KPI;
            $originalData->leader_assessment_rank = KPISummaryUpgrade::getRank($leaderScore);
            $originalData->result_assessment_rank = KPISummaryUpgrade::getRank($leaderScore);
            $originalData->leader_assessment_by = auth()->user()->id;
            $originalData->save();
            DB::commit();

            /**
             * save history
             * */
            $owner = auth()->user()->user;
            $newData = json_encode($originalData);
            $jobData = [
                'user_id' => $originalData->user_id,
                'department_id' => $owner->department_id,
                'owner' => auth()->user()->id,
                'position_of_owner' => $owner->position_id,
                'action_name' => HistoryAssessmentKpi::LEADER_ASSESSMENT_KPI,
                'original_data' => $originalDataHistory,
                'new_data' => $newData
            ];
            $job = (new StoreHistoryAssessmentKpiJob($jobData))->onQueue(QueueName::STORE_HISTORY_ASSESSMENT_KPI);
            dispatch($job);


            //send email thông báo cho nhân viên
            $user_default = User::where('email', '<EMAIL>')->first();
            $notifier = env('APP_ENV') == 'production' ? $originalData->owner : $user_default;

            $message = [
                'subject' => '[' . env('APP_NAME') . '] - Kết quả CBLĐ đánh giá KPI tháng ' . Carbon::parse($originalData->to_date)->format('m/Y'),
                'greeting' => 'Xin chào, ' . $notifier->name,
                'body' => 'Bạn vừa nhận được kết quả CBLĐ đánh giá KPI tháng ' . Carbon::parse($originalData->to_date)->format('m/Y'),
                'actionTxt' => 'Xem chi tiết',
                'actionUrl' => env('APP_URL') . '/upgrade-assessment-kpi/' . $originalData->id,
            ];

            $notifier->notify(new EmailNotification($message));

            return response()->json(['message' => 'Xử lý thành công']);
        } catch (Exception $e) {
            DB::rollBack();
            Log::error("KpiAssessmentController saveAssessmentLeader: " . $e->getMessage());
            return response()->json([], 500);
        }
    }

    //Leader mở lại tự đánh giá
    public function leaderReopenStaffSelfAssessment(Request $request)
    {
        try {
            $status = [
                KPISummaryUpgrade::STAFF_SELF_ASSESSMENT_KPI,
                KPISummaryUpgrade::LEADER_ASSESSMENT_KPI,
            ];

            $originalData = KPISummaryUpgrade::with('kpi')
                ->whereIn('status', $status)
                ->where('id', $request->kpiSummaryId)
                ->where('user_id', $request->userId)
                ->first();
            $checkPermission = $this->isAllowReopenAssessmentLeader($originalData);
            if (!$checkPermission->status) {
                return response()->json(['message' => $checkPermission->msg], $checkPermission->code);
            }
            $originalDataHistory = json_encode($originalData);

            $originalData->status = KPISummaryUpgrade::LEADER_REOPEN_SELF_ASSESSMENT_KPI;
            $originalData->save();

            /**
             * save history
             * */
            $owner = auth()->user()->user;
            $newData = json_encode($originalData);
            $jobData = [
                'user_id' => $originalData->user_id,
                'department_id' => $owner->department_id,
                'owner' => auth()->user()->id,
                'position_of_owner' => $owner->position_id,
                'action_name' => HistoryAssessmentKpi::LEADER_REOPEN_SELF_ASSESSMENT_KPI,
                'original_data' => $originalDataHistory,
                'new_data' => $newData
            ];
            $job = (new StoreHistoryAssessmentKpiJob($jobData))->onQueue(QueueName::STORE_HISTORY_ASSESSMENT_KPI);
            dispatch($job);

            return response()->json(['message' => 'Xử lý thành công']);
        } catch (Exception $e) {
            DB::rollBack();
            Log::error("KpiAssessmentController leaderReopenStaffSelfAssessment: " . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function exportExcel(Request $request)
    {
        try {
            $query = KPISummaryUpgrade::with(['kpi', 'department', 'position', 'leader'])->whereHas('owner.user', function ($q) {
                $q->whereNull('end_date')
                    ->orWhere('end_date', '>=', date('Y-m-d'));
            });
            $department_id = $request->department_id;
            $rank_code = $request->rank_code;
            //query theo tung vi tri truyen vao
            KPISummaryUpgrade::getConditionList($request, $query, $department_id, $rank_code);
            $date = explode('-', $request->month);
            $year = $date[1] ?: Carbon::parse($this->toDate)->format('Y');
            $month = $date[0] ?: Carbon::parse($this->toDate)->format('m');
            $rs = $query->whereNotIn('status', [KPISummaryUpgrade::STAFF_REGISTER_KPI, KPISummaryUpgrade::LEADER_REOPEN_KPI])
                ->when($year, function ($q, $year) {
                    $q->whereYear('to_date', $year);
                })
                ->when($month, function ($q, $month) {
                    $q->whereMonth('to_date', $month);
                })
                ->orderBy('department_id');
            $kpi_array = with(clone $rs)->get();
            $count_staff_of_department =  with(clone $rs)->selectRaw('count(*) as total_staff, department_id')->groupBy('department_id')->get();
            $count_staff_of_company = with(clone $rs)->count();
            $position_ranks = Position::year(date('Y'))->select('id', 'code', 'name')->groupBy('code')->get();
            $inputFileName = 'templates/kpi_upgrade.xlsx';
            $fileType = \PhpOffice\PhpSpreadsheet\IOFactory::identify($inputFileName);
            $objReader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader($fileType);
            $objPHPExcel = $objReader->load($inputFileName);
            $sheet = $objPHPExcel->setActiveSheetIndex(0);
            $row = 7;

            foreach ($kpi_array as $key => $item) {
                $sheet->setCellValue("A" . $row, $key + 1);
                $sheet->setCellValue("B" . $row, $item->owner->staff_code);
                $sheet->setCellValue("C" . $row, $item->owner->name);
                $sheet->setCellValue("D" . $row, $item->position->code);
                $sheet->setCellValue("E" . $row, $item->department->code);
                $sheet->setCellValue("F" . $row, $item->leader->name);
                $sheet->setCellValue("G" . $row, $item->self_assessment_score . '%');
                $sheet->setCellValue("H" . $row, $item->self_assessment_rank);
                $sheet->setCellValue("I" . $row, $item->leader_assessment_score . '%');
                $sheet->setCellValue("J" . $row, $item->leader_assessment_rank);
                $sheet->setCellValue("K" . $row, $item->result_assessment_rank);
                $row++;
            }
            $sheet->getStyle('A7:K' . ($row - 1))->getBorders()->getAllBorders()->setBorderStyle(Style\Border::BORDER_THIN);

            //tổng hợp theo phòng
            $sheetDepartment = $objPHPExcel->setActiveSheetIndex(1);
            $row = 5;
            $rank_A1 = 0;
            $rank_A = 0;
            $rank_B = 0;
            $rank_C = 0;
            $rank_D = 0;
            $rank_NULL = 0;
            foreach ($count_staff_of_department as $key => $item) {
                $assessment_rank_A1 = [];
                $assessment_rank_A = [];
                $assessment_rank_B = [];
                $assessment_rank_C = [];
                $assessment_rank_D = [];
                $assessment_rank_NULL = [];
                foreach ($kpi_array as $e) {
                    if ($e->department_id == $item->department_id) {
                        if ($e->leader_assessment_rank === "A+") {
                            array_push($assessment_rank_A1, $e);
                        }
                        if ($e->leader_assessment_rank === "A") {
                            array_push($assessment_rank_A, $e);
                        }
                        if ($e->leader_assessment_rank === "B") {
                            array_push($assessment_rank_B, $e);
                        }
                        if ($e->leader_assessment_rank === "C") {
                            array_push($assessment_rank_C, $e);
                        }
                        if ($e->leader_assessment_rank === "D") {
                            array_push($assessment_rank_D, $e);
                        }
                        if ($e->leader_assessment_rank === "") {
                            array_push($assessment_rank_NULL, $e);
                        }
                    }
                }
                $item->assessment_rank_A1 = $assessment_rank_A1;
                $item->assessment_rank_A = $assessment_rank_A;
                $item->assessment_rank_B = $assessment_rank_B;
                $item->assessment_rank_C = $assessment_rank_C;
                $item->assessment_rank_D = $assessment_rank_D;
                $item->assessment_rank_NULL = $assessment_rank_NULL;

                $rank_A1 += count($item->assessment_rank_A1);
                $rank_A += count($item->assessment_rank_A);
                $rank_B += count($item->assessment_rank_B);
                $rank_C += count($item->assessment_rank_C);
                $rank_D += count($item->assessment_rank_D);
                $rank_NULL += count($item->assessment_rank_NULL);

                $sheetDepartment->setCellValue("A" . $row, $key + 1);
                $sheetDepartment->setCellValue("B" . $row, $item->department->code);
                $sheetDepartment->setCellValue("C" . $row, count($item->assessment_rank_A1) / $count_staff_of_company);
                $sheetDepartment->setCellValue("D" . $row, count($item->assessment_rank_A1));
                $sheetDepartment->setCellValue("E" . $row, count($item->assessment_rank_A) / $count_staff_of_company);
                $sheetDepartment->setCellValue("F" . $row, count($item->assessment_rank_A));
                $sheetDepartment->setCellValue("G" . $row, count($item->assessment_rank_B)  / $count_staff_of_company);
                $sheetDepartment->setCellValue("H" . $row, count($item->assessment_rank_B));
                $sheetDepartment->setCellValue("I" . $row, count($item->assessment_rank_C) / $count_staff_of_company);
                $sheetDepartment->setCellValue("J" . $row, count($item->assessment_rank_C));
                $sheetDepartment->setCellValue("K" . $row, count($item->assessment_rank_D) / $count_staff_of_company);
                $sheetDepartment->setCellValue("L" . $row, count($item->assessment_rank_D));
                $sheetDepartment->setCellValue("M" . $row, $item->total_staff / $count_staff_of_company);
                $sheetDepartment->setCellValue("N" . $row, $item->total_staff);

                $sheetDepartment->setCellValue("B" . 5 + $key + 1, "Total");
                $sheetDepartment->setCellValue("C" . 5 + $key + 1, $rank_A1 / $count_staff_of_company);
                $sheetDepartment->setCellValue("D" . 5 + $key + 1, $rank_A1);
                $sheetDepartment->setCellValue("E" . 5 + $key + 1, $rank_A / $count_staff_of_company);
                $sheetDepartment->setCellValue("F" . 5 + $key + 1, $rank_A);
                $sheetDepartment->setCellValue("G" . 5 + $key + 1, $rank_B  / $count_staff_of_company);
                $sheetDepartment->setCellValue("H" . 5 + $key + 1, $rank_B);
                $sheetDepartment->setCellValue("I" . 5 + $key + 1, $rank_C / $count_staff_of_company);
                $sheetDepartment->setCellValue("J" . 5 + $key + 1, $rank_C);
                $sheetDepartment->setCellValue("K" . 5 + $key + 1, $rank_D / $count_staff_of_company);
                $sheetDepartment->setCellValue("L" . 5 + $key + 1, $rank_D);
                $sheetDepartment->setCellValue("M" . 5 + $key + 1, 1);
                $sheetDepartment->setCellValue("N" . 5 + $key + 1, $count_staff_of_company);
                $row++;
            }
            $sheetDepartment->getStyle('A5:N' . ($row))->getBorders()->getAllBorders()->setBorderStyle(Style\Border::BORDER_THIN);
            //tổng hợp theo cấp bậc
            $sheetLevel = $objPHPExcel->setActiveSheetIndex(2);
            $row = 5;
            $position_rank_A1 = 0;
            $position_rank_A = 0;
            $position_rank_B = 0;
            $position_rank_C = 0;
            $position_rank_D = 0;
            $position_rank_NULL = 0;
            foreach ($position_ranks as $key => $item) {
                $assessment_rank_A1 = [];
                $assessment_rank_A = [];
                $assessment_rank_B = [];
                $assessment_rank_C = [];
                $assessment_rank_D = [];
                $assessment_rank_NULL = [];
                foreach ($kpi_array as $e) {
                    if ($e->position->code == $item->code) {
                        if ($e->leader_assessment_rank === "A+") {
                            array_push($assessment_rank_A1, $e);
                        }
                        if ($e->leader_assessment_rank === "A") {
                            array_push($assessment_rank_A, $e);
                        }
                        if ($e->leader_assessment_rank === "B") {
                            array_push($assessment_rank_B, $e);
                        }
                        if ($e->leader_assessment_rank === "C") {
                            array_push($assessment_rank_C, $e);
                        }
                        if ($e->leader_assessment_rank === "D") {
                            array_push($assessment_rank_D, $e);
                        }
                        if ($e->leader_assessment_rank === "") {
                            array_push($assessment_rank_NULL, $e);
                        }
                    }
                }
                $item->assessment_rank_A1 = $assessment_rank_A1;
                $item->assessment_rank_A = $assessment_rank_A;
                $item->assessment_rank_B = $assessment_rank_B;
                $item->assessment_rank_C = $assessment_rank_C;
                $item->assessment_rank_D = $assessment_rank_D;
                $item->assessment_rank_NULL = $assessment_rank_NULL;

                $position_rank_A1 += count($item->assessment_rank_A1);
                $position_rank_B += count($item->assessment_rank_B);
                $position_rank_C += count($item->assessment_rank_C);
                $position_rank_D += count($item->assessment_rank_D);
                $position_rank_NULL += count($item->assessment_rank_NULL);

                $sheetLevel->setCellValue("A" . $row, $key + 1);
                $sheetLevel->setCellValue("B" . $row, $item->code);
                $sheetLevel->setCellValue("C" . $row, count($item->assessment_rank_A1) / $count_staff_of_company);
                $sheetLevel->setCellValue("D" . $row, count($item->assessment_rank_A1));
                $sheetLevel->setCellValue("E" . $row, count($item->assessment_rank_A) / $count_staff_of_company);
                $sheetLevel->setCellValue("F" . $row, count($item->assessment_rank_A));
                $sheetLevel->setCellValue("G" . $row, count($item->assessment_rank_B) / $count_staff_of_company);
                $sheetLevel->setCellValue("H" . $row, count($item->assessment_rank_B));
                $sheetLevel->setCellValue("I" . $row, count($item->assessment_rank_C) / $count_staff_of_company);
                $sheetLevel->setCellValue("J" . $row, count($item->assessment_rank_C));
                $sheetLevel->setCellValue("K" . $row, count($item->assessment_rank_D) / $count_staff_of_company);
                $sheetLevel->setCellValue("L" . $row, count($item->assessment_rank_D));
                $sheetLevel->setCellValue("M" . $row, (count($item->assessment_rank_A1) + count($item->assessment_rank_A) + count($item->assessment_rank_B) + count($item->assessment_rank_C) + count($item->assessment_rank_D) + count($item->assessment_rank_NULL)) / $count_staff_of_company);
                $sheetLevel->setCellValue("N" . $row, count($item->assessment_rank_A1) + count($item->assessment_rank_A) + count($item->assessment_rank_B) + count($item->assessment_rank_C) + count($item->assessment_rank_D) + count($item->assessment_rank_NULL));

                $sheetLevel->setCellValue("B" . 5 + $key + 1, "Total");
                $sheetLevel->setCellValue("C" . 5 + $key + 1, $position_rank_A1 / $count_staff_of_company);
                $sheetLevel->setCellValue("D" . 5 + $key + 1, $position_rank_A1);
                $sheetLevel->setCellValue("E" . 5 + $key + 1, $position_rank_A / $count_staff_of_company);
                $sheetLevel->setCellValue("F" . 5 + $key + 1, $position_rank_A);
                $sheetLevel->setCellValue("G" . 5 + $key + 1, $position_rank_B  / $count_staff_of_company);
                $sheetLevel->setCellValue("H" . 5 + $key + 1, $position_rank_B);
                $sheetLevel->setCellValue("I" . 5 + $key + 1, $position_rank_C / $count_staff_of_company);
                $sheetLevel->setCellValue("J" . 5 + $key + 1, $position_rank_C);
                $sheetLevel->setCellValue("K" . 5 + $key + 1, $position_rank_D / $count_staff_of_company);
                $sheetLevel->setCellValue("L" . 5 + $key + 1, $position_rank_D);
                $sheetLevel->setCellValue("M" . 5 + $key + 1, 1);
                $sheetLevel->setCellValue("N" . 5 + $key + 1, $count_staff_of_company);
                $row++;
            }

            $sheetLevel->getStyle('A5:N' . ($row))->getBorders()->getAllBorders()->setBorderStyle(Style\Border::BORDER_THIN);

            $objWriter = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($objPHPExcel, $fileType);
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename="KPI_' .  $request->month . '.xlsx"');
            header('Cache-Control: max-age=0');
            $objWriter->save("php://output");
        } catch (Exception $e) {
            Log::error("KpiAssessmentController exportExcel: " . $row . $e->getMessage());
            return response()->json([], 500);
        }
    }

    private function isAllowStaffSelfAssessment($data, $config = null)
    {
        if (!$data) {
            return (object) [
                'status' => false,
                'code' => 404,
                'msg' => 'Không tìm thấy dữ liệu',
            ];
        }

        $config = $config ?: SystemConfig::getKpiAssessmentTime();

        if (
            !$config
            || $data->user_id != auth()->user()->id
            || !in_array($data->status, [KPISummaryUpgrade::LEADER_CONFIRM_KPI, KPISummaryUpgrade::LEADER_REOPEN_SELF_ASSESSMENT_KPI])
        ) {
            return (object) [
                'status' => false,
                'code' =>  403,
                'msg' => 'Bạn không có quyền',
            ];
        }


        return (object) [
            'status' => true,
            'code' => 200,
            'msg' => 'Allowed'
        ];
    }

    private function isAllowAssessmentLeader($data, $config = null)
    {
        if (!$data) {
            return (object) [
                'status' => false,
                'code' => 404,
                'msg' => 'Không tìm thấy dữ liệu',
            ];
        }

        $config = $config ?: SystemConfig::getKpiAssessmentTime();

        //lấy leader của nhân viên được CBLĐ đánh giá KPI
        $leaderPositions = UserWorking::allActive()
            ->where('user_id', $data->user_id)
            ->pluck('dependent_position_id')
            ->toArray();
        //lấy ra vị trí chung
        $count = array_intersect($leaderPositions, KPISummaryUpgrade::getAllPosition());

        $status = [
            KPISummaryUpgrade::STAFF_SELF_ASSESSMENT_KPI,
            KPISummaryUpgrade::LEADER_REOPEN_SELF_ASSESSMENT_KPI,
        ];

        if (
            !$config
            || count($count) == 0 || $data->user_id == auth()->user()->id
            || !in_array($data->status, $status)
            || ($data->status == KPISummaryUpgrade::STAFF_SELF_ASSESSMENT_KPI && Carbon::now()->gt(Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-') . $config[SystemConfig::LEADER_ASSESSMENT_KPI] . ' 23:59:59')))
        ) {
            return (object) [
                'status' => false,
                'code' => count($count) == 0 ? 403 : 404,
                'msg' => count($count) == 0 ? 'Bạn không có quyền' : 'Đã quá thời gian cho phép',
            ];
        }

        return (object) [
            'status' => true,
            'code' => 200,
            'msg' => 'Allowed'
        ];
    }

    private function isAllowReopenAssessmentLeader($data, $config = null)
    {
        if (!$data) {
            return (object) [
                'status' => false,
                'code' => 404,
                'msg' => 'Không tìm thấy dữ liệu',
            ];
        }

        $config = $config ?: SystemConfig::getKpiAssessmentTime();
        $leaderPositions = UserWorking::allActive()
            ->where('user_id', $data->user_id)
            ->pluck('dependent_position_id')
            ->toArray();

        //lấy ra vị trí chung
        $count = array_intersect($leaderPositions, KPISummaryUpgrade::getAllPosition());

        $status = [
            KPISummaryUpgrade::STAFF_SELF_ASSESSMENT_KPI,
            KPISummaryUpgrade::LEADER_ASSESSMENT_KPI
        ];

        if (
            !$config
            || count($count) == 0 || $data->user_id == auth()->user()->id
            || !in_array($data->status, $status)

        ) {
            return (object) [
                'status' => false,
                'code' => 403,
                'msg' => 'Bạn không có quyền',
            ];
        }

        return (object) [
            'status' => true,
            'code' => 200,
            'msg' => 'Allowed'
        ];
    }

    public function listMobile(Request $request)
    {
        try {
            $query = KPISummaryUpgrade::with(['owner.user', 'department', 'position', 'leader'])->whereHas('owner.user', function ($q) {
                $q->whereNull('end_date')
                    ->orWhere('end_date', '>=', date('Y-m-d'));
            });
            $department_id = $request->department_id;
            $rank_code = $request->rank_code;
            //query theo tung vi tri truyen vao
            KPISummaryUpgrade::getConditionList($request, $query, $department_id, $rank_code);
            $date = explode('-', $request->month);
            $year = $date[1] ?: Carbon::parse($this->toDate)->format('Y');
            $month = $date[0] ?: Carbon::parse($this->toDate)->format('m');
            $rs = $query->whereNotIn('status', [KPISummaryUpgrade::STAFF_REGISTER_KPI, KPISummaryUpgrade::LEADER_REOPEN_KPI])
                ->when($year, function ($q, $year) {
                    $q->whereYear('to_date', $year);
                })
                ->when($month, function ($q, $month) {
                    $q->whereMonth('to_date', $month);
                })
                ->orderBy('to_date', 'DESC')
                ->orderBy('user_id')
                ->paginate(20);

            $pre_page = $request['pre_page'] ?? 20;

            $option = [
                'current_page' => $rs->currentPage(),
                'per_page' => $pre_page,
                'total' => $rs->total()
            ];

            $rs = (new APIJsonResponse)->responseSuccess((new KPIUpgradeAssessmentTransformer)->transforms($rs), null, $option);
        } catch (\Exception $e) {
            $rs = (new APIJsonResponse)->responseError();
        }

        return $rs;
    }

    public function detailMobile(Request $request)
    {
        try {
            $db = KPISummaryUpgrade::with('owner.user', 'kpi')
                ->whereNotIn('status', KPISummaryUpgrade::STATUS_REGISTERED_KPI)
                ->where('id', $request->id)
                ->first();

            if (!$db) {
                return response()->json(['message' => 'Không tìm thấy dữ liệu'], 404);
            }

            if (
                $db->user_id == auth()->user()->id
                || in_array($db->owner->user->dependent_position_id, KPISummaryUpgrade::getAllPosition())
                || auth()->user()->hasAnyPermission(['leader_send_kpi_to_hr', 'approve_kpi', 'kpi_list_management'])
            ) {
                $config = SystemConfig::getKpiAssessmentTime();
                $isAllowStaffSelfAssessment = $this->isAllowStaffSelfAssessment($db, $config)->status;
                $isAllowAssessmentLeader = $this->isAllowAssessmentLeader($db, $config)->status;
                $isAllowReopenAssessmentLeader = $this->isAllowReopenAssessmentLeader($db, $config)->status;

                $db['isAllowStaffSelfAssessment']    = $isAllowStaffSelfAssessment;
                $db['isAllowAssessmentLeader']       = $isAllowAssessmentLeader;
                $db['isAllowReopenAssessmentLeader'] = $isAllowReopenAssessmentLeader;
            } else {
                return response()->json(['message' => 'Bạn không có quyền xem KPI của nhân viên này'], 403);
            }

            $rs = (new APIJsonResponse)->responseSuccess((new KPIUpgradeAssessmentTransformer)->viewDetail($db));
        } catch (\Exception $e) {
            $rs = (new APIJsonResponse)->responseError();
        }

        return $rs;
    }

    //Nhân viên tự đánh giá kpi
    public function saveStaffSelfAssessmentMobile(Request $request)
    {
        $request->validate([
            'kpiSummaryId' => 'required',
            'criteria' => 'required|array',
            'criteria.*.id' => 'required',
            'criteria.*.self_assessment_score' => 'required|numeric',
            'criteria.*.self_assessment_result' => 'required',
        ], [], [
            'kpiSummaryId' => 'Thông tin đánh giá',
            'criteria' => 'Tiêu chí',
            'criteria.*.id' => 'Tiêu chí',
            'criteria.*.self_assessment_score' => 'Điểm tự đánh giá',
            'criteria.*.self_assessment_result' => 'Kết quả thực hiện',
        ]);

        try {

            $originalData = KPISummaryUpgrade::with('kpi')
                ->where('id', $request->kpiSummaryId)
                ->where('user_id', auth()->user()->id)
                ->first();

            $checkPermission = $this->isAllowStaffSelfAssessment($originalData);

            if (!$checkPermission->status) {
                return response()->json(['message' => $checkPermission->msg], $checkPermission->code);
            }

            $originalDataHistory = json_encode($originalData);
            DB::beginTransaction();

            $selfScore = 0;
            foreach ($request->criteria as $item) {
                $model = KPIUpgrade::where('kpi_summary_id', $originalData->id)
                    ->where('id', $item['id'])
                    ->first();
                if (!$model) continue;
                $model->self_assessment_score = $item['self_assessment_score'];
                $model->self_assessment_result = $item['self_assessment_result'];
                $model->save();

                $selfScore += $model->proportion * $item['self_assessment_score'] / 100;
            }

            $originalData->self_assessment_score = $selfScore;
            $originalData->status = KPISummaryUpgrade::STAFF_SELF_ASSESSMENT_KPI;
            $originalData->self_assessment_rank = KPISummaryUpgrade::getRank($selfScore);
            $originalData->save();

            DB::commit();

            /**
             * save history
             * */
            $working = auth()->user()->user;
            $newData = json_encode($originalData);
            $jobData = [
                'user_id' => auth()->user()->id,
                'department_id' => $working->department_id,
                'owner' => auth()->user()->id,
                'position_of_owner' => $working->position_id,
                'action_name' => HistoryAssessmentKpi::STAFF_SELF_ASSESSMENT_KPI,
                'original_data' => $originalDataHistory,
                'new_data' => $newData
            ];

            $job = (new StoreHistoryAssessmentKpiJob($jobData))->onQueue(QueueName::STORE_HISTORY_ASSESSMENT_KPI);
            dispatch($job);


            //send email và notification thông báo cho CBLĐ

            $manager = UserWorking::with('user')
                ->allActive()
                ->where('position_id', $request->user()->user->leader->id)
                ->first();
            $user_default = User::where('email', '<EMAIL>')->first();

            if ($manager) {
                $notifier = env('APP_ENV') == 'production' ? $manager->user : $user_default;
                $message = [
                    'subject' => '[' . env('APP_NAME') . '][' . auth()->user()->name . '] - Kết quả tự đánh giá KPI tháng ' . Carbon::parse($originalData->to_date)->format('m/Y'),
                    'greeting' => 'Xin chào, ' . $notifier->name,
                    'body' => 'Bạn vừa nhận được kết quả tự đánh giá KPI của nhân viên: ' . auth()->user()->name,
                    'actionTxt' => 'Xem chi tiết',
                    'actionUrl' => env('APP_URL') . '/upgrade-assessment-kpi/' . $originalData->id,
                ];

                $notifier->notify(new EmailNotification($message));

                $messageApp = [
                    'title' => '[' . env('APP_NAME') . '][' . auth()->user()->name . '] - Kết quả tự đánh giá KPI tháng ' . Carbon::parse($originalData->to_date)->format('m/Y'),
                    'link' => env('APP_URL') . '/upgrade-assessment-kpi/' . $originalData->id,
                    'content' => 'Bạn vừa nhận được kết quả tự đánh giá KPI của nhân viên: ' . auth()->user()->name,
                    'option' => 'add',
                ];
                # push notify to app
                dispatch(new SendNotifyToAppJob($notifier, $messageApp, FirebaseNotification::PUSH_TYPE_NOTIFICATION))->onQueue('notify_mobile');
            }

            $rs = (new APIJsonResponse)->responseSuccess();
        } catch (\Exception $e) {
            $rs = (new APIJsonResponse)->responseError();
        }

        return $rs;
    }

    // CBLĐ đánh giá nhân viên
    public function saveAssessmentLeaderMobile(Request $request)
    {
        $request->validate([
            'kpiSummaryId' => 'required',
            'userId' => 'required',
            'criteria' => 'required|array',
            'criteria.*.id' => 'required',
            'criteria.*.leader_assessment_score' => 'required|numeric',
            'criteria.*.leader_assessment_result' => 'required',
        ], [], [
            'kpiSummaryId' => 'Thông tin đánh giá',
            'userId' => 'CBNV',
            'criteria' => 'Tiêu chí',
            'criteria.*.id' => 'Tiêu chí',
            'criteria.*.leader_assessment_score' => 'Điểm CBLĐ đánh giá',
            'criteria.*.leader_assessment_result' => 'KQ thực hiện CBLĐ đánh giá',
        ]);

        try {
            $status = [
                KPISummaryUpgrade::STAFF_SELF_ASSESSMENT_KPI,
                KPISummaryUpgrade::LEADER_REOPEN_SELF_ASSESSMENT_KPI,
            ];

            $originalData = KPISummaryUpgrade::with('owner', 'kpi')
                ->whereIn('status', $status)
                ->where('id', $request->kpiSummaryId)
                ->where('user_id', $request->userId)
                ->first();

            $checkPermission = $this->isAllowAssessmentLeader($originalData);
            if (!$checkPermission->status) {
                return response()->json(['message' => $checkPermission->msg], $checkPermission->code);
            }

            $originalDataHistory = json_encode($originalData);
            DB::beginTransaction();
            $leaderScore = 0;
            foreach ($request->criteria as $item) {

                $model = KPIUpgrade::where('kpi_summary_id', $originalData->id)
                    ->where('id', $item['id'])
                    ->first();
                if (!$model) continue;
                $model->leader_assessment_score = $item['leader_assessment_score'];
                $model->leader_assessment_result = $item['leader_assessment_result'];
                $model->result_final = $item['result_final'];
                $model->leader_note = $item['leader_note'];
                $model->save();

                $leaderScore += $model->proportion * $item['leader_assessment_score'] / 100;
            }

            $originalData->leader_assessment_score = $leaderScore;
            $originalData->status = KPISummaryUpgrade::LEADER_ASSESSMENT_KPI;
            $originalData->leader_assessment_rank = KPISummaryUpgrade::getRank($leaderScore);
            $originalData->result_assessment_rank = KPISummaryUpgrade::getRank($leaderScore);
            $originalData->leader_assessment_by = auth()->user()->id;
            $originalData->save();
            DB::commit();

            /**
             * save history
             * */
            $owner = auth()->user()->user;
            $newData = json_encode($originalData);
            $jobData = [
                'user_id' => $originalData->user_id,
                'department_id' => $owner->department_id,
                'owner' => auth()->user()->id,
                'position_of_owner' => $owner->position_id,
                'action_name' => HistoryAssessmentKpi::LEADER_ASSESSMENT_KPI,
                'original_data' => $originalDataHistory,
                'new_data' => $newData
            ];
            $job = (new StoreHistoryAssessmentKpiJob($jobData))->onQueue(QueueName::STORE_HISTORY_ASSESSMENT_KPI);
            dispatch($job);


            //send email thông báo cho nhân viên
            $user_default = User::where('email', '<EMAIL>')->first();
            $notifier = env('APP_ENV') == 'production' ? $originalData->owner : $user_default;

            $message = [
                'subject' => '[' . env('APP_NAME') . '] - Kết quả CBLĐ đánh giá KPI tháng ' . Carbon::parse($originalData->to_date)->format('m/Y'),
                'greeting' => 'Xin chào, ' . $notifier->name,
                'body' => 'Bạn vừa nhận được kết quả CBLĐ đánh giá KPI tháng ' . Carbon::parse($originalData->to_date)->format('m/Y'),
                'actionTxt' => 'Xem chi tiết',
                'actionUrl' => env('APP_URL') . '/upgrade-assessment-kpi/' . $originalData->id,
            ];

            $notifier->notify(new EmailNotification($message));

            $messageApp = [
                'title' => '[' . env('APP_NAME') . '] - Kết quả CBLĐ đánh giá KPI tháng ' . Carbon::parse($originalData->to_date)->format('m/Y'),
                'link' => env('APP_URL') . '/upgrade-assessment-kpi/' . $originalData->id,
                'content' => 'Bạn vừa nhận được kết quả CBLĐ đánh giá KPI tháng ' . Carbon::parse($originalData->to_date)->format('m/Y'),
                'option' => 'add',
            ];
            # push notify to app
            dispatch(new SendNotifyToAppJob($notifier, $messageApp, FirebaseNotification::PUSH_TYPE_ASSESSMENT_KPT))->onQueue('notify_mobile');

            $rs = (new APIJsonResponse)->responseSuccess();
        } catch (\Exception $e) {
            $rs = (new APIJsonResponse)->responseError();
        }

        return $rs;
    }

    //Leader mở lại tự đánh giá
    public function leaderReopenStaffSelfAssessmentMobile(Request $request)
    {
        try {
            $status = [
                KPISummaryUpgrade::STAFF_SELF_ASSESSMENT_KPI,
                KPISummaryUpgrade::LEADER_ASSESSMENT_KPI,
            ];

            $originalData = KPISummaryUpgrade::with('kpi')
                ->whereIn('status', $status)
                ->where('id', $request->kpiSummaryId)
                ->where('user_id', $request->userId)
                ->first();
            $checkPermission = $this->isAllowReopenAssessmentLeader($originalData);
            if (!$checkPermission->status) {
                return response()->json(['message' => $checkPermission->msg], $checkPermission->code);
            }
            $originalDataHistory = json_encode($originalData);

            $originalData->status = KPISummaryUpgrade::LEADER_REOPEN_SELF_ASSESSMENT_KPI;
            $originalData->save();

            /**
             * save history
             * */
            $owner = auth()->user()->user;
            $newData = json_encode($originalData);
            $jobData = [
                'user_id' => $originalData->user_id,
                'department_id' => $owner->department_id,
                'owner' => auth()->user()->id,
                'position_of_owner' => $owner->position_id,
                'action_name' => HistoryAssessmentKpi::LEADER_REOPEN_SELF_ASSESSMENT_KPI,
                'original_data' => $originalDataHistory,
                'new_data' => $newData
            ];
            $job = (new StoreHistoryAssessmentKpiJob($jobData))->onQueue(QueueName::STORE_HISTORY_ASSESSMENT_KPI);
            dispatch($job);

            $rs = (new APIJsonResponse)->responseSuccess();
        } catch (\Exception $e) {
            $rs = (new APIJsonResponse)->responseError();
        }

        return $rs;
    }
}
