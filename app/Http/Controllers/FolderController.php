<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Folder;
use App\Models\FolderPermission;
use App\Models\SubFolder;
use App\Models\Response;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use File;

class FolderController extends Controller
{
    public function index()
    {
        $data = [
            'folders' =>  Folder::all(),
            'sub_folders' => DB::table('sub_folders')->whereNull('folder_id')->whereNull('deleted_at')
                ->get(),
            'users' => $this->getUsers(),
            'permission_of_me' => $this->getPermission()
        ];
        return Response::formatResponse(config('apicode.SUCCESS'), $data);
    }

    public function folderTrashList()
    {
        $data = [
            'folder_trash' => DB::table('folders')
                ->whereNotNull('deleted_at')
                ->get(),
            'sub_folder_trash' => DB::table('sub_folders')->whereNull('folder_id')->whereNotNull('deleted_at')->get(),
            'permission_of_me' => $this->getPermission()
        ];
        return Response::formatResponse(config('apicode.SUCCESS'), $data);
    }

    public function createFolder(Request $request)
    {
        try {
            $data = [
                'name' => $request->folder_name,
                'created_by' => auth()->user()->id
            ];
            Folder::create($data);
            return Response::formatResponse(config('apicode.SUCCESS'), $data);
        } catch (\Exception $e) {
            return Response::formatResponse(config('apicode.INTERNAL_SERVER_ERROR'), $e->getMessage());
        }
    }

    public function uploadMultiFolder(Request $request)
    {
        try {
            $files = $request->file('files');
            if ($request->hasFile('files')) {
                foreach ($files as $file) {
                    $file_name = $file->getClientOriginalName();
                    $file->storeAs('public/attachments', $file_name);
                    SubFolder::create([
                        'name' => $file_name,
                        'path' => 'attachments/' . $file_name,
                    ]);
                }
            }
            return Response::formatResponse(config('apicode.SUCCESS'), 1);
        } catch (\Exception $e) {
            return Response::formatResponse(config('apicode.INTERNAL_SERVER_ERROR'), $e->getMessage());
        }
    }

    public function deleteFile($id)
    {
        try {
            $file = SubFolder::where('id', $id)->delete();
            return Response::formatResponse(config('apicode.SUCCESS'), $file);
        } catch (\Exception $e) {
            return Response::formatResponse(config('apicode.INTERNAL_SERVER_ERROR'), $e->getMessage());
        }
    }

    public function deleteFolder($id)
    {
        try {
            //xóa tất cả các quyền xem file cần xóa trong bảng folder_permissions
            $folder_permissions = FolderPermission::where('folder_id', $id)->get();
            foreach ($folder_permissions as $permission) {
                $permission->delete();
            }
            //xóa file trong bảng folders và sub_folders
            $file = Folder::where('id', $id)->first();
            $file->delete();
            $folder = SubFolder::where('folder_id', $id)->get();
            foreach ($folder as $file) {
                $file->delete();
            }
            return Response::formatResponse(config('apicode.SUCCESS'), $file);
        } catch (\Exception $e) {
            return Response::formatResponse(config('apicode.INTERNAL_SERVER_ERROR'), $e->getMessage());
        }
    }

    public function downloadFile($id)
    {
        $data = SubFolder::where('id', $id)->first();
        $file_path = $data->path;
        $file = public_path('/storage/' . $file_path);
        $headers = array('Content-Type: application/pdf',);
        return response()->download($file, 'info.pdf', $headers);
    }

    public function downloadFolder($id)
    {
        $file = Folder::where('id', $id)->first();
        $zip = new \ZipArchive();
        $file_name_zip = $file->name . '.zip';
        if ($zip->open(public_path('/storage/attachments/' . $file_name_zip), \ZipArchive::CREATE) === TRUE) {
            $files = File::files(public_path('/storage/attachments/' . $id));
            foreach ($files as $key => $file) {
                $relativeName = basename($file);
                $zip->addFile($file, $relativeName);
            }
            $zip->close();
        }
        return response()->download(public_path('/storage/attachments/' . $file_name_zip));
    }

    public function detailFolder($id)
    {
        $data = [
            'sub_folders' => SubFolder::where('folder_id', $id)->get(),
            'users' => $this->getUsers(),
            'permission_of_me' => $this->getPermission()
        ];
        return Response::formatResponse(config('apicode.SUCCESS'), $data);
    }

    public function uploadMultiFolderDetail(Request $request, $id)
    {
        try {
            $files = $request->file('files');
            if ($request->hasFile('files')) {
                foreach ($files as $file) {
                    $file_name = $file->getClientOriginalName();
                    $file->storeAs('public/attachments/' . $id, $file_name);
                    SubFolder::create([
                        'name' => $file_name,
                        'path' => 'attachments/' . $id . '/' . $file_name,
                        'folder_id' => $id,
                        'created_by' => auth()->user()->id
                    ]);
                }
            }
            return Response::formatResponse(config('apicode.SUCCESS'), $id);
        } catch (\Exception $e) {
            return Response::formatResponse(config('apicode.INTERNAL_SERVER_ERROR'), $e->getMessage());
        }
    }

    public function restore(Request $request, $id)
    {
        try {
            $data = $request->all();
            if (!$data['path']) {
                Folder::withTrashed()->where('id', $id)->restore();
                SubFolder::withTrashed()->where('folder_id', $id)->restore();
            } else {
                SubFolder::withTrashed()->where('id', $id)->restore();
            }
            return Response::formatResponse(config('apicode.SUCCESS'), $id);
        } catch (\Exception $e) {
            return Response::formatResponse(config('apicode.INTERNAL_SERVER_ERROR'), $e->getMessage());
        }
    }

    public function shareFolder(Request $request)
    {
        try {
            $user_ids = $request->user_ids;
            $folder_id = $request->folder_share;
            $permissions = $request->permissions;
            $array_data = [];

            foreach ($user_ids as $user) {
                foreach ($permissions as $permission) {
                    $data = [
                        'user_id' => $user['id'],
                        'created_by' => auth()->user()->id,
                        'folder_id' => $folder_id['id'],
                        'folder_type' => isset($folder_id['path']) ? 'file' : 'folder',
                        'permission' => $permission['permission']
                    ];
                    array_push($array_data, $data);
                }
            }
            FolderPermission::insert($array_data);
            return Response::formatResponse(config('apicode.SUCCESS'), $array_data);
        } catch (\Exception $e) {
            return Response::formatResponse(config('apicode.INTERNAL_SERVER_ERROR'), $e->getMessage());
        }
    }
    public function getPermission()
    {
        $permission_of_me = FolderPermission::where('user_id', auth()->user()->id)->with('folder')->get();
        return  $permission_of_me;
    }

    public function getUsers()
    {
        $users =  User::where('id', '<>', '1')->whereHas('endOfWorking', function ($query) {
            $query->active();
        })->get();
        return $users;
    }
}
