<?php

namespace App\Http\Controllers;

use App\Models\FunctionsManager;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class FunctionsManagerController extends Controller
{
    public function getFunctionsActiveMobile(Request $request)
    {
        try {
            $rs = FunctionsManager::select('id', 'title', 'redirect', 'icon', 'parent_id')
                ->with('children')
                ->where('type', 'mobile')
                ->whereNull('parent_id')
                ->get();
            return  response()->json($rs);
        } catch (\Exception $e) {
            Log::error("FunctionsManagerController getFunctionsActive: " . $e->getMessage());
            return response()->json([], 500);
        }
    }
}
