<?php

namespace App\Builder;

use App\Models\User;

class NotificationConfig
{
    // message
    private array $msg;

    // target push
    private User $user;

    //notification mobile type
    private int $notificationType;

    // notification callable
    // return Notification
    private mixed $notification;

    // event callable
    // return ShouldBroadcast
    private mixed $broadcast;

    // email contents
    private mixed $contentMails;

    public function __construct()
    {
    }

    /**
     * @param User $user
     */
    public function setUser(User &$user): void
    {
        $this->user = $user;
    }

    /**
     * @param array $msg
     */
    public function setMsg(array $msg): void
    {
        $this->msg = $msg;
    }

    /**
     * @param mixed $notification
     */
    public function setNotification($notification): void
    {
        $this->notification = $notification;
    }

    /**
     * @return mixed
     */
    public function getNotification()
    {
        return $this->notification;
    }


    /**
     * @param mixed $broadcast
     */
    public function setBroadcast($broadcast): void
    {
        $this->broadcast = $broadcast;
    }

    /**
     * @return mixed
     */
    public function getBroadcast()
    {
        return $this->broadcast;
    }

    /**
     * @param int $notificationType
     */
    public function setNotificationType(int $notificationType): void
    {
        $this->notificationType = $notificationType;
    }

    /**
     * @return array
     */
    public function getMsg(): array
    {
        return $this->msg;
    }

    /**
     * @return int
     */
    public function getNotificationType(): int
    {
        return $this->notificationType;
    }

    /**
     * @return User
     */
    public function getUser(): User
    {
        return $this->user;
    }

    /**
     * @return mixed
     */
    public function getContentMails()
    {
        return $this->contentMails;
    }

    /**
     * @param mixed $contentMails
     */
    public function setContentMails($contentMails): void
    {
        $this->contentMails = $contentMails;
    }
}
