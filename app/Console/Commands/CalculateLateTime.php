<?php

namespace App\Console\Commands;


use App\Models\UserWorking;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use App\Jobs\SendEmailInfoLateTime;
use App\Models\InOutGeneral;
use App\Models\SystemConfig;

class CalculateLateTime extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'calculate_late_time';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Calculate Late Time';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try {
            $userLists = UserWorking::notAdmin()
                ->allActive()
                ->with([
                    'user:id,name,email,full_time_sheet',
                ])
                ->orderBy('user_id')
                ->groupBy('user_id')
                ->get();
            $month = date_format(date_create(date('Y-m-d')), 'Y-m');
            $data = InOutGeneral::where('date', 'like', $month . '%')->get();
            $dataInOut = vietec_convert_array_to_map($data, 'user_id', true);
            foreach ($userLists as $item) {
                $tmpDataInOut = $dataInOut[$item->user_id] ?? [];
                if (!$tmpDataInOut) {
                    continue;
                }

                if ($item->user->full_time_sheet) {
                    continue;
                }

                $late_minute = [];
                $early_minute = [];
                foreach ($tmpDataInOut as $tmpItem) {
                    $timeIn = Carbon::createFromFormat('H:i:s', "08:10:00");
                    $timeInAfternoon = Carbon::createFromFormat('H:i:s', "13:00:00");
                    $checkIn = Carbon::createFromFormat('H:i:s', date_format(date_create($tmpItem->check_in), 'H:i:s'));

                    $timeOut = Carbon::createFromFormat('H:i:s', "17:00:00");
                    $timeOutMorning = Carbon::createFromFormat('H:i:s', "11:59:59");
                    $checkOut = Carbon::createFromFormat('H:i:s', date_format(date_create($tmpItem->check_out), 'H:i:s'));

                    if ($checkIn->greaterThan($timeIn)) {
                        $endTime = Carbon::createFromTime($checkIn->hour, $checkIn->minute, $checkIn->second);
                        if ($checkIn->greaterThan($timeInAfternoon)) {
                            $startTime = Carbon::createFromTime($timeInAfternoon->hour, $timeInAfternoon->minute, $timeInAfternoon->second);
                            array_push($late_minute, $startTime->diffInMinutes($endTime));
                        } else {
                            $startTime = Carbon::createFromTime($timeIn->hour, $timeIn->minute, $timeIn->second);
                            array_push($late_minute, $startTime->diffInMinutes($endTime));
                        }
                    }

                    if ($checkOut->lessThan($timeOut)) {
                        $startTime = Carbon::createFromTime($checkOut->hour, $checkOut->minute, $checkOut->second);
                        if ($checkOut->lessThan($timeOutMorning)) {
                            $endTime = Carbon::createFromTime($timeOutMorning->hour, $timeOutMorning->minute, $timeOutMorning->second);
                            array_push($early_minute, $startTime->diffInMinutes($endTime));
                        } else {
                            $endTime = Carbon::createFromTime($timeOut->hour, $timeOut->minute, $timeOut->second);
                            array_push($early_minute, $startTime->diffInMinutes($endTime));
                        }
                    }
                }
                $email_vp = $item->user->email;
                $email_vp = SystemConfig::where('type', SystemConfig::EMAIL_NOTIFICATION_HR)->first();
                $receiver = explode(',', $email_vp->content);
                $cc = array_shift($receiver);
                if (array_sum($late_minute) > 0 || array_sum($early_minute) > 0) {
                    $content = (object) [];
                    $content->receiver = env('APP_ENV') == 'production' ? $receiver : '<EMAIL>';
                    $content->cc = env('APP_ENV') == 'production' ? $cc : ['<EMAIL>', '<EMAIL>'];
                    $content->subject = '[VIETEC-QLDN]-NHẮC NHỞ QUY ĐịNH THỜI GIAN LÀM VIỆC';
                    $content->name = $item->user->name;
                    $content->month = date_format(date_create($month), 'm-Y');
                    $content->late_minute = array_sum($late_minute);
                    $content->early_minute = array_sum($early_minute);
                    $emailJob = (new SendEmailInfoLateTime($content))->onQueue('email');
                    dispatch($emailJob);
                }
            }
            echo "done nhe! \n";
        } catch (\Exception $e) {
            Log::error('Calculate Late Time Job: ' . $e->getMessage());
        }
    }
}
