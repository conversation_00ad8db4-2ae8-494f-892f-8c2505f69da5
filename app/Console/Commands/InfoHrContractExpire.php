<?php

namespace App\Console\Commands;


use App\Models\UserWorking;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use App\Jobs\SendEmailInfoContractExpire;
use App\Models\SystemConfig;

class InfoHrContractExpire extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'info-hr-staff-contract-expire';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Staff contract expire';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try {
            $config = SystemConfig::where('type', SystemConfig::EMAIL_NOTIFICATION_HR)->first();
            $receiver = explode(',', $config->content);
            if (count($receiver) == 0) {
                echo "khong ton tai nguoi nhan! \n";
                die();
            }
            $first_element = array_shift($receiver);

            $array_user = UserWorking::where('is_sub_position', false)
                ->where('status', 1)
                // ->where(function ($q) {
                //     $q->where(function ($sq) {
                //         $sq->where('end_date', Carbon::now()->addDays(14)->format('Y-m-d'))
                //             ->whereIn('employment_contract_id', [
                //                 EmploymentContract::employment_contract_TV,
                //                 EmploymentContract::employment_contract_HV,
                //                 EmploymentContract::employment_contract_TTS,
                //             ]);
                //     })->orWhere(function ($sq) {
                //         $sq->where('end_date', Carbon::now()->addDays(30)->format('Y-m-d'))
                //             ->whereIn('employment_contract_id', [
                //                 EmploymentContract::employment_contract_XDTH1,
                //                 EmploymentContract::employment_contract_XDTH2,
                //                 EmploymentContract::employment_contract_XDTH3,
                //             ]);
                //     });
                // })
                ->where('end_date', Carbon::now()->addDays(7)->format('Y-m-d'))
                ->with('user', 'employmentContract')
                ->get();

            if (count($array_user) == 0) {
                echo "khong co hop dong sap het han! \n";
                die();
            }
            $content = (object) [];
            $content->receiver = env('APP_ENV') == 'production' ? $first_element : '<EMAIL>';
            $content->subject = '[VIETEC-QLDN]-THÔNG BÁO HỢP ĐỒNG NHÂN SỰ SẮP HẾT HẠN';
            $content->array = $array_user;
            $content->cc = env('APP_ENV') == 'production' ? $receiver : ['<EMAIL>'];
            $emailJob = (new SendEmailInfoContractExpire($content))->onQueue('email');
            dispatch($emailJob);
            echo "done nhe! \n";
        } catch (\Exception $e) {
            Log::error('CalculateAbsenceYear Job: ' . $e->getMessage());
        }
    }
}
