<?php

namespace App\Console\Commands;

use App\Models\UserWorking;
use App\Models\PersonInOut;
use App\Models\InOutGeneral;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class InOutGeneralCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'in_out_general {day}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'In Out General';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try {

            $day = $this->argument('day');
            
            if ($day == 'now') {
                $date = date('Y-m-d');
            } else {
                $date = date("Y-m-{$day}");
            }

            $limCheckIn = strtotime("{$date} 09:00:00") * 1000;
            $limCheckOut = strtotime("{$date} 16:00:00") * 1000;
            
            # danh sách nhân sự chính thức đang active hiện tại
            $userLists = UserWorking::notAdmin()->active()->with('user:id,staff_code,full_time_sheet')->groupBy('user_id')->get();
            foreach ($userLists as $user) {

                $aliasID = $user->user->staff_code;
                $userFullTimeSheet = $user->user->full_time_sheet ?? 0;

                if (!$userFullTimeSheet) {
                    # chỉ tính toán đối với các vị trí cần chấm công

                    $data = PersonInOut::select(DB::raw('MIN(`time`) checkin, MAX(`time`) checkout, aliasID, date'))
                        ->where('aliasID', $aliasID)
                        ->where('date', 'LIKE', $date . '%')
                        ->where('create_by', PersonInOut::CREARE_BY_CAMAI)
                        ->first();

                    if ($data && $data->checkin && $data->checkout) {

                        # xoá bản ghi nếu tồn tại rồi thêm mới lại
                        InOutGeneral::where('user_id', $user->user_id)->where('date', $date)->delete();
                        
                        $model = new InOutGeneral();
                        $model->user_id = $user->user_id;
                        $model->department_id = $user->department_id;
                        $model->position_id = $user->position_id;
                        $model->dependent_position_id = $user->dependent_position_id;
                        $model->employment_contract_id = $user->employment_contract_id;
                        $model->date = $date;
                        $model->check_in = date('Y-m-d H:i:s', ($data->checkin/1000));
                        $model->check_out = date('Y-m-d H:i:s', ($data->checkout/1000));
                        $model->status = ($data->checkin <= $limCheckIn && $limCheckOut <=  $data->checkout) ? InOutGeneral::STATUS_TRUE : InOutGeneral::STATUS_FALSE;
                        $model->save();
                        
                    }
                }
            }

            echo "done nhe! \n";
        } catch (\Exception $e) {

            Log::error('InOutGeneral Job: ' . $e->getMessage());
        }
    }
}
