<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Anik\Amqp\ConsumableMessage;
use Anik\Laravel\Amqp\Facades\Amqp;
use App\Models\User;
use App\Notifications\UserNotification;

class ListenerNotificationMessageCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'listener:notifications {system}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        Amqp::connection($this->argument('system'))->consume(function(ConsumableMessage $message) {
            $row = json_decode($message->getMessageBody(), true);
            
            if(isset($row['user_id'])){
                $user = User::where('id', $row['user_id'])->first();
                if($user != null){
                    dump($row);
                    $user->notify((new UserNotification($user, $row))->onQueue(env('QUEUE_NAME_PREFIX').'_user_notifications'));
                }
            }

            $message->ack();
        });
    }
}
