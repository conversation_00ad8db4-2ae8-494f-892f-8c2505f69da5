<?php

namespace App\Console\Commands;

use App\Models\BusinessTrip;
use App\Models\InOutsOnline;
use App\Models\PersonInOut;
use App\Models\AbsenceLetter;
use App\Models\User;
use App\Models\UserWorking;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class CalculateTimeSheetRD extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'calculate_timesheet_rd {option} {day}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Calculate Timesheet R&D Department';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try {

            $option = $this->argument('option');
            $day = $this->argument('day') == 'now' ? date('d') : $this->argument('day');

            $date = date('Y-m-' . $day);
            $month = date('Y-m');
            $minCheckIn = strtotime("{$date} 08:10:00");
            $maxCheckIn = strtotime("{$date} 08:58:00");
            $minCheckOut = strtotime("{$date} 17:10:00");
            $maxCheckOut = strtotime("{$date} 18:30:00");

            // department_id = 8 là phòng phát triển sản phẩm
            $departmentId = 8;
            $teamUserID = UserWorking::active()->department($departmentId)->pluck('user_id')->toArray();
            // $teamUserID = [4, 5, 6, 9, 10, 39, 74, 97, 109];
            $teamUserID = [39]; // datnt

            $userList = User::select('id', 'staff_code')->whereIn('id', $teamUserID)->with('hanet')->get()->toArray();
            foreach ($userList as $user) {

                # check làm việc online
                $online = InOutsOnline::onlyMe($user['id'])->date($date)->count();
                # check đi công tác
                $businessTrip = BusinessTrip::onlyMe($user['id'])->betweenDate($date)->count();
                # check xin nghỉ phép
                $absenceLetter = AbsenceLetter::onlyMe($user['id'])
                    ->fromDate($month)
                    ->where('from_time', 1)
                    ->where('to_time', 2)
                    ->where('from_date', '<=', $date)
                    ->where('to_date', '>=', $date)
                    ->count();

                # nếu online, đi công tác hoặc xin nghỉ phép vì lý do nào đó thì ko cần checkInOut bằng camAI nữa
                if ($online || $businessTrip || $absenceLetter) {
                    continue;
                }

                if ($user && $user['hanet'] && $user['hanet']['personID']) {

                    if ($option == 'checkin') {
                        $rand = random_int($minCheckIn, $maxCheckIn);
                    } else {
                        $rand = random_int($minCheckOut, $maxCheckOut);
                    }
                    $tmpTime = $rand * 1000;
                    $tmpDate = date("Y-m-d H:i:s", ($rand));

                    $rowPersonInOut = new PersonInOut();
                    $rowPersonInOut->date = $tmpDate;
                    $rowPersonInOut->personTitle = $user['hanet']['title'];
                    $rowPersonInOut->action_type = 'update';
                    $rowPersonInOut->detected_image_url = null;
                    $rowPersonInOut->placeID = '10818';
                    $rowPersonInOut->deviceID = 'C21024B545';
                    $rowPersonInOut->personName = $user['hanet']['name'];
                    $rowPersonInOut->aliasID = $user['hanet']['aliasID'];
                    $rowPersonInOut->data_type = 'log';
                    $rowPersonInOut->personID = $user['hanet']['personID'];
                    $rowPersonInOut->time = $tmpTime;
                    $rowPersonInOut->personType = 0;
                    $rowPersonInOut->placeName = 'Công ty Vietec';
                    $rowPersonInOut->hash = 'hash';
                    $rowPersonInOut->mask = -1;
                    $rowPersonInOut->deviceName = 'Thử nghiệm';
                    $rowPersonInOut->create_by = 1;
                    $rowPersonInOut->save();
                }
            }

            echo "done nhe! \n";
        } catch (\Exception $e) {
            Log::error('CalculateTimeSheetRD Job: ' . $e->getMessage());
        }
    }
}
