<?php

namespace App\Console\Commands;

use Carbon\Carbon;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class CreateTableByYearCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'create_table_by_year';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create Table By Year';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $tables = [
            'm_departments',
            'm_positions',
            // 'm_product_ranks',
            'team_sale_areas',
            'team_sale_area_history',
            // 'quarterly_goals'
        ];

        $now = Carbon::now();
        if ($now->endOfYear()->format('Y-m-d') != $now->format('Y-m-d')) {
            return;
        }

        $nowYear = $now->format('Y');
        $nextYear = $now->addYears(1)->format('Y');
        $sql = "";
        DB::beginTransaction();
        try {
            foreach ($tables as $table) {
                $newTable = $table . "_" . $nextYear;
                $oldTable = $table . "_" . $nowYear;
                $dayUpdated = $now->addDays(1)->format('Y-m-d');
                $sql = "CREATE TABLE IF NOT EXISTS " . $newTable . " LIKE " . $oldTable . ";";
                DB::statement($sql);
                $sql = "INSERT INTO " . $newTable . " SELECT * FROM " . $oldTable . ";";
                DB::statement($sql);
                $sql = "UPDATE " . $newTable . " SET created_at='" . $dayUpdated . "', updated_at='" . $dayUpdated . "';";
                DB::statement($sql);
            }

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
        }
    }
}
