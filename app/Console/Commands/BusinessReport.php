<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use App\Models\BusinessPlan;
use App\Builder\NotificationBuilder;
use App\Builder\NotificationConfig;
use App\Models\User;

class BusinessReport extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'business_report';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Business Report';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try {
            $data = BusinessPlan::where('status', BusinessPlan::PLAN_APPROVED)->whereDate('to_date', Carbon::now()->subDays(3))->get();
            foreach ($data as $e) {
                $user = User::select('name', 'email')->where('id', $e->created_by)->first();
                $config = new NotificationConfig();
                $config->setUser($user);
                $contentMail = (object)[];
                $contentMail->subject = "[VIETEC-QLDN]- Thông báo cập nhật báo cáo công tác";
                $contentMail->receiver = env('APP_ENV') == 'production' ? $user->email : '<EMAIL>';
                $contentMail->receiver_name = $user->name;
                $contentMail->message = 'Anh/chị vui lòng cập nhật báo cáo công tác Từ ngày ' . Carbon::parse($e->from_date)->format('d-m-Y') . ' Đến ngày ' . Carbon::parse($e->to_date)->format('d-m-Y');
                $config->setContentMails([$contentMail]);
                NotificationBuilder::getBuilder()->sendMessage($config, [NotificationBuilder::EMAIL]);
            }
            echo "done nhe! \n";
        } catch (\Exception $e) {
            Log::error('Business Report Job: ' . $e->getMessage());
        }
    }
}
