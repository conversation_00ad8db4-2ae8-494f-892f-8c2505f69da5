<?php

namespace App\Console\Commands;

use App\Models\Client;
use App\Models\Contract;
use App\Models\School;
use App\Models\User;
use App\Models\UserWorking;
use Illuminate\Support\Facades\Log;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class ConvertLicense extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'convert_license';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command convert data';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $licenses = DB::table('qldn_orders_pro')->where('province', '24')->get();
        foreach ($licenses  as $li) {
            $license = json_decode($li->license, true);
            $data_license = $license[0];
            $school = School::where('map_qa', $data_license['unit_id'])->first();
            isset($school) && $data_client = Client::where('school_id', $school['school_id'])->first();
            if ($school) {
                if (!$data_client) {
                    $client = Client::create([
                        'school_id' => $school->school_id,
                    ]);
                    $client_id = $client->id;
                } else {
                    $client_id = $data_client->id;
                }
                $user = User::where('email', $li->sale_email)->first();
                $user_working = UserWorking::active()->sale()->where('user_id', $user->id)->first();

                if ($user_working) {
                    if ($user_working->dependent_position_id) {
                        $leader_id = $user_working->dependent_position_id;
                    } else {
                        $leader_id = $user_working->position_id;
                    }
                }
                $unequal_price = $li->price - $li->price_real;
                $debt_price = $li->price_real - $li->payed;
                $contract = [
                    'client_id' => $client_id,
                    'product_id' => $data_license['project_id'],
                    'contract_signing_date' => $li->order_start ? $li->order_start : null,
                    'contract_expire_date' => $li->order_stop ? $li->order_stop : null,
                    'contract_value' => $li->price,
                    'payment_date' => $li->payed_at ? $li->payed_at : null,
                    'payment_amount' => $li->price_real,
                    'received_money' => $li->payed,
                    'unequal' => $unequal_price,
                    'debt' => $debt_price,
                    'user_name_product' => $data_license['username'],
                    'sale_id' => $user ? $user->id : null,
                    'leader_id' => $leader_id ? $leader_id : null,
                ];
                Contract::create($contract);
                $md5_orders_pro = md5($li->order_id . $li->type . $li->activated_at . $li->deleted);
                DB::table('qldn_orders_pro')->whereRaw("md5(concat(order_id,type,activated_at,deleted)) = '$md5_orders_pro'")->update(['convert_status' => 1]);
            }
        }
    }
}
