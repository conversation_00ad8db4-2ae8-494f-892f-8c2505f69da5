<?php

namespace App\Console\Commands;


use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use App\Models\SystemConfig;
use App\Jobs\SendEmailInfoRegisterKPI;

class InformationAssessmentKPI extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'information_assessment_KPI';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Information Assessment KPI';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try {
            $config = SystemConfig::where('type', SystemConfig::INFORMATION_COMPANY)->first();

            $receiver = explode(',', $config->content);
            if (count($receiver) == 0) {
                echo "khong ton tai nguoi nhan! \n";
                die();
            }

            $first_element = array_shift($receiver);
            $current_month = Carbon::now()->format('m');
            $last_current_month = Carbon::now()->endOfMonth()->format('d-m-Y');
            $content = (object) [];
            $content->receiver = env('APP_ENV') == 'production' ? $first_element : '<EMAIL>';
            $content->subject = '[VIETEC-QLDN]-THÔNG BÁO ĐÁNH GIÁ KPI THÁNG ' . $current_month;
            $content->message = 'Anh/chị vui lòng vào hệ thống QLDN để đánh giá KPI tháng ' . $current_month . '. Hạn đánh giá KPI là ngày: ' . $last_current_month . '. Trường hợp anh/chị không đánh giá đúng hạn thì hệ thống mặc định quy về xếp hạng C';
            $content->link = 'https://internal.vietec.com.vn/assessment-list-kpi';
            $content->cc = env('APP_ENV') == 'production' ? $receiver : ['<EMAIL>'];

            $emailJob = (new SendEmailInfoRegisterKPI($content))->onQueue('email');
            dispatch($emailJob);

            echo "done nhe! \n";
        } catch (\Exception $e) {
            Log::error('Information Assessment KPI Job: ' . $e->getMessage());
        }
    }
}
