<?php

namespace App\Console\Commands;

use App\Models\Client;
use App\Models\Contract;
use App\Models\School;
use App\Models\SWCallDetailCC;
use App\Models\SWCallDetailCRM;
use App\Models\User;
use App\Models\UserWorking;
use Illuminate\Support\Facades\Log;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class SyncCallCenterCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync-callcenter';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Đồng bộ dữ liệu từ Callcenter';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try {


            DB::transaction(function () {
                $current_date = date('Y-m-d');
                $calls = SWCallDetailCC::date($current_date)->usernameNotNull()->orderBy('CreatedDateTime', 'DESC')->get()->toArray();

                // Xoá dữ liệu cũ
                SWCallDetailCRM::date($current_date)->delete();
                $total = 0;
                foreach ($calls as $call) {
                    // unset($call['Id']);
                    // unset($call['Log']);
                    $new_call = new SWCallDetailCRM();
                    foreach ($call as $field => $value) {
                        $new_call->$field = $value;
                    }
                    $new_call->save();
                    $total++;
                }

                dump("[".$current_date."] Đồng bộ dữ liệu CallCenter --> QLDN: Total = ".$total);
            });
        } catch (\Exception $ex) {
            dd($ex->getMessage());
        }
    }
}
