<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Anik\Amqp\ConsumableMessage;
use Anik\Laravel\Amqp\Facades\Amqp;
use Anik\Amqp\Queues\Queue;
use Anik\Amqp\AmqpConnectionFactory;
use Anik\Amqp\Consumer;
use PhpAmqpLib\Connection\AMQPSSLConnection;
use Anik\Amqp\Exchanges\Exchange;
use Anik\Amqp\Exchanges\Fanout;
use Anik\Amqp\Exchanges\Topic;
use Anik\Amqp\Producer;
use Anik\Amqp\ProducibleMessage;
use PhpAmqpLib\Message\AMQPMessage;
use PhpAmqpLib\Exception\AMQPRuntimeException;
use App\Models\User;
use App\Notifications\UserNotification;
use App\Traits\ConsumableMessageChannelTrait;

class ConsumeNotification extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'consume2 {system}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    use ConsumableMessageChannelTrait;
    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->consumeListener($this->argument('system'), 'thong_bao', 'ack');
    }

    /**
     * Xử lý Message
     */
    private function processMessageBody($body)
    {
        //throw new \Exception('Test');
        $row = json_decode($body, true);

        if (isset($row['user_id'])) {
            $user = User::where('id', $row['user_id'])->first();
            if ($user != null) {
                //dd($row);
                $user->notify((new UserNotification($user, $row))->onQueue(env('QUEUE_NAME_PREFIX') . '_user_notification'));
            }
        }
    }
}
