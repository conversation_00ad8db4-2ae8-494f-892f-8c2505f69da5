<?php

namespace App\Console\Commands;


use App\Models\UserWorking;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use App\Models\SystemConfig;
use App\Jobs\SendEmailHappyBirthday;
use App\Models\ConfigEmail;

class HappyBirthday extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'happy_birthday';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Happy Birthday';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try {
            $config = SystemConfig::where('type', SystemConfig::INFORMATION_COMPANY)->first();

            $receiver = explode(',', $config->content);
            if (count($receiver) == 0) {
                echo "khong ton tai nguoi nhan! \n";
                die();
            }
            $first_element = array_shift($receiver);
            $birthday_persons = UserWorking::active()->whereHas('user', function ($query) {
                $query->whereMonth('birth_of_date', Carbon::now()->format('m'));
            })->with('user', 'department', 'position')->get();
            $current_month = Carbon::now()->format('m');
            $config_content = ConfigEmail::first();
            $content = (object) [];
            $content->receiver = env('APP_ENV') == 'production' ? $first_element : '<EMAIL>';
            $content->subject = '[VIETEC-QLDN]-CHÚC MỪNG SINH NHẬT THÁNG ' . $current_month;
            $content->month = $current_month;
            $content->message = $config_content->content;
            $content->image = $config_content->path;
            $content->birthday_persons = $birthday_persons;
            $content->cc = env('APP_ENV') == 'production' ? $receiver : ['<EMAIL>'];
            $emailJob = (new SendEmailHappyBirthday($content))->onQueue('email');
            dispatch($emailJob);

            echo "done nhe! \n";
        } catch (\Exception $e) {
            Log::error('Happy Birthday Job: ' . $e->getMessage());
        }
    }
}
