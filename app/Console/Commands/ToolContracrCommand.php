<?php

namespace App\Console\Commands;

use App\Models\ProvinceBusinessMarket;
use App\Models\DistrictBusinessMarket;
use App\Models\TmpContract;
use App\Models\Client;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class ToolContracrCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'tool-contract-create';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'tool_contract_create';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try {

            DB::beginTransaction();

            // 5000228412
            // 5000228726
            // 5000228927
            // 5000229166
            // 5000228500
            // 5000228902
            // 5000229409
            // 5000228765
            // 5000229208

            $code = 'CH.TQ.CSHUNGMY';
            $tmpContract = TmpContract::where('client_code', $code)->first();
            // dd($tmpContract);
            if ($tmpContract) {
                $newClient = new Client();
                $newClient->province_id = $tmpContract->province_id;
                $newClient->district_id = $tmpContract->district_id;
                $newClient->code = $tmpContract->client_code;
                $newClient->head_master_name = $tmpContract->head_master_name;
                $newClient->position = $tmpContract->position;
                $newClient->head_master_phone = $tmpContract->head_master_phone;
                $newClient->accountant_name = $tmpContract->accountant_name;
                $newClient->accountant_phone = $tmpContract->accountant_phone;
                $newClient->contact_name = $tmpContract->contact_name;
                $newClient->contact_phone = $tmpContract->contact_phone;
                $newClient->name = $tmpContract->client_name;
                $newClient->name_upper = strtoupper($tmpContract->client_name);
                $newClient->tax_code = $tmpContract->tax_code;
                $newClient->address = $tmpContract->address;
                $newClient->save();
            }
            // \var_dump($newClient);
            echo "done nhe! \n";
            DB::commit();
        } catch (\Exception $e) {
            DB::rollback();
            Log::error('ToolContracrCommand Job: ' . $e->getMessage());
        }
    }
}
