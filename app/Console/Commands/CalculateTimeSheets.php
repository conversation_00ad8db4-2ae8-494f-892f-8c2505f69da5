<?php

namespace App\Console\Commands;

use App\Models\Holiday;
use App\Models\UserWorking;
use App\Models\TimeSheet;
use App\Models\AbsenceLetter;
use App\Models\BusinessPlan;
use App\Models\BusinessReport;
use App\Models\InOutsOnline;
use App\Models\BusinessTrip;
use Illuminate\Console\Command;
use App\Providers\UtilityServiceProvider as u;
use Illuminate\Support\Facades\Log;

class CalculateTimeSheets extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'calculate-time-sheets {option}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command calculate time sheets';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

        # calculate-time-sheets now => tính toán công trong 1 ngày: thời điểm call job
        # calculate-time-sheets all => tính toán công cho full tháng: tháng hiện tại lúc call job
        # calculate-time-sheets ago => tính toán công cho full tháng trước đó: trước 1 tháng so với tháng hiện tại lúc call job

        try {

            # bat dau tinh toan
            echo "Bat dau: " . time() . "\n";

            $option = $this->argument('option');

            if ($option == 'all') {
                // tính full tháng hiện tại
                $arrDate = [];
                for ($i = 1; $i <= 31; $i++) {
                    if (checkdate((date('m') + 0), $i, date('Y'))) {
                        $arrDate[] = $i < 10 ? date('Y-m') . '-0' . $i : date('Y-m') . '-' . $i;
                    }
                }
                $year = date('Y');
                $month = date('m');
                $currentDateTime = date('Y-m');
            } else if ($option == 'ago') {
                // tính full tháng trước
                $arrDate = [];
                $monthAgo = date('Y-m-d', strtotime('-1 month', strtotime(date("Y-m-05"))));
                $month = date_format(date_create($monthAgo), 'm');
                $year = date_format(date_create($monthAgo), 'Y');
                $currentDateTime = date_format(date_create($monthAgo), 'Y-m');
                for ($i = 1; $i <= 31; $i++) {
                    if (checkdate(($month + 0), $i, $year)) {
                        $arrDate[] = $i < 10 ? $currentDateTime . '-0' . $i : $currentDateTime . '-' . $i;
                    }
                }
            } else {
                // tính cho đầu tháng đến lúc call job
                // $arrDate = [date('Y-m-d')];
                $arrDate = [];
                $dateInfo = getdate();
                for ($i = 1; $i <= $dateInfo['mday']; $i++) {
                    if (checkdate((date('m') + 0), $i, date('Y'))) {
                        $arrDate[] = $i < 10 ? date('Y-m') . '-0' . $i : date('Y-m') . '-' . $i;
                    }
                }
                $year = date('Y');
                $month = date('m');
                $currentDateTime = date('Y-m');
            }

            # lấy danh sách nghỉ lễ trong danh mục
            $arrayHolidays = self::getPublicHolidays($currentDateTime);

            # lấy các bản ghi nghỉ phép có ngày bắt đầu hoặc kết thức trong tháng
            $absenceLetters = self::getAbsenceLetters($currentDateTime);

            # list danh sách nhân sự đang active hiện tại
            $userLists = UserWorking::notAdmin()->active()->with('user')->groupBy('user_id')->get();
            foreach ($userLists as $user) {
                $checkIsset = TimeSheet::where('year', $year)->where('month', $month)->where('user_id', $user->user_id)->count();
                if ($checkIsset == 0) {
                    $model = new TimeSheet();
                    $model->user_id = $user->user_id;
                    $model->department_id = $user->department_id;
                    $model->position_id = $user->position_id;
                    $model->dependent_position_id = $user->dependent_position_id;
                    $model->employment_contract_id = $user->employment_contract_id;
                    $model->month = $month;
                    $model->year = $year;
                    $model->save();
                } else {
                    if ($option == 'now' || $option == 'all') {
                        $model = TimeSheet::where('year', $year)->where('month', $month)->where('user_id', $user->user_id)->first();
                        if ($model) {
                            $model->department_id = $user->department_id;
                            $model->position_id = $user->position_id;
                            $model->dependent_position_id = $user->dependent_position_id;
                            $model->employment_contract_id = $user->employment_contract_id;
                            $model->save();
                        }
                    }
                }
            }

            # danh sách nhân sự có trong bảng chấm công tháng hiện tại
            $userListInTimeSheet = TimeSheet::where('year', $year)->where('month', $month)->with('user:id,staff_code,full_time_sheet')->get();

            foreach ($userListInTimeSheet as $item) {

                if (!$item->user && !$item->user->id) {
                    continue;
                }

                $id = $item->id;
                $staffCode = $item->user->staff_code;
                $userId = $item->user->id;
                $userFullTimeSheet = $item->user->full_time_sheet ?? 0;
                $absenceLetterUser = $absenceLetters[$userId] ?? [];

                foreach ($arrDate as $date) {

                    # lấy số thứ tự của ngày trong tuần để bỏ qua thứ 7 và chủ nhật
                    $dateName = date_format(date_create($date), 'N');
                    if ($dateName == 6 || $dateName == 7) {
                        continue;
                    }

                    # bắt đầu tính công cho thứ 2 đến thứ 6
                    $sql = "SELECT MIN(`time`) start_time, MAX(`time`) end_time, aliasID, personID, `date` FROM in_outs WHERE `date` like '$date%' AND aliasID = '$staffCode' GROUP BY aliasID";
                    $datas = u::query($sql);
                    $workDaySymbol = 3; // ban đầu là ko lương hết
                    $fieldName = 'day_' . date_format(date_create($date), 'd');

                    $oldTimeSheetValue = $item->$fieldName; // giá trị cũ ngày này

                    if (!empty($datas)) {

                        # các trường hợp trong này là có bản ghi trong máy chấm công
                        $checkIn = $datas[0]->start_time;
                        $checkOut = $datas[0]->end_time;

                        $minStartCheckIn = strtotime("{$date} 00:00:00") * 1000;
                        $maxStartCheckIn = strtotime("{$date} 08:59:59") * 1000;
                        $minStartCheckOut = strtotime("{$date} 16:00:00") * 1000;
                        $maxStartCheckOut = strtotime("{$date} 23:59:59") * 1000;

                        // # nếu checkIn sớm hơn 06:00 thì gán lại giờ checkIn để tính công là từ 06:00
                        // if ($checkIn < $minStartCheckIn) {
                        //     $checkIn = $minStartCheckIn;
                        // }
                        // # nếu checkOut sau 21h thì gán lại giờ checkOut để tính công là từ  21:00
                        // if ($checkOut > $maxStartCheckOut) {
                        //     $checkOut = $maxStartCheckOut;
                        // }

                        if ($checkIn >= $minStartCheckIn && $checkOut <= $maxStartCheckOut) {
                            # giờ checkIn và checkOut nằm từ 00:00 => 23:59
                            if ($checkIn <= $maxStartCheckIn && $checkOut >= $minStartCheckOut) {
                                # đi làm cả ngày
                                $workDaySymbol = 1;

                                # xác định thử việc và học việc
                                if ($item->employment_contract_id == 1) {
                                    # Thử việc
                                    $workDaySymbol = 11;
                                } else if ($item->employment_contract_id == 2) {
                                    # Học việc
                                    $workDaySymbol = 10;
                                } else if ($item->employment_contract_id == 6) {
                                    # Thực tập sinh
                                    $workDaySymbol = 16;
                                }
                            } else if (($checkOut - $checkIn) >= (4 * 60 * 60 * 1000)) {
                                # điều kiện này là khoảng thời gian nữa in và out là lớn hơn hoặc bằng 4  giờ, tính theo mili giây
                                # đi làm nữa ngày
                                $workDaySymbol = 6;
                                # xác định thử việc, học việc và thực tập sinh đi làm nữa ngày
                                if ($item->employment_contract_id == 1) {
                                    # Thử việc/2
                                    $workDaySymbol = 15;
                                } else if ($item->employment_contract_id == 2) {
                                    # Học việc/2
                                    $workDaySymbol = 14;
                                } else if ($item->employment_contract_id == 6) {
                                    # Thực tập sinh/2
                                    $workDaySymbol = 17;
                                }
                            }
                        }
                    }

                    # các vị trí full công, cấu hình trong bảng user
                    if ($userFullTimeSheet) {
                        $workDaySymbol = 1;
                        if ($item->employment_contract_id == 1) {
                            # Thử việc
                            $workDaySymbol = 11;
                        } else if ($item->employment_contract_id == 2) {
                            # Học việc
                            $workDaySymbol = 10;
                        } else if ($item->employment_contract_id == 6) {
                            # Thực tập sinh
                            $workDaySymbol = 16;
                        }
                    }

                    # làm việc online
                    $online100 = InOutsOnline::where('user_id', $userId)->where('status', 2)->whereBetween('checkin', [$date . " 00:00:00", $date . " 08:59:59"])->whereBetween('checkout', [$date . " 16:00:00", $date . " 23:59:59"])->count();
                    if ($online100 > 0) {
                        $workDaySymbol = 7; // Làm việc online hưởng 100%
                    } else {

                        # xét các trường hợp 50% online

                        $onlineMorningShift = InOutsOnline::where('user_id', $userId)->where('status', 2)->whereBetween('checkin', [$date . " 00:00:00", $date . " 08:59:59"])->whereBetween('checkout', [$date . " 12:00:00", $date . " 16:00:00"])->count();
                        $onlineAfternoonShift = InOutsOnline::where('user_id', $userId)->where('status', 2)->whereBetween('checkin', [$date . " 12:00:00", $date . " 13:00:00"])->whereBetween('checkout', [$date . " 16:00:00", $date . " 23:59:59"])->count();

                        if ($onlineMorningShift > 0 || $onlineAfternoonShift > 0) {

                            if (!empty($datas)) {
                                # online 50% và có đi làm 50% còn lại
                                if (in_array($workDaySymbol, [6, 14, 15, 17])) {
                                    $workDaySymbol = 18;
                                    # xác định thử việc và học việc
                                    if ($item->employment_contract_id == 1) {
                                        # Thử việc
                                        $workDaySymbol = 19;
                                    } else if ($item->employment_contract_id == 2) {
                                        # Học việc
                                        $workDaySymbol = 20;
                                    } else if ($item->employment_contract_id == 6) {
                                        # Thực tập sinh
                                        $workDaySymbol = 21;
                                    }
                                }
                            } else {
                                # chỉ online 50% nũa ngày còn lại ko đi làm
                                $workDaySymbol = 8; // Online hưởng 50% lương
                            }
                        }
                    }

                    # đăng ký công tác
                    // $businessTrip = BusinessTrip::onlyMe($userId)->betweenDate($date)->where('status', 2)->count();
                    // if ($businessTrip > 0) {
                    //     $workDaySymbol = 9; // Đi công tác
                    // }

                    $businessTrip = BusinessReport::betweenDate($date)->where('status', BusinessPlan::REPORT_APPROVED)->get();

                    foreach ($businessTrip as $e) {
                        foreach ($e->staffs as $i) {
                            if ($i['id'] == $userId) {
                                $workDaySymbol = 9;
                            }
                        }
                    }

                    # Xét các trường hợp có đơn xin nghỉ phép
                    # phần đơn xin nghỉ phép sẽ là ưu tiên cao hơn so với máy chấm công và chỉ nhân sự chính thức mới có phép
                    if (!empty($absenceLetterUser)) {

                        foreach ($absenceLetterUser as $letter) {

                            if (strtotime($date) < strtotime($letter['from_date']) || strtotime($date) > strtotime($letter['to_date'])) {
                                continue;
                            }

                            // from_time: 1-8h, 2-12h
                            // to_time: 1-12h, 2-17h
                            if ($letter['absence_letter_type_id'] == 1) {
                                # absence_letter_type_id = 1 => nghỉ phép năm
                                if ($letter['from_time'] == 1) {
                                    if ($letter['to_time'] == 2) {
                                        // 1- 2
                                        if (strtotime($date) >= strtotime($letter['from_date']) && strtotime($date) <= strtotime($letter['to_date'])) {
                                            $workDaySymbol = 2; // Nghỉ phép
                                        }
                                    } else if ($letter['to_time'] == 1) {
                                        // 1 - 1
                                        if (strtotime($date) >= strtotime($letter['from_date']) && strtotime($date) < strtotime($letter['to_date'])) {
                                            $workDaySymbol = 2; // Nghỉ phép
                                        } else if (strtotime($date) == strtotime($letter['to_date'])) {
                                            # đoạn if này là kiểm tra xem cùng nữa ngày nghỉ phép này có chấm công nữa ngày còn lại hay ko
                                            if ($workDaySymbol == 1 || $workDaySymbol == 6) {
                                                $workDaySymbol = 4; // Nghỉ 1/2 ngày có phép
                                            } else {
                                                $workDaySymbol = 5; // Nghỉ 1/2 ngày có phép, nửa ngày còn lại nghỉ không lương
                                            }
                                            // xin nghỉ sáng, online chiều 
                                            if ($onlineAfternoonShift > 0 || $online100 > 0) {
                                                $workDaySymbol = 22;
                                            }
                                        }
                                    }
                                } else {
                                    if ($letter['to_time'] == 2) {
                                        // 2 - 2
                                        if (strtotime($date) == strtotime($letter['from_date'])) {
                                            # trường hợp này là ngày đầu tiên nghĩ nữa ngày, các ngày còn lại full ngày

                                            # đoạn if này là kiểm tra xem cùng nữa ngày nghỉ phép này có chấm công nữa ngày còn lại hay ko
                                            if ($workDaySymbol == 1 || $workDaySymbol == 6) {
                                                $workDaySymbol = 4; // Nghỉ 1/2 ngày có phép
                                            } else {
                                                $workDaySymbol = 5; // Nghỉ 1/2 ngày có phép, nửa ngày còn lại nghỉ không lương
                                            }
                                            // xin nghỉ chiều, online sáng
                                            if ($onlineMorningShift > 0 || $online100 > 0) {
                                                $workDaySymbol = 22;
                                            }
                                        } else if (strtotime($date) > strtotime($letter['from_date']) && strtotime($date) <= strtotime($letter['to_date'])) {
                                            $workDaySymbol = 2; // Nghỉ phép
                                        }
                                    } else if ($letter['to_time'] == 1) {
                                        // 2 - 1
                                        if (strtotime($date) == strtotime($letter['from_date']) || strtotime($date) == strtotime($letter['to_date'])) {
                                            # trường hợp này là ngày đầu tiên nghĩ nữa ngày, các ngày còn lại full ngày

                                            # đoạn if này là kiểm tra xem cùng nữa ngày nghỉ phép này có chấm công nữa ngày còn lại hay ko
                                            if ($workDaySymbol == 1 || $workDaySymbol == 6) {
                                                $workDaySymbol = 4; // Nghỉ 1/2 ngày có phép
                                            } else {
                                                $workDaySymbol = 5; // Nghỉ 1/2 ngày có phép, nửa ngày còn lại nghỉ không lương
                                            }
                                        } else if (strtotime($date) > strtotime($letter['from_date']) && strtotime($date) < strtotime($letter['to_date'])) {
                                            $workDaySymbol = 2; // Nghỉ phép
                                        }
                                    }
                                }
                            } else if (in_array($letter['absence_letter_type_id'], [2, 3, 4, 5, 6]) && strtotime($date) >= strtotime($letter['from_date']) && strtotime($date) <= strtotime($letter['to_date'])) {
                                $workDaySymbol = 2;
                            } else if (in_array($letter['absence_letter_type_id'], [8]) && strtotime($date) >= strtotime($letter['from_date']) && strtotime($date) <= strtotime($letter['to_date'])) {
                                $workDaySymbol = 24;
                            } else if (in_array($letter['absence_letter_type_id'], [9, 10, 11, 12, 13]) && strtotime($date) >= strtotime($letter['from_date']) && strtotime($date) <= strtotime($letter['to_date'])) {
                                $workDaySymbol = 23;
                            } else if ($letter['absence_letter_type_id'] == 7) {
                                # nghỉ ko lương cả ngày
                                if ($letter['from_time'] == 1 && $letter['to_time'] == 2) {
                                    $workDaySymbol = 3;
                                }
                            }
                        }
                    }

                    # phần này là check ngày nghỉ lễ theo public holiday
                    if (in_array($date, $arrayHolidays)) {
                        $workDaySymbol = 12; // Nghỉ lễ
                    }

                    # lưu lại, nếu giá trị đang tính khác giá trị cũ
                    if ($oldTimeSheetValue != $workDaySymbol) {
                        $timeSheet = TimeSheet::find($id);
                        $timeSheet->$fieldName = $workDaySymbol;
                        $timeSheet->save();
                    }
                }

                # bắt đầu tính toán luôn các tổng hợp các loại công
                self::calcuTotaltimeSheet($id, $arrDate);
            }


            echo "done tinh cong CamAI! \n";
            # ket thuc tinh toan
            echo "Ket thuc: " . time() . "\n";
        } catch (\Exception $e) {
            Log::error('CalculateTimeSheets: ' . $e->getMessage());
        }
    }

    public static function getPublicHolidays($month)
    {
        $arrayHolidays = [];
        $month = $month ?: date('Y-m');

        $holidays = Holiday::select('date')
            ->where("date", "LIKE", $month . "%")
            ->get()
            ->toArray();

        foreach ($holidays as $item) {
            $arrayHolidays[] = $item['date'];
        }

        return $arrayHolidays;
    }

    public static function getAbsenceLetters($month)
    {
        $month = $month ?: date('Y-m');

        $absenceLetters = AbsenceLetter::approve()
            ->where(function ($subQuery) use ($month) {
                $subQuery->where('from_date', 'LIKE', $month . '%')
                    ->orWhere('to_date', 'LIKE', $month . '%');
            })
            ->get()
            ->toArray();
        $absenceLetters = vietec_convert_array_to_map($absenceLetters, 'user_id', true);

        return $absenceLetters;
    }

    public static function calcuTotaltimeSheet($timeSheetId, $arrDate)
    {
        $timeSheet = TimeSheet::find($timeSheetId);
        $total_study_work = 0; // học việc
        $total_probationary = 0; // thử việc
        $total_real = 0;
        $total_online1 = 0;
        $total_online2 = 0;
        $total_online_probationary = 0;
        $total_bussiness = 0;
        $total_bussiness_probationary = 0;
        $total_holiday_absence = 0;
        $total_salary = 0;
        $total_no_salary = 0;
        $total_lunch = 0;
        $union_fund = 0;
        $total_intern = 0; // thực tập sinh
        $total_social_insurance = 0; // số ngày nghỉ bảo hiểm xã hội

        foreach ($arrDate as $date) {
            # lấy số thứ tự của ngày trong tuần để bỏ qua thứ 7 và chủ nhật
            $dateName = date_format(date_create($date), 'N');
            if ($dateName != 6 && $dateName != 7) {
                $fieldName = 'day_' . date_format(date_create($date), 'd');
                if ($timeSheet->$fieldName == 10) {
                    $total_study_work++;
                } else if ($timeSheet->$fieldName == 11) {
                    $total_probationary++;
                    $total_lunch++;
                } else if ($timeSheet->$fieldName == 1) {
                    $total_real++;
                    $total_lunch++;
                } else if ($timeSheet->$fieldName == 6) {
                    $total_real += 0.5;
                    $total_no_salary += 0.5;
                } else if ($timeSheet->$fieldName == 5) {
                    $total_no_salary += 0.5;
                    $total_holiday_absence += 0.5;
                } else if ($timeSheet->$fieldName == 4) {
                    $total_real += 0.5;
                    $total_holiday_absence += 0.5;
                    $total_lunch++;
                } else if ($timeSheet->$fieldName == 7) {
                    $total_online1++;
                    $total_lunch++;
                } else if ($timeSheet->$fieldName == 8) {
                    $total_online2++;
                } else if ($timeSheet->$fieldName == 9) {
                    $total_bussiness++;
                } else if ($timeSheet->$fieldName == 2 || $timeSheet->$fieldName == 12) {
                    $total_holiday_absence++;
                } else if ($timeSheet->$fieldName == 3) {
                    $total_no_salary++;
                } else if ($timeSheet->$fieldName == 14) {
                    $total_study_work += 0.5;
                } else if ($timeSheet->$fieldName == 15) {
                    $total_probationary += 0.5;
                    $total_lunch++;
                } else if ($timeSheet->$fieldName == 16) {
                    $total_intern++;
                } else if ($timeSheet->$fieldName == 17) {
                    $total_intern += 0.5;
                } else if ($timeSheet->$fieldName == 23 || $timeSheet->$fieldName == 24) {
                    $total_social_insurance++;
                } else if ($timeSheet->$fieldName == 18) {
                    $total_online2++;
                    $total_real += 0.5;
                    $total_lunch++;
                } else if ($timeSheet->$fieldName == 22) {
                    $total_online2++;
                    $total_holiday_absence += 0.5;
                    $total_lunch++;
                }
            }
        }

        $total_salary = $total_intern + $total_study_work + $total_probationary + $total_real + $total_online1 + ($total_online2 / 2) + $total_bussiness + $total_holiday_absence;
        $timeSheet->total_intern = $total_intern;
        $timeSheet->total_study_work = $total_study_work;
        $timeSheet->total_probationary = $total_probationary;
        $timeSheet->total_real = $total_real;
        $timeSheet->total_online1 = $total_online1;
        $timeSheet->total_online2 = $total_online2;
        $timeSheet->total_online_probationary = $total_online_probationary;
        $timeSheet->total_bussiness = $total_bussiness;
        $timeSheet->total_bussiness_probationary = $total_bussiness_probationary;
        $timeSheet->total_holiday_absence = $total_holiday_absence;
        $timeSheet->total_salary = $total_salary;
        $timeSheet->total_no_salary = $total_no_salary;
        $timeSheet->total_lunch = $total_lunch;
        $timeSheet->union_fund = $union_fund;
        $timeSheet->total_social_insurance = $total_social_insurance;
        $timeSheet->save();

        return true;
    }
}
