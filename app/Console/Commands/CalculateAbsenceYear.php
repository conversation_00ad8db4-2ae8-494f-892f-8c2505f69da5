<?php

namespace App\Console\Commands;

use App\Models\AbsenceYear;
use App\Models\TmpAbsenceYear;
use App\Models\UserWorking;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CalculateAbsenceYear extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'calculate_absence_year';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Calculate Absence Year';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try {

            DB::beginTransaction();

            $monthFull = date('Y-m');

            # danh sách nhân sự chính thức đang active hiện tại
            $officialUsers = UserWorking::active()->notAdmin()->Official()->pluck('user_id')->toArray();

            foreach ($officialUsers as $userId) {

                # theo yêu cầu của phòng HCNS, job này chạy đầu tháng, cứ mùng 1 hàng tháng là cộng phép cho các nhân sự chính thức thời điểm đó

                # lấy bản ghi phép hiện tại
                $currentAbsenceYear = AbsenceYear::where('user_id', $userId)->first();
                # check trạng thái cộng phép trong tháng
                $checkAddInMonth = TmpAbsenceYear::where('user_id', $userId)->where('month', $monthFull)->where('status_add_in_month', 1)->first();

                if ($currentAbsenceYear && !$checkAddInMonth) {

                    $note = "[QLDN] Tự động cộng thêm phép trong tháng đối với các nhân viên chính thức tại thời điểm tính";
                    AbsenceYear::updateAbsenceYear($userId, ($currentAbsenceYear->absence_year_val + 1), $note); // hàm update này đã có log trong rồi

                    $model = new TmpAbsenceYear();
                    $model->user_id = $userId;
                    $model->status_add_in_month = 1;
                    $model->month = $monthFull;
                    $model->save();
                }
            }

            echo "done nhe! \n";
            DB::commit();
        } catch (\Exception $e) {
            DB::rollback();
            Log::error('CalculateAbsenceYear Job: ' . $e->getMessage());
        }
    }
}
