<?php

namespace App\Console\Commands;

use App\Models\AbsenceYear;
use App\Models\TmpAbsenceYear;
use App\Models\UserWorking;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CalculateResetAbsenceYear extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'calculate_reset_absence_year';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Calculate Reset Absence Year';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {   
        $monthFull = date('m');
        if ($monthFull != '04') {
            echo "jobs nay chi thuc hien trong thang 4 \n";
            exit();  
        }
    
        DB::beginTransaction();
        try {
            # danh sách nhân sự chính thức đang active hiện tại
            $officialUsers = UserWorking::active()->notAdmin()->Official()->pluck('user_id')->toArray();

            foreach ($officialUsers as $userId) {

                #  lấy ngày phép hiện tại
                $currentAbsenceYear = AbsenceYear::where('user_id', $userId)->first();
                if (($currentAbsenceYear->absence_year_val + 0) > 4) {
                    echo ($currentAbsenceYear->absence_year_val + 0) . " => 4" . "\n";
                    $note = "[QLDN] Tự động reset phép năm cũ!";
                    AbsenceYear::updateAbsenceYear($userId, 4, $note);
                } else {
                    echo ($currentAbsenceYear->absence_year_val + 0) . "\n";
                }
            }

            echo "done nhe! \n";
            DB::commit();
        } catch (\Exception $e) {
            DB::rollback();
            Log::error('CalculateAbsenceYear Job: ' . $e->getMessage());
        }
    }
}
