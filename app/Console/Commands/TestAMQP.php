<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Anik\Amqp\ConsumableMessage;
use Anik\Laravel\Amqp\Facades\Amqp;
use Anik\Amqp\Queues\Queue;
use Anik\Amqp\AmqpConnectionFactory;
use PhpAmqpLib\Connection\AMQPSSLConnection;
use Anik\Amqp\Exchanges\Exchange;
use Anik\Amqp\Exchanges\Fanout;
use Anik\Amqp\Exchanges\Topic;
use Anik\Amqp\Producer;
use Anik\Amqp\ProducibleMessage;
use PhpAmqpLib\Message\AMQPMessage;

class TestAMQP extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'publish {system}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $message = [
            'name' => '<PERSON><PERSON><PERSON>'
        ];
        try {
            //Amqp::connection($this->argument('system'))->publish(json_encode($message));
            $connection_name = $this->argument('system');
            $connection_config = config('amqp')['connections'][$connection_name]['connection'];

            $connection = AmqpConnectionFactory::makeFromArray($connection_config['hosts'], [], $connection_config['class']);

            $exchange_config = config('amqp')['connections'][$connection_name]['exchange'];
            $queue_config = config('amqp')['connections'][$connection_name]['queue'];
            $channel = $connection->channel();

            $channel->exchange_declare(
                $exchange_config['name'],
                $exchange_config['type'],
                $exchange_config['passive'],
                $exchange_config['durable'],
                $exchange_config['auto_delete'],
                $exchange_config['internal'],
                $exchange_config['no_wait'],
                $exchange_config['arguments'],
                $exchange_config['ticket']
            );

            $queue_name = env('AMQP_QUEUE_NAME_PREFIX_VIETEC') . 'thong_bao';

            $channel->queue_declare(
                $queue_name,
                $queue_config['passive'],
                $queue_config['durable'],
                $queue_config['exclusive'],
                $queue_config['auto_delete'],
                $queue_config['no_wait'],
                $queue_config['arguments'],
                $queue_config['ticket']
            );

            

            $channel->queue_bind($queue_name, $exchange_config['name']);

            $message = [
                'time' => date('Y-m-d H:i:s'),
                'user_id' => 4,
                'title' => 'Dev đang thử thông báo 123',
                'body' => 'Nội dung thông báo 123',
                'image' => 'https://kidsenglish.vn/frontend/assets/img/logo.svg',
                'badge' => strval(1),
                'data' => [
                    'push_type' => strval(1),
                    'badge' => strval(1),
                    'id' => '',
                ]
            ];

            $channel->basic_publish(new AMQPMessage(json_encode($message)), $exchange_config['name'], '');

            // $exchange = Exchange::make(
            //     [
            //         'name' => $exchange_config['name'],
            //         'type' => $exchange_config['type'],
            //         'passive' => $exchange_config['passive'],
            //         'durable' => $exchange_config['durable'],
            //     ]
            // );

            //Amqp::connection($this->argument('system'))->publish(json_encode($message), '', $exchange);


            // $msg = new AMQPMessage([
            //     'name' => 'Nguyen Anh Tuan'
            // ]);

            // $channel->basic_publish($msg, $exchange_config['name']);
            // $exchange = Exchange::make(
            //     [
            //         'name' => 'tuanna',
            //         'type' => Exchange::TYPE_DIRECT,
            //         'declare' => true,
            //         'durable' => true
            //     ]
            // );


            // $queue = Queue::make([
            //     'name' => 'tuanna.tuanna123',
            //     'declare' => true,
            //     'durable' => true
            // ]);
            //$connection->channel()->exchange_declare('tuanna123', 'direct', false, true);
            // sleep(3);
            //$connection->channel()->queue_declare('tuanna123', false, true);

            //$connection->channel()->queue_bind('tuanna123', 'tuanna123');

            // $msg = new ProducibleMessage(json_encode($message));

            // (new Producer($connection))->publishBasic($msg, '', $exchange, []);
        } catch (\Exception $ex) {
            dd($ex);
        }
    }
}
