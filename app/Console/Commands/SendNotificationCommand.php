<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Notifications\UserNotification;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use App\Services\FirebaseNotification;
use Kreait\Firebase\Factory;
use Kreait\Firebase\Exception\MessagingException;
use Kreait\Firebase\Exception\FirebaseException;

class SendNotificationCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'send-notification {user_id}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send notification';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $user = User::where('id', $this->argument('user_id'))->first();

        $message = [
            'title' => 'Thông báo',
            'body' => 'Đ<PERSON> nghị cấp tài kho<PERSON>n <PERSON>',
            'image' => 'https://cdn-icons-png.freepik.com/512/3242/3242120.png',
            'badge' => 1,
            'data' => [
                'push_type' => strval(FirebaseNotification::PUSH_TYPE_NOTIFICATION),
                'badge' => strval(1),
                'id' => '',
            ]
        ];

        $user->notify(new UserNotification($user, $message));
        dd('Sent');

        $message = [
            'title' => 'Title',
            'body' => 'Body',
            'badge' => 1,
            'data' => [
                'push_type' => FirebaseNotification::PUSH_TYPE_NOTIFICATION,
                'badge' => 1,
                'id' => '',
            ]
        ];

        //FirebaseNotification::getInstance()->sendNotificationToSingleDevice($user->userDevice->first()->firebase_token, $message);

        try {

            //dd(base_path(env('FIREBASE_SERVICE_ACCOUNT_KEY_PATH', '')));
            $factory = (new Factory)->withServiceAccount(base_path(env('FIREBASE_SERVICE_ACCOUNT_KEY_PATH', '')));
            $messaging = $factory->createMessaging();

            $message = [
                'notification' => [
                    'title' => 'Title',
                    'body' =>  'Body',
                ],
                "android" => [
                    "notification" => [
                        "sound" => "default"
                    ]
                ],
                "apns" => [
                    "payload" => [
                        "aps" => [
                            "sound" => "default",
                            "badge" => 1
                        ]
                    ]
                ],
                'data' => [
                    'click_action' => FirebaseNotification::FLUTTER_NOTIFICATION_CLICK,
                    'push_type' => FirebaseNotification::PUSH_TYPE_IN_OUT_EXPLANATION,
                    'badge' => 1,
                    'id' => 123
                ],
                //'token' => $user->userDevice->where('device_id','UKQ1.230804.001')->first()->firebase_token,
            ];

            foreach ($user->userDevice as $device) {
                if (isset($device->firebase_token) && $device->firebase_token != '') {
                    $message['token'] = $device->firebase_token;
                    $rs = $messaging->send($message);
                    dump($rs);
                }
            }
        } catch (\Exception $e) {
            dd($e);
        }
    }
}
