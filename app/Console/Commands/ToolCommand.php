<?php

namespace App\Console\Commands;

use App\Models\ProvinceBusinessMarket;
use App\Models\DistrictBusinessMarket;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use App\Models\User;

class ToolCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'adjust_data_bussiness';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'adjust_data_bussiness';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

        $user = User::where('id', 5)->where('status', 1)->first();
        $notifications = [];
        foreach ($user->unreadNotifications as $k => $notification) {
            if ($k >= 2) {
                break;
            }
            
            $tmpData = $notification['data'];
            unset($tmpData['content']);
            $notification['data'] = $tmpData;

            $notifications[] = $notification;

            dd($notifications);
        }
        dd($notifications);

        try {

            DB::beginTransaction();

            // $rs = ProvinceBusinessMarket::orderBy('code')->get();
            // foreach ($rs as $item) {
            //     ProvinceBusinessMarket::where('code', $item->code)->update([
            //         'province_id' => Str::uuid()->toString(),
            //     ]);

            //     \dump('done: ' . $item->code);
            // }

            // $rs = DistrictBusinessMarket::orderBy('code')->get();
            // foreach ($rs as $item) {
            //     DistrictBusinessMarket::where('code', $item->code)->update([
            //         'district_id' => Str::uuid()->toString(),
            //     ]);

            //     \dump('done: ' . $item->code);
            // }

            echo "done nhe! \n";
            DB::commit();
        } catch (\Exception $e) {
            DB::rollback();
            Log::error('ToolCommand Job: ' . $e->getMessage());
        }
    }
}
