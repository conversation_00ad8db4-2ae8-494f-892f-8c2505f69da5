<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ClearRefreshToken extends  Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'clear_refresh_token';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'clear_refresh_token';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try {
            DB::statement('DELETE FROM device_sessions WHERE refresh_expired_at < ?', [time()]);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
        }
    }
}
